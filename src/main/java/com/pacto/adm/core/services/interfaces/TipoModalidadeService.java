package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.LogDTO;
import com.pacto.adm.core.dto.TipoModalidadeDTO;
import com.pacto.adm.core.dto.filtros.FiltroLogClienteJSON;
import com.pacto.adm.core.dto.filtros.FiltroTipoModalidadeJSON;
import com.pacto.adm.core.entities.contrato.TipoModalidade;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;


public interface TipoModalidadeService {

    TipoModalidade findById(Integer id) throws Exception;

    List<TipoModalidadeDTO> findAll(FiltroTipoModalidadeJSON filtroTipoModalidadeJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    TipoModalidadeDTO saveOrUpdate(TipoModalidadeDTO tipoModalidadeDTO) throws Exception;

    void delete(Integer id) throws Exception;

    List<LogDTO> buscarLogs(FiltroLogClienteJSO<PERSON> filtros, Integer idTipoMod, PaginadorDTO paginadorDTO) throws Exception;

}
