package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.controlecreditotreino.ControleCreditoTreinoAdapter;
import com.pacto.adm.core.dao.interfaces.ControleCreditoTreinoDao;
import com.pacto.adm.core.dto.controlecreditotreino.ControleCreditoTreinoDTO;
import com.pacto.adm.core.dto.filtros.FiltroControleCreditoTreinoJSON;
import com.pacto.adm.core.services.interfaces.ControleCreditoTreinoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Service;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;


@Service
public class ControleCreditoTreinoServiceImpl implements ControleCreditoTreinoService {

    private final ControleCreditoTreinoDao controleCreditoTreinoDao;
    private final ControleCreditoTreinoAdapter controleCreditoTreinoAdapter;

    public ControleCreditoTreinoServiceImpl(
            ControleCreditoTreinoDao controleCreditoTreinoDao, ControleCreditoTreinoAdapter controleCreditoTreinoAdapter
    ) {
        this.controleCreditoTreinoDao = controleCreditoTreinoDao;
        this.controleCreditoTreinoAdapter = controleCreditoTreinoAdapter;
    }

    @Override
    public Integer consultarSaldoCredito(Integer codigoContrato) {
        AtomicReference<Integer> total = new AtomicReference<>(0);
        try (SessionImplementor sessionImplementor = controleCreditoTreinoDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                try {
                    StringBuilder sql = new StringBuilder();
                    sql.append("select sum(quantidade) as total \n");
                    sql.append("from controleCreditoTreino \n");
                    sql.append("where contrato = ").append(codigoContrato);
                    PreparedStatement pst = connection.prepareStatement(sql.toString());
                    ResultSet rs = pst.executeQuery();
                    if (rs.next()) {
                        total.set(rs.getInt("total"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return total.get();
    }

    @Override
    public List<ControleCreditoTreinoDTO> findByCodigoContrato(Integer codigoContrato, PaginadorDTO paginadorDTO, FiltroControleCreditoTreinoJSON filtros) throws ServiceException {
        try {
            List<ControleCreditoTreinoDTO> dtos = controleCreditoTreinoAdapter.toDtos(controleCreditoTreinoDao.findByCodigoContrato(codigoContrato, paginadorDTO, filtros));
            return dtos;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }
}
