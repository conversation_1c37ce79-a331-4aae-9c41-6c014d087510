package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.LogApiAdapter;
import com.pacto.adm.core.dao.interfaces.LogApiRepository;
import com.pacto.adm.core.dto.LogApiDTO;
import com.pacto.adm.core.entities.LogApi;
import com.pacto.adm.core.services.interfaces.LogApiService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

@Service
public class LogApiServiceImpl implements LogApiService {

    private static final int MAXIMO_RESULTADOS = 10;
    private final RequestService requestService;
    private final LogApiRepository logApiRepository;
    private final LogApiAdapter logApiAdapter;

    private Logger logger = Logger.getLogger(LogApiServiceImpl.class.getName());

    public LogApiServiceImpl(RequestService requestService, LogApiRepository logApiRepository, LogApiAdapter logApiAdapter) {
        this.requestService = requestService;
        this.logApiRepository = logApiRepository;
        this.logApiAdapter = logApiAdapter;
    }

    public List<LogApiDTO> all() throws ServiceException {
        try {
            List<LogApiDTO> dtos = new ArrayList<>();
            Page<LogApi> page = logApiRepository.findAll(PageRequest.of(0, Integer.MAX_VALUE));
            List<LogApi> lista = page.getContent();
            lista.forEach(l -> {
                dtos.add(logApiAdapter.toDto(l));
            });
            return dtos;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }
}
