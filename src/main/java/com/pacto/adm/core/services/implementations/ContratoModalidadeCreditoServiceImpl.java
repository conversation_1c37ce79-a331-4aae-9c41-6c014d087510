package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dao.interfaces.ContratoModalidadeCreditoDao;
import com.pacto.adm.core.entities.contrato.ContratoModalidadeCredito;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.services.interfaces.ContratoModalidadeCreditoService;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

@Service
public class ContratoModalidadeCreditoServiceImpl implements ContratoModalidadeCreditoService {
    ContratoModalidadeCreditoDao contratoModalidadeCreditoDao;

    @Override
    public ContratoModalidadeCredito consultarPorContratoModalidade(Integer codModalidade) throws ServiceException {
        List<ContratoModalidadeCredito> contratoModalidadeCredito = new ArrayList<>();

        try (SessionImplementor sessionImplementor = contratoModalidadeCreditoDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                String sql = "SELECT * FROM ContratoModalidadeCredito WHERE ContratoModalidade = " + codModalidade;
                try {
                    ResultSet rs = contratoModalidadeCreditoDao.createStatement(connection, sql);
                    if (rs.next()) {
                        ContratoModalidadeCredito contratoModalidadeCredito1 = new ContratoModalidadeCredito();
                        contratoModalidadeCredito1.setCodigo(rs.getInt("codigo"));
                        contratoModalidadeCredito1.setQtdCreditoCompra(rs.getInt("qtdcreditocompra"));
                        contratoModalidadeCredito1.setQtdCreditoDisponivel(rs.getInt("qtdcreditodisponivel"));
                        contratoModalidadeCredito1.setValorMensal(rs.getDouble("valormensal"));
                        contratoModalidadeCredito1.setValorTotal(rs.getDouble("valortotal"));
                        contratoModalidadeCredito1.setValorUnitario(rs.getDouble("valorunitario"));
                        contratoModalidadeCredito.add(contratoModalidadeCredito1);
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        if (!contratoModalidadeCredito.isEmpty()) {
            return contratoModalidadeCredito.get(0);
        }
        return null;
    }
}
