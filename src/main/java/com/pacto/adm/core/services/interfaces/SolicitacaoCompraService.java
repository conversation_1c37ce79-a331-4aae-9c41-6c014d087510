package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.filtros.FiltroSolicitacaoCompraJSON;
import com.pacto.adm.core.dto.solicitacaocompra.SolicitacaoCompraDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface SolicitacaoCompraService {
    List<SolicitacaoCompraDTO> listarSolicitacoes(FiltroSolicitacaoCompraJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    SolicitacaoCompraDTO buscarSolicitacao(Integer id) throws ServiceException;

    SolicitacaoCompraDTO saveOrUpdate(SolicitacaoCompraDTO solicitacaoCompraDTO) throws ServiceException;
}
