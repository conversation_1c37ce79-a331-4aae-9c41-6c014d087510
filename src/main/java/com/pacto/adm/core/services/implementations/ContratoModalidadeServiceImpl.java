package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.contratomodalidade.ContratoModalidadeAdapter;
import com.pacto.adm.core.adapters.contratomodalidade.ContratoModalidadeCreditoAdapter;
import com.pacto.adm.core.dao.interfaces.AulaDesmarcadaDao;
import com.pacto.adm.core.dao.interfaces.ClienteDao;
import com.pacto.adm.core.dao.interfaces.ContratoDao;
import com.pacto.adm.core.dao.interfaces.ContratoModalidadeDao;
import com.pacto.adm.core.dao.interfaces.EmpresaDao;
import com.pacto.adm.core.dao.interfaces.MatriculaAlunoHorarioTurmaDao;
import com.pacto.adm.core.dto.agendatotal.AgendaTotalDTO;
import com.pacto.adm.core.dto.base.ClientDiscoveryDataDTO;
import com.pacto.adm.core.dto.contratomodalidade.ContratoModalidadeDTO;
import com.pacto.adm.core.dto.contratomodalidade.ContratoModalidadeHorarioTurmaDTO;
import com.pacto.adm.core.dto.contratomodalidade.ContratoModalidadeTurmaDTO;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.adm.core.entities.contrato.MatriculaAlunoHorarioTurma;
import com.pacto.adm.core.entities.contrato.NivelTurma;
import com.pacto.adm.core.entities.contrato.Presenca;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.services.interfaces.ContratoModalidadeCreditoService;
import com.pacto.adm.core.services.interfaces.ContratoModalidadeService;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.adm.core.services.interfaces.NivelTurmaService;
import com.pacto.adm.core.services.interfaces.PresencaService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.enumerador.DiaSemana;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class ContratoModalidadeServiceImpl implements ContratoModalidadeService {

    @Autowired
    private ContratoModalidadeAdapter contratoModalidadeAdapter;
    @Autowired
    private ContratoModalidadeDao contratoModalidadeDao;
    @Autowired
    private ContratoDao contratoDao;
    @Autowired
    private MatriculaAlunoHorarioTurmaDao matriculaAlunoHorarioTurmaDao;
    @Autowired
    private NivelTurmaService nivelTurmaService;
    @Autowired
    private PresencaService presencaService;
    @Autowired
    private ContratoModalidadeCreditoService contratoModalidadeCreditoService;
    @Autowired
    private ContratoModalidadeCreditoAdapter contratoModalidadeCreditoAdapter;
    @Autowired
    private ClienteDao clienteDao;
    @Autowired
    private EmpresaDao empresaDao;
    @Autowired
    private RequestService requestService;
    @Autowired
    private AulaDesmarcadaDao aulaDesmarcadaDao;
    @Autowired
    private DiscoveryService discoveryService;
    @Autowired
    private HttpServico httpServico;

    @Override
    public List<ContratoModalidadeDTO> findAllByContrato(Integer codContrato, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<ContratoModalidadeDTO> contratoModalidadeDTOS = contratoModalidadeAdapter.toDtos(contratoModalidadeDao.findAllByContrato(codContrato, paginadorDTO));

            if (!contratoModalidadeDTOS.isEmpty()) {
                Contrato contrato = contratoDao.findById(codContrato);
                for (ContratoModalidadeDTO contratoModalidadeDTO : contratoModalidadeDTOS) {
                    for (ContratoModalidadeTurmaDTO contratoModalidadeTurmaDTO : contratoModalidadeDTO.getTurmas()) {
                        preencherInformacoesPresencaTurmas(codContrato, contrato, contratoModalidadeDTO, contratoModalidadeTurmaDTO);

                        setaReposicoesEAulasDesmarcadas(codContrato, contrato, contratoModalidadeTurmaDTO);

                        setaTotalDeAultasDesmarcadasConsiderandoContratoAntigo(codContrato, contrato, contratoModalidadeTurmaDTO);
                    }
                }
                processarDadosContrato(contratoModalidadeDTOS, codContrato);
            }
            return contratoModalidadeDTOS;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    private void processarDadosContrato(List<ContratoModalidadeDTO> listaContratoModalidade, Integer codigoContrato) throws Exception {
        try {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = discoveryService.getClientDiscovery();
            String zwUrl = clientDiscoveryDataDTO.getServiceUrls().getZwUrl();
            if (UteisValidacao.emptyString(zwUrl)) {
                throw new Exception("ZwUrl não informado");
            }

            JSONObject jsonBody = new JSONObject();
            jsonBody.put("contrato", codigoContrato);
            jsonBody.put("tipo", "detalhe_contrato_modalidade");

            StringBuilder urlZw = new StringBuilder(zwUrl);
            urlZw.append("/prest/tela-cliente")
                    .append("?key=").append(requestService.getUsuarioAtual().getChave())
                    .append("&op=contrato");

            ResponseEntity<String> responseZw = httpServico.doJson(
                    urlZw.toString(),
                    jsonBody.toString(), HttpMethod.POST, "");

            JSONArray jsonArrayCM = new JSONArray(new JSONObject(new JSONObject(responseZw).getString("body")).getString("content"));
            for (int e = 0; e < jsonArrayCM.length(); e++) {
                JSONObject jsonCM = jsonArrayCM.getJSONObject(e);
                Integer contrato_modalidade_codigo = jsonCM.optInt("contrato_modalidade_codigo");
                JSONArray jsonArrayCMT = jsonCM.getJSONArray("contrato_modalidade_turma_lista");
                for (ContratoModalidadeDTO contratoModalidadeDTO : listaContratoModalidade) {
                    if (contratoModalidadeDTO.getCodigo().equals(contrato_modalidade_codigo)) {
                        for (int e1 = 0; e1 < jsonArrayCMT.length(); e1++) {
                            JSONObject jsonCMT = jsonArrayCMT.getJSONObject(e1);
                            Integer contrato_modalidade_turma_codigo = jsonCMT.optInt("contrato_modalidade_turma_codigo");
                            for (ContratoModalidadeTurmaDTO contratoModalidadeTurmaDTO : contratoModalidadeDTO.getTurmas()) {
                                if (contratoModalidadeTurmaDTO.getCodigo().equals(contrato_modalidade_turma_codigo)) {
                                    contratoModalidadeTurmaDTO.setPresencas(jsonCMT.optInt("presenca"));
                                    contratoModalidadeTurmaDTO.setReposicoes(jsonCMT.optInt("reposicao"));
                                    contratoModalidadeTurmaDTO.setFaltas(jsonCMT.optInt("faltas"));
                                    contratoModalidadeTurmaDTO.setDesmarcadas(jsonCMT.optInt("desmarcadas"));
                                    contratoModalidadeTurmaDTO.setTotalAulas(jsonCMT.optInt("aulas_total"));
                                    contratoModalidadeTurmaDTO.setTotalAulasHoje(jsonCMT.optInt("aulas_total_atual"));
                                    contratoModalidadeTurmaDTO.setTotalAulasDesmarcadasContratoPassado(jsonCMT.optInt("desmarcadas_contrato_anterior"));
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }



    private void setaTotalDeAultasDesmarcadasConsiderandoContratoAntigo(Integer codContrato, Contrato contrato, ContratoModalidadeTurmaDTO contratoModalidadeTurmaDTO) throws ServiceException {
        if (contrato.getContratoBaseadoRenovacao() > 0 || contrato.getContratoBaseadoRematricula() > 0) {
            Integer contratoAnterior = contrato.getContratoBaseadoRenovacao() > 0 ? contrato.getContratoBaseadoRenovacao() : contrato.getContratoBaseadoRematricula();
            contratoModalidadeTurmaDTO.setTotalAulasDesmarcadasContratoPassado(consultaTotalAulasDesmarcadasContratoAnterior(codContrato, contrato.getEmpresa().getCodigo(), contratoModalidadeTurmaDTO.getCodigo(), contratoAnterior));
        }
    }

    private void preencherInformacoesPresencaTurmas(Integer codContrato, Contrato contrato, ContratoModalidadeDTO contratoModalidadeDTO, ContratoModalidadeTurmaDTO contratoModalidadeTurmaDTO) throws Exception {
        Integer presencas = 0;
        Integer faltas = 0;
        Integer contAulas = 0;
        Integer contAulasPossiveis = 0;
        Integer aulasCanceladaSemReposicao = 0;

        List<Presenca> listaPresenca = new ArrayList<>();
        List<MatriculaAlunoHorarioTurma> matriculaAlunoHorarioTurmas = new ArrayList<>();

        Empresa empresa = empresaDao.findById(requestService.getEmpresaId());
        if (!contrato.getEmpresa().getHabilitarSomaDeAulaNaoVigente()) {
            Date dataPesquisa = contrato.getVigenciaAteAjustada();
            if (obterHistoricoContratoPorCodigoContratoTipoHistorico(contrato.getCodigo(), "CA")) {
                dataPesquisa = Uteis.somarDias(dataPesquisa, -1);
            }
            if (contratoModalidadeTurmaDTO.getHorarios() != null) {
                for (ContratoModalidadeHorarioTurmaDTO contratoModalidadeHorarioTurma : contratoModalidadeTurmaDTO.getHorarios()) {
                    MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurma = matriculaAlunoHorarioTurmaDao.consultarMatriculaAtivaPorHorarioTurma(contrato.getCodigo(), contratoModalidadeHorarioTurma.getHorarioTurma().getCodigo(), dataPesquisa);
                    if (matriculaAlunoHorarioTurma != null) {
                        List<Presenca> lista = presencaService.listaPresenca(matriculaAlunoHorarioTurma.getCodigo());
                        if (lista != null) {
                            listaPresenca.addAll(lista);
                        }
                        matriculaAlunoHorarioTurmas.add(matriculaAlunoHorarioTurma);
                        Date dInicio = matriculaAlunoHorarioTurma.getDataInicio();
                        Date dFim = contrato.getVigenciaAteAjustada();

                        Integer aulaCancSemRep = contarAulasDesmarcadasPorPeriodoSemReposicao(contrato.getCodigo(), matriculaAlunoHorarioTurma.getHorarioTurma().getCodigo(), dInicio, dFim, false);
                        aulasCanceladaSemReposicao += aulaCancSemRep;

                        contratoModalidadeTurmaDTO.setDataInicioMatricula(dInicio);
                        contratoModalidadeTurmaDTO.setDataFimMatricula(matriculaAlunoHorarioTurma.getDataFim());

                        contAulas = contAulas + Calendario.contarDiasDaSemanaEntre(dInicio, dFim, DiaSemana.getDiaSemana(matriculaAlunoHorarioTurma.getHorarioTurma().getDiaSemana()).getNumeral());
                        contAulas = contAulas - aulaCancSemRep;
                        if (Calendario.maiorOuIgual(contrato.getVigenciaAteAjustada(), Calendario.hoje())) {
                            contAulasPossiveis = contAulasPossiveis + Calendario.contarDiasDaSemanaEntre(dInicio, Calendario.hoje(), DiaSemana.getDiaSemana(matriculaAlunoHorarioTurma.getHorarioTurma().getDiaSemana()).getNumeral());

                            contAulasPossiveis = contAulasPossiveis - contarAulasDesmarcadasPorPeriodoSemReposicao(contrato.getCodigo(), matriculaAlunoHorarioTurma.getHorarioTurma().getCodigo(), dInicio, Calendario.hoje(), false);
                        } else {
                            contAulasPossiveis = contAulas;
                        }
                        presencas = presencas + contarPresencasAluno(matriculaAlunoHorarioTurma.getCodigo(), null, null);

                        NivelTurma nivelTurma = nivelTurmaService.consultarPorCodigo(matriculaAlunoHorarioTurma.getHorarioTurma().getNivelTurma().getCodigo());
                        if (nivelTurma != null) {
                            matriculaAlunoHorarioTurma.getHorarioTurma().getNivelTurma().setDescricao(nivelTurma.getDescricao());
                        }
                    }
                }
            }
        } else {
            matriculaAlunoHorarioTurmas = matriculaAlunoHorarioTurmaDao.consultarMatriculasPorCodigoContrato(contrato.getCodigo());

            for (MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurma : matriculaAlunoHorarioTurmas) {
                if (!UteisValidacao.emptyNumber(matriculaAlunoHorarioTurma.getCodigo())) {
                    List<Presenca> lista = presencaService.listaPresenca(matriculaAlunoHorarioTurma.getCodigo());
                    if (lista != null) {
                        listaPresenca.addAll(lista);
                    }
                    Date dInicio = matriculaAlunoHorarioTurma.getDataInicio();
                    Date dFim = Calendario.maiorOuIgual(matriculaAlunoHorarioTurma.getDataFim(), contrato.getVigenciaAteAjustada()) ? contrato.getVigenciaAteAjustada() : matriculaAlunoHorarioTurma.getDataFim();

                    Integer aulaCancSemRep = contarAulasDesmarcadasPorPeriodoSemReposicao(contrato.getCodigo(), matriculaAlunoHorarioTurma.getHorarioTurma().getCodigo(), dInicio, dFim, false);

                    aulasCanceladaSemReposicao += aulaCancSemRep;

                    contratoModalidadeTurmaDTO.setDataInicioMatricula(dInicio);
                    contratoModalidadeTurmaDTO.setDataFimMatricula(matriculaAlunoHorarioTurma.getDataFim());
                    contAulas = contAulas + Calendario.contarDiasDaSemanaEntre(dInicio, dFim, DiaSemana.getDiaSemana(matriculaAlunoHorarioTurma.getHorarioTurma().getDiaSemana()).getNumeral());
                    contAulas = contAulas - aulaCancSemRep;
                    if (Calendario.maiorOuIgual(contrato.getVigenciaAteAjustada(), Calendario.hoje())) {
                        Date comparar = Calendario.hoje();

                        if (dFim != null && Calendario.menor(dFim, Calendario.hoje())) {
                            comparar = dFim;
                        }

                        contAulasPossiveis = contAulasPossiveis + Calendario.contarDiasDaSemanaEntre(dInicio, comparar, DiaSemana.getDiaSemana(matriculaAlunoHorarioTurma.getHorarioTurma().getDiaSemana()).getNumeral());

                        contAulasPossiveis = contAulasPossiveis - contarAulasDesmarcadasPorPeriodoSemReposicao(contrato.getCodigo(), matriculaAlunoHorarioTurma.getHorarioTurma().getCodigo(), dInicio, Calendario.hoje(), false);
                    } else {
                        contAulasPossiveis = contAulas;
                    }
                    presencas = presencas + contarPresencasAluno(matriculaAlunoHorarioTurma.getCodigo(), null, null);
                }
            }
        }
        if (contrato.isVendaCreditoTreino()) {
            contratoModalidadeTurmaDTO.setTotalAulas(contrato.getContratoDuracao().getContratoDuracaoCreditoTreino().getQuantidadeCreditoCompra());
            presencas = consultaTotalOperacao(codContrato, false);
            faltas = consultaTotalOperacao(codContrato, true);
            contratoModalidadeTurmaDTO.setPresencas(presencas);
            contratoModalidadeTurmaDTO.setFaltas(faltas);
            contratoModalidadeTurmaDTO.setQuantidadeCreditoCompra(contrato.getContratoDuracao().getContratoDuracaoCreditoTreino().getQuantidadeCreditoCompra());
            contratoModalidadeTurmaDTO.setQuantidadeCreditoDisponivel(contrato.getContratoDuracao().getContratoDuracaoCreditoTreino().getQuantidadeCreditoDisponivel());
        } else if (contrato.isVendaCreditoSessao()) {
            contratoModalidadeDTO.setContratoModalidadeCredito(contratoModalidadeCreditoAdapter.toDto(contratoModalidadeCreditoService.consultarPorContratoModalidade(contratoModalidadeDTO.getCodigo())));
            contratoModalidadeTurmaDTO.setTotalAulas(contratoModalidadeDTO.getContratoModalidadeCredito().getQtdCreditoCompra());

        } else {
            contratoModalidadeTurmaDTO.setTotalAulas(contAulas);
        }

        contratoModalidadeTurmaDTO.setTotalAulasDesmarcadasContratoPassado(aulasCanceladaSemReposicao);
        contratoModalidadeTurmaDTO.setTotalAulasHoje(contAulasPossiveis);
        contratoModalidadeTurmaDTO.setPresencas(presencas);
        Cliente cliente = clienteDao.findByPessoa(contrato.getPessoa().getCodigo());
        List<String> horariosTurma = matriculaAlunoHorarioTurmaDao.codigosHorarioTurmaContratoTurma(contrato.getCodigo(), contratoModalidadeTurmaDTO.getTurma().getCodigo());
        if (!contrato.isVendaCreditoTreino()) {
            List<AgendaTotalDTO> aulasPossiveisContrato = consultaAulasContrato(cliente, contrato.getCodigo(), empresa.getHabilitarSomaDeAulaNaoVigente(), matriculaAlunoHorarioTurmas);

            List<AgendaTotalDTO> aulasPossiveisTurma = new ArrayList<>();
            for (AgendaTotalDTO ag : aulasPossiveisContrato) {
                if (horariosTurma.contains(ag.getId())) {
                    aulasPossiveisTurma.add(ag);
                }
            }

            List<AgendaTotalDTO> listaFaltas = validarDadosLista(aulasPossiveisTurma, listaPresenca);
            contratoModalidadeTurmaDTO.setFaltas(listaFaltas != null ? listaFaltas.size() : 0);
//            if (listaPresenca.size() <= aulasPossiveisTurma.size()) {
//                contratoModalidadeTurmaDTO.setFaltas(aulasPossiveisTurma.size() - listaPresenca.size());
//            }
        }
    }
    public List<AgendaTotalDTO> consultaAulasContrato(Cliente cliente, Integer contrato,boolean habilitarSomaDeAulaNaoVigente,List<MatriculaAlunoHorarioTurma> listaMatriculaHorarioTurma) throws Exception {
        Map<Date, List<Integer>> mapaAgendamentosDesmarcados = aulaDesmarcadaDao.consultarAgendamentosDesmarcadosSemValidarData(cliente.getCodigo(),contrato);

        return new ArrayList<>(matriculaAlunoHorarioTurmaDao.consultarAulas(Integer.parseInt(cliente.getMatricula()), Calendario.hoje(), mapaAgendamentosDesmarcados, contrato, habilitarSomaDeAulaNaoVigente, listaMatriculaHorarioTurma));
    }
    private List<AgendaTotalDTO> validarDadosLista(List<AgendaTotalDTO> lista, List<Presenca> listaPresenca) {
        List<AgendaTotalDTO> listaAula = new ArrayList<AgendaTotalDTO>();
        try {
            for (Presenca presencaVO : listaPresenca) {
                for (AgendaTotalDTO agendaTotal : lista) {
                    String dataSting = agendaTotal.getInicio().substring(0, 10);
                    SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                    Date dataAgenda = sdf.parse(dataSting);
                    if (agendaTotal.getCodigoMatriculaAlunoHorarioTurma().equals(presencaVO.getDadosTurma()) && Calendario.igual(dataAgenda, presencaVO.getDataPresenca())) {
                        listaAula.add(agendaTotal);
                        break;
                    }
                }
            }

            for (final AgendaTotalDTO agendaTotal : listaAula) {
                AgendaTotalDTO agendaTotal1 = lista.stream().filter(o -> o.getId().equals(agendaTotal.getId()) && o.getInicio().equals(agendaTotal.getInicio())).findFirst().orElse(null);

                lista.remove(agendaTotal1);
            }

            //Remover falta em aula do dia com horario que ainda nem chegou.
            for (final AgendaTotalDTO aula : lista) {
                if (Calendario.igual(Calendario.getDataComHoraZerada(Uteis.getDate(aula.getInicio())), Calendario.getDataComHoraZerada(Calendario.hoje()))) {
                    String[] dataHora = aula.getInicio().split(" ");
                    if (Calendario.maiorComHora(Calendario.getDataComHora(Uteis.getDate(dataHora[0]), dataHora[1]), Calendario.hoje())) {
                        lista.remove(aula);
                        break;
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return lista;
    }

    private void setaReposicoesEAulasDesmarcadas(Integer codContrato, Contrato contrato, ContratoModalidadeTurmaDTO contratoModalidadeTurmaDTO) throws ServiceException {
        contratoModalidadeTurmaDTO.setReposicoes(consultaTotalReposicoes(codContrato, contratoModalidadeTurmaDTO.getTurma().getCodigo(), false));
        contratoModalidadeTurmaDTO.setTotalReposicoesPresentes(consultaTotalReposicoes(codContrato, contratoModalidadeTurmaDTO.getTurma().getCodigo(), true));
        contratoModalidadeTurmaDTO.setDesmarcadas(consultaTotalAulasDesmarcadas(codContrato, contrato.getEmpresa().getCodigo(), contratoModalidadeTurmaDTO.getTurma().getCodigo()));
    }

    private Integer contarAulasDesmarcadasPorPeriodoSemReposicao(Integer codContrato, Integer horarioTurma, Date dataInicio, Date dataFim, Boolean permiteReporAulaDesmarcada) throws ServiceException {
        List<Integer> presencas = new ArrayList<>();

        try (SessionImplementor sessionImplementor = contratoModalidadeDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                String sql = "select count(codigo) as aulas from auladesmarcada where permiteReporAulaDesmarcada = " + permiteReporAulaDesmarcada + " and reposicao is null" +
                        " and contrato = " + codContrato + " and horarioturma = " + horarioTurma + " and dataorigem::date >= '" + dataInicio + "' and dataorigem::date <= '" + dataFim + "'";
                try {
                    ResultSet rs = contratoModalidadeDao.createStatement(connection, sql);
                    if (rs.next()) {
                        presencas.add(rs.getInt("aulas"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        if (!presencas.isEmpty()) {
            return presencas.get(0);
        }
        return 0;
    }

    private Boolean obterHistoricoContratoPorCodigoContratoTipoHistorico(Integer codContrato, String tipoHistorico) throws ServiceException {
        List<Boolean> existe = new ArrayList<>();

        try (SessionImplementor sessionImplementor = contratoModalidadeDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                String sql = "select exists (SELECT codigo FROM HistoricoContrato WHERE contrato = " + codContrato + " and tipoHistorico ='" + tipoHistorico.toUpperCase() + "') as existe";
                try {
                    ResultSet rs = contratoModalidadeDao.createStatement(connection, sql);
                    if (rs.next()) {
                        existe.add(rs.getBoolean("existe"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        if (!existe.isEmpty()) {
            return existe.get(0);
        }
        return false;
    }

    private Integer consultaTotalAulasDesmarcadas(Integer codContrato, Integer codEmpresa, Integer codTurma) throws ServiceException {
        List<Integer> qtd = new ArrayList<>();

        try (SessionImplementor sessionImplementor = contratoModalidadeDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                String sql = "select count(codigo) as aulas from auladesmarcada  where datareposicao is null and contrato = " + codContrato + " and ( turma = " + codTurma + " or turmadestino = " + codTurma + " ) and empresa = " + codEmpresa + " and permiteReporAulaDesmarcada = true  and contratoanterior is null";
                try {
                    ResultSet rs = contratoModalidadeDao.createStatement(connection, sql);
                    if (rs.next()) {
                        qtd.add(rs.getInt("aulas"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        if (!qtd.isEmpty()) {
            return qtd.get(0);
        }
        return 0;
    }

    private Integer consultaTotalReposicoes(Integer codContrato, Integer codTurma, Boolean presenca) throws ServiceException {
        List<Integer> qtd = new ArrayList<>();

        try (SessionImplementor sessionImplementor = contratoModalidadeDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                String sql = "select count(r.codigo) from reposicao r left join auladesmarcada a on a.reposicao = r.codigo where r.contrato in ( " + codContrato + ") and (r.turmaorigem in ( " + codTurma + ") or (a.turmadestino is not null and a.turmadestino in(" + codTurma + ")))";
                if (presenca) {
                    sql += " and r.datapresenca is not null ";
                }
                try {
                    ResultSet rs = contratoModalidadeDao.createStatement(connection, sql);
                    if (rs.next()) {
                        qtd.add(rs.getInt("count"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        if (!qtd.isEmpty()) {
            return qtd.get(0);
        }
        return 0;
    }

    private Integer consultaTotalAulasDesmarcadasContratoAnterior(Integer codContrato, Integer codEmpresa, Integer codTurma, Integer codContratoAnterior) throws ServiceException {
        List<Integer> qtd = new ArrayList<>();

        try (SessionImplementor sessionImplementor = contratoModalidadeDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                String sql = "select count(codigo) as aulas from auladesmarcada  where datareposicao is null and contrato = " + codContrato + " and empresa = " + codEmpresa + " and contratoanterior = " + codContratoAnterior + " and permiteReporAulaDesmarcada = true";
                try {
                    ResultSet rs = contratoModalidadeDao.createStatement(connection, sql);
                    if (rs.next()) {
                        qtd.add(rs.getInt("aulas"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        if (qtd.size() > 0) {
            return qtd.get(0);
        }
        return 0;
    }

    private Integer contarPresencasAluno(Integer codMatAlunoHorarioTurma, Date periodoInicial, Date periodoFinal) throws ServiceException {
        List<Integer> qtd = new ArrayList<>();

        try (SessionImplementor sessionImplementor = contratoModalidadeDao.createSessionCurrentWork()) {
            sessionImplementor.doWork(connection -> {
                String condicaoPeriodo = "";
                if (periodoInicial != null && periodoFinal != null) {
                    condicaoPeriodo = "AND datapresenca >= '" + Uteis.getDataJDBC(periodoInicial) + "' AND datapresenca <= '" + Uteis.getDataJDBC(periodoFinal) + "' ";
                }
                String sql = "select count(codigo) from presenca where dadosturma = " + codMatAlunoHorarioTurma + " and datapresenca is not null " + condicaoPeriodo;
                try {
                    ResultSet rs = contratoModalidadeDao.createStatement(connection, sql);
                    if (rs.next()) {
                        qtd.add(rs.getInt("count"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        if (!qtd.isEmpty()) {
            return qtd.get(0);
        }
        return 0;
    }

    private Integer consultaTotalOperacao(Integer codContrato, boolean consultarFaltas) throws ServiceException {
        List<Integer> qtd = new ArrayList<>();
        int codTipoOperacao = consultarFaltas ? 3 : 2;

        try (SessionImplementor sessionImplementor = contratoModalidadeDao.createSessionCurrentWork()){
            sessionImplementor.doWork(connection -> {
                String sql = "select count(*) as total from controleCreditoTreino where contrato = " + codContrato + " and tipoOperacaoCreditoTreino = " + codTipoOperacao;
                try {
                    ResultSet rs = contratoModalidadeDao.createStatement(connection, sql);
                    if (rs.next()) {
                        qtd.add(rs.getInt("total"));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        if (!qtd.isEmpty()) {
            return qtd.get(0);
        }
        return 0;
    }

}
