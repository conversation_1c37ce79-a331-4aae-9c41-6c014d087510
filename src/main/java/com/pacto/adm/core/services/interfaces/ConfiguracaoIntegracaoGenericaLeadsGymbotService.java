package com.pacto.adm.core.services.interfaces;
 import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGenericaLeadsGymbotDTO;
 import com.pacto.config.exceptions.ServiceException;


public interface ConfiguracaoIntegracaoGenericaLeadsGymbotService {

    ConfiguracaoIntegracaoGenericaLeadsGymbotDTO findByEmpresaId(Integer codigoEmpresa) throws ServiceException;

    ConfiguracaoIntegracaoGenericaLeadsGymbotDTO salvar(ConfiguracaoIntegracaoGenericaLeadsGymbotDTO configuracaoIntegracaoGenericaLeadsGymbotDTO) throws ServiceException;

}
