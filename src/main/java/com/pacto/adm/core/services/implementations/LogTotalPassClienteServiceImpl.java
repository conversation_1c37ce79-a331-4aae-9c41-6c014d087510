package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.LogAdapter;
import com.pacto.adm.core.adapters.LogTotalPassAdapter;
import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.dao.interfaces.LogDao;
import com.pacto.adm.core.dao.interfaces.LogTotalPassDao;
import com.pacto.adm.core.dao.interfaces.UsuarioDao;
import com.pacto.adm.core.dto.ClienteDadosGymPassDTO;
import com.pacto.adm.core.dto.ClienteDadosPessoaisDTO;
import com.pacto.adm.core.dto.LogTotalPassDTO;
import com.pacto.adm.core.dto.TelefoneDTO;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Log;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.contrato.LogTotalPass;
import com.pacto.adm.core.enumerador.situacaoaluno.SituacaoDoAlunoEnum;
import com.pacto.adm.core.services.interfaces.LogTotalPassService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Query;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class LogTotalPassClienteServiceImpl implements LogTotalPassService {

    @Autowired
    UsuarioAdapter usuarioAdapter;
    @Autowired
    UsuarioDao usuarioDao;

    private final LogTotalPassDao logTotalPassDao;
    private final LogTotalPassAdapter logTotalPassAdapter;
    private final RequestService requestService;
    private List<LogTotalPass> logs;

    private Logger logger = Logger.getLogger(LogTotalPassClienteServiceImpl.class.getName());

    public LogTotalPassClienteServiceImpl(LogTotalPassDao logTotalPassDao, LogTotalPassAdapter logTotalPassAdapter, RequestService requestService) {
        this.logs = new ArrayList<>();
        this.logTotalPassDao = logTotalPassDao;
        this.logTotalPassAdapter = logTotalPassAdapter;
        this.requestService = requestService;
    }

    public List<LogTotalPassDTO> consultaLogTotalPass(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception {
        List<LogTotalPassDTO> logTotalPassDTOS = logTotalPassAdapter.toDtos(logTotalPassDao.findByPessoa(codPessoa, paginadorDTO));

        for (LogTotalPassDTO logTotalPassDTO : logTotalPassDTOS) {
            Integer codigoUsuario = logTotalPassDTO.getUsuario();
            if (codigoUsuario != null) {
                try {
                    UsuarioDTO usuario = usuarioAdapter.toDto(usuarioDao.findById(codigoUsuario));
                    logTotalPassDTO.setUsuarioDTO(usuario);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }

        return logTotalPassDTOS;
    }


    @Override
     public Boolean consultaLogTotalPassPorPessoa(Integer codPessoa) throws ServiceException {
            List<Boolean> existe = new ArrayList<>();

            try (SessionImplementor sessionImplementor = logTotalPassDao.createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    String sql = "select exists (SELECT codigo FROM logtotalpass WHERE pessoa = " + codPessoa + ") as existe";
                    try {
                        ResultSet rs = logTotalPassDao.createStatement(connection, sql);
                        if (rs.next()) {
                            existe.add(rs.getBoolean("existe"));
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }

                });
            } catch (Exception e) {
                logger.log(Level.SEVERE, e.getMessage());
                e.printStackTrace();
                throw new ServiceException(e);
            }
            if (!existe.isEmpty()) {
                return existe.get(0);
            }
            return false;
        }

    public void incluirLogTotalPass (LogTotalPassDTO logTotalPassDTO) throws Exception {
        LogTotalPass logTotalPass = new LogTotalPass();
        logTotalPass.setApikey(logTotalPassDTO.getApikey());
        logTotalPass.setUsuario(logTotalPassDTO.getUsuario());
        logTotalPass.setTipo(logTotalPassDTO.getTipo());
        logTotalPass.setUri(logTotalPassDTO.getUri());
        logTotalPass.setResposta(logTotalPassDTO.getResposta());
        logTotalPass.setTempoResposta(logTotalPassDTO.getTempoResposta());
        logTotalPass.setPessoa(logTotalPassDTO.getPessoa());
        logTotalPass.setDataregistro(logTotalPassDTO.getDataregistro());
        logTotalPass.setEmpresa(logTotalPassDTO.getEmpresa());
        logTotalPass.setIp(logTotalPassDTO.getIp());
        logTotalPass.setJson(logTotalPassDTO.getJson());
        logTotalPass.setOrigem(logTotalPassDTO.getOrigem());

        logTotalPassDao.save(logTotalPass);
    }
}
