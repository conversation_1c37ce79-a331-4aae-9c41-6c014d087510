package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoWordPressAdapter;
import com.pacto.adm.core.dao.interfaces.ConfiguracaoIntegracaoWordPressDao;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoWordPressDTO;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoWordPress;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoIntegracaoWordPressService;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Locale;

@Service
public class ConfiguracaoIntegracaoWordPressServiceImpl implements ConfiguracaoIntegracaoWordPressService {

    @Value("${integracao.wordpress.emaildestinolead}")
    private String emailDestinoLead;
    private final MessageSource messageSource;
    private final RequestService requestService;
    private final ConfiguracaoIntegracaoWordPressDao configDao;
    private final ConfiguracaoIntegracaoWordPressAdapter configAdapter;

    public ConfiguracaoIntegracaoWordPressServiceImpl(MessageSource messageSource, RequestService requestService, ConfiguracaoIntegracaoWordPressDao configDao, ConfiguracaoIntegracaoWordPressAdapter configAdapter) {
        this.messageSource = messageSource;
        this.requestService = requestService;
        this.configDao = configDao;
        this.configAdapter = configAdapter;
    }


    @Override
    public ConfiguracaoIntegracaoWordPressDTO findByEmpresaId(Integer codigoEmpresa) throws ServiceException {
        ConfiguracaoIntegracaoWordPress configEntity = configDao.findByEmpresaId(codigoEmpresa);
        ConfiguracaoIntegracaoWordPressDTO configDTO;
        if (!UteisValidacao.emptyNumber(configEntity.getCodigo())) {
            configDTO = configAdapter.toDto(configEntity);
        } else {
            configDTO = new ConfiguracaoIntegracaoWordPressDTO();
            configDTO.setEmpresa(new EmpresaDTO());
            configDTO.getEmpresa().setCodigo(codigoEmpresa);
        }
        configDTO.setEmailDestino(emailDestinoLead);

        configDTO.setFormularioPadraoMensagem("<label> Seu nome (obrigatório) [text* nome] </label> <label> " +
                "Seu e-mail (obrigatório) [email* email] </label>" +
                " <label> Seu telefone (obrigatório) [tel* telefone] </label> [submit \"Enviar\"]");

        StringBuilder corpoPadraoMensagem = new StringBuilder();
        corpoPadraoMensagem.append(configDTO.getEmpresa().getCodigo());
        corpoPadraoMensagem.append(";[nome];[email];[telefone];COLOQUE_O_NOME_DA_LANDING_PAGE_AQUI;COLOQUE_A_URL_DA_LANDING_PAGE_AQUI;");
        corpoPadraoMensagem.append(requestService.getUsuarioAtual().getChave());
        configDTO.setCorpoPadraoMensagem(corpoPadraoMensagem.toString());

        return configDTO;
    }

    @Override
    public ConfiguracaoIntegracaoWordPressDTO salvar(ConfiguracaoIntegracaoWordPressDTO configDTO) throws ServiceException {
        validarCampos(configDTO);
        try {
            configDao.getCurrentSession().clear();
            ConfiguracaoIntegracaoWordPress configEntity;
            if (Uteis.intNullOrEmpty(configDTO.getCodigo())) {
                configEntity = configAdapter.toEntity(configDTO);
                configEntity = configDao.save(configEntity);
            } else {
                configEntity = configAdapter.toEntity(configDTO);
                configEntity = configDao.update(configEntity);
            }
            ConfiguracaoIntegracaoWordPressDTO dtoRetornar = configAdapter.toDto(configEntity);
            return dtoRetornar;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public void validarCampos(ConfiguracaoIntegracaoWordPressDTO configuracaoIntegracaoWordPressDTO) throws ServiceException {
        if (configuracaoIntegracaoWordPressDTO.getEmpresa() == null || UteisValidacao.emptyNumber(configuracaoIntegracaoWordPressDTO.getEmpresa().getCodigo())) {
            throw new ServiceException(messageSource.getMessage("integracao.wordpress.empresa.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (configuracaoIntegracaoWordPressDTO.getResponsavelPadrao() == null || UteisValidacao.emptyNumber(configuracaoIntegracaoWordPressDTO.getResponsavelPadrao().getCodigo())) {
            throw new ServiceException(messageSource.getMessage("integracao.wordpress.responsavel.nao.informado", null, new Locale(requestService.getLocale())));
        }
        if (Uteis.nullOrEmpty(configuracaoIntegracaoWordPressDTO.getHoraLimite())) {
            throw new ServiceException(messageSource.getMessage("integracao.wordpress.hora.limite.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (configuracaoIntegracaoWordPressDTO.getAcaoObjecao() == null) {
            throw new ServiceException(messageSource.getMessage("integracao.wordpress.acao.objecao.nao.informado", null, new Locale(requestService.getLocale())));
        }
    }
}
