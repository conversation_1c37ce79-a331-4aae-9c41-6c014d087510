package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.MovParcelaDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroMovParcelaJSON;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface MovParcelaService {

    List<MovParcelaDTO> findAllByCodPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws ServiceException;
    List<MovParcelaDTO> findAllByCodContrato(Integer codContrato, PaginadorDTO paginadorDTO) throws ServiceException;
    List<MovParcelaDTO> findAllByCodRecibo(Integer codRecibo) throws ServiceException;

    List<MovParcelaDTO> consultarPorCodigoPessoa(Integer codPessoa, Integer matricula, FiltroMovParcelaJSON filtrosJson, PaginadorDTO paginadorDTO) throws Exception;

    void cancelarProdutosVinculados(Integer codParcela) throws Exception;

    void cancelarParcelaSorteio(Integer codParcela, String justificativa) throws Exception;

    List<MovParcelaDTO> findAllByCodProduto(Integer codMovProduto, PaginadorDTO paginadorDTO) throws ServiceException;
}
