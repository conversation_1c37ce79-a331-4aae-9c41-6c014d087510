package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.dao.interfaces.UsuarioDao;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.filtros.FiltroUsuarioJSON;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.services.interfaces.UsuarioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UsuarioServiceImpl implements UsuarioService {

    @Autowired
    UsuarioDao usuarioDao;
    @Autowired
    UsuarioAdapter usuarioAdapter;

    @Override
    public List<UsuarioDTO> findAll(FiltroUsuarioJSON filtros) throws ServiceException {
        try {
            usuarioDao.getCurrentSession().clear();
            List<Usuario> usuario = usuarioDao.findAll(filtros);
            return usuarioAdapter.toDtos(usuario);
        } catch (Exception exception) {
            exception.printStackTrace();
            throw new ServiceException(exception);
        }
    }

    @Override
    public List<UsuarioDTO> consultarUsuariosMeta(FiltroUsuarioJSON filtros) throws ServiceException {
        try {
            usuarioDao.getCurrentSession().clear();
            List<Usuario> usuario = usuarioDao.consultarUsuariosMeta(filtros);
            return usuarioAdapter.toDtos(usuario);
        } catch (Exception exception) {
            exception.printStackTrace();
            throw new ServiceException(exception);
        }
    }

    @Override
    public Usuario buscarUsuarioPorCodigo(Integer codUsuario) throws ServiceException {
        try {
            usuarioDao.getCurrentSession().clear();
            Usuario usuario = usuarioDao.findById(codUsuario);
            return usuario;
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return null;
    }
}
