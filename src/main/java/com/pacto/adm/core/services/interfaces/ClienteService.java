package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.ClienteColaboradoresDTO;
import com.pacto.adm.core.dto.ClienteColaboradoresImprimirDTO;
import com.pacto.adm.core.dto.ClienteComBonusDTO;
import com.pacto.adm.core.dto.ClienteComFreepassDTO;
import com.pacto.adm.core.dto.ClienteComGympassDTO;
import com.pacto.adm.core.dto.ClienteDTO;
import com.pacto.adm.core.dto.ClienteDadosAuxiliaresDTO;
import com.pacto.adm.core.dto.ClienteDadosGymPassDTO;
import com.pacto.adm.core.dto.ClienteDadosPessoaisDTO;
import com.pacto.adm.core.dto.ClienteDadosPlanoDTO;
import com.pacto.adm.core.dto.ClienteDadosTotalPassDTO;
import com.pacto.adm.core.dto.ClienteInativoPeriodoAcessoDTO;
import com.pacto.adm.core.dto.ClienteListagemDTO;
import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.dto.QuestionarioClienteDTO;
import com.pacto.adm.core.dto.cliente.ClienteParaVerificarDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.filtros.bi.FiltroBIDTO;
import com.pacto.adm.core.dto.integracaomanychat.ClienteSimplificadoDTO;
import com.pacto.adm.core.dto.linhaTempo.LinhaDoTempoContratoDTO;
import com.pacto.adm.core.dto.linhaTempo.LinhaDoTempoDiaDTO;
import com.pacto.adm.core.dto.LogDTO;
import com.pacto.adm.core.dto.SenhaDeAcessoDTO;
import com.pacto.adm.core.dto.filtros.FiltroClienteJSON;
import com.pacto.adm.core.dto.filtros.FiltroLinhaDoTempoJSON;
import com.pacto.adm.core.dto.filtros.FiltroLogClienteJSON;
import com.pacto.adm.core.dto.filtros.FiltroPessoaJSON;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface ClienteService {

    ClienteDadosPessoaisDTO dadosPessoais(Integer matricula) throws ServiceException;

    ClienteDadosAuxiliaresDTO dadosAuxiliares(Integer matricula) throws ServiceException;

    ClienteDadosPessoaisDTO dadosPessoaisPorPessoa(Integer codPessoa) throws ServiceException;

    ClienteDadosPlanoDTO dadosPlano(Integer matricula) throws ServiceException;
    ClienteDadosGymPassDTO dadosGymPass(Integer matricula) throws ServiceException;
    String autorizaAcessoGymPass(Integer matricula, ClienteDadosGymPassDTO clienteDadosGymPassDTO) throws Exception;
    void salvaDadosGymPass(Integer matricula, ClienteDadosGymPassDTO clienteDadosGymPassDTO) throws ServiceException;
    void deletaDadosGymPass(Integer matricula) throws ServiceException;

    String autorizaAcessoTotalPass(ClienteDadosTotalPassDTO clienteDadosTotalPassDTO) throws ServiceException;

    Integer obterTotalPontos(Integer matricula) throws ServiceException;

    Double obterSaldoContaCorrente(Integer matricula) throws ServiceException;

    List<ClienteListagemDTO> listaClientes(FiltroClienteJSON filtro, PaginadorDTO paginadorDTO) throws ServiceException;

    String salvaSenhaDeAcesso(SenhaDeAcessoDTO senhaDeAcessoDTO) throws Exception;

    void atualizarSintetico(Integer codigoPessoa) throws ServiceException;

    void atualizarSinteticoTelaCliente(Integer codigoCliente) throws ServiceException;

    List<LogDTO> buscarLogs(Integer codigo, Integer pessoa, Integer codClienteTreino,
                            PaginadorDTO paginadorDTO, FiltroLogClienteJSON filtros) throws ServiceException;


    ClienteDTO findByMatricula(Integer codMatricula) throws ServiceException;

    String obterSituacaoClienteInativo(ContratoDTO contrato, ClienteDTO cliente) throws Exception;

    List<ClienteColaboradoresImprimirDTO> listaAlunosColaboradoresImprimir(FiltroPessoaJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;
    List<ClienteColaboradoresDTO> listaAlunosColaboradores(FiltroPessoaJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LinhaDoTempoDiaDTO> linhaDoTempo(Integer matricula, FiltroLinhaDoTempoJSON filtros) throws ServiceException;

    List<LinhaDoTempoContratoDTO> linhaDoTempoContratos(Integer matricula) throws ServiceException;

    void salvaDadosChurn(ClienteDadosPessoaisDTO cliente) throws ServiceException;

    void alterarFreepass(Integer codProdutoFreepass, Integer codResponsavelFreepass);

    void gerarPendenciaQuestionarioCliente(Cliente cliente, QuestionarioClienteDTO questionarioClienteDTO);

    List<ClienteSimplificadoDTO> consultarClienteSimplificadoPorCpf(String cpf) throws ServiceException;

    List<ClienteInativoPeriodoAcessoDTO> consultarClientesInativosComPeriodoAcesso(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    List<ClienteComFreepassDTO> findClientesComFreepass(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    List<ClienteComGympassDTO> findClientesComGympass(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    void limparPendenciaQuestionarioCliente(Cliente cliente);

    boolean clienteEstaInadimplente(Integer codigoMatricula, Integer codigoEmpresa) throws ServiceException;


    List<ClienteParaVerificarDTO> consultarClientesParaVerificar(FiltroBIDTO filtroBIDTO, PaginadorDTO paginadorDTO, Boolean verificado) throws ServiceException;
}
