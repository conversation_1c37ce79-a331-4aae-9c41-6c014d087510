package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoJoinAdapter;
import com.pacto.adm.core.dao.interfaces.ConfiguracaoIntegracaoJoinDao;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoJoinDTO;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoJoin;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoIntegracaoJoinService;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Locale;

@Service
public class ConfiguracaoIntegracaoJoinServiceImpl implements ConfiguracaoIntegracaoJoinService {

    private final MessageSource messageSource;
    private final RequestService requestService;
    private final ConfiguracaoIntegracaoJoinDao configDao;
    private final ConfiguracaoIntegracaoJoinAdapter configAdapter;

    public ConfiguracaoIntegracaoJoinServiceImpl(MessageSource messageSource, RequestService requestService, ConfiguracaoIntegracaoJoinDao configDao, ConfiguracaoIntegracaoJoinAdapter configAdapter) {
        this.messageSource = messageSource;
        this.requestService = requestService;
        this.configDao = configDao;
        this.configAdapter = configAdapter;
    }


    @Override
    public ConfiguracaoIntegracaoJoinDTO findByEmpresaId(Integer codigoEmpresa) throws ServiceException {
        ConfiguracaoIntegracaoJoin configEntity = configDao.findByEmpresaId(codigoEmpresa);
        ConfiguracaoIntegracaoJoinDTO configDTO;
        if (!UteisValidacao.emptyNumber(configEntity.getCodigo())) {
            configDTO = configAdapter.toDto(configEntity);
        } else {
            configDTO = new ConfiguracaoIntegracaoJoinDTO();
            configDTO.setEmpresa(new EmpresaDTO());
            configDTO.getEmpresa().setCodigo(codigoEmpresa);
        }
        return configDTO;
    }

    @Override
    public ConfiguracaoIntegracaoJoinDTO salvar(ConfiguracaoIntegracaoJoinDTO configDTO) throws ServiceException {
        validarCampos(configDTO);
        try {
            configDao.getCurrentSession().clear();
            ConfiguracaoIntegracaoJoin configEntity;
            if (Uteis.intNullOrEmpty(configDTO.getCodigo())) {
                configEntity = configAdapter.toEntity(configDTO);
                configEntity = configDao.save(configEntity);
            } else {
                configEntity = configAdapter.toEntity(configDTO);
                configEntity = configDao.update(configEntity);
            }
            ConfiguracaoIntegracaoJoinDTO dtoRetornar = configAdapter.toDto(configEntity);
            return dtoRetornar;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public void validarCampos(ConfiguracaoIntegracaoJoinDTO configDTO) throws ServiceException {
        if (configDTO.getEmpresa() == null || UteisValidacao.emptyNumber(configDTO.getEmpresa().getCodigo())) {
            throw new ServiceException(messageSource.getMessage("integracao.join.empresa.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (configDTO.getResponsavelPadrao() == null || UteisValidacao.emptyNumber(configDTO.getResponsavelPadrao().getCodigo())) {
            throw new ServiceException(messageSource.getMessage("integracao.join.responsavel.nao.informado", null, new Locale(requestService.getLocale())));
        }
        if (Uteis.nullOrEmpty(configDTO.getHoraLimite())) {
            throw new ServiceException(messageSource.getMessage("integracao.join.hora.limite.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (configDTO.getAcaoObjecao() == null) {
            throw new ServiceException(messageSource.getMessage("integracao.join.acao.objecao.nao.informado", null, new Locale(requestService.getLocale())));
        }
    }
}
