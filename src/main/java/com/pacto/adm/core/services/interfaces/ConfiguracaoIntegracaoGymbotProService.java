package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGymbotProDTO;
import com.pacto.adm.core.dto.integracoes.IntegracaoGymbotProDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface ConfiguracaoIntegracaoGymbotProService {

    List<ConfiguracaoIntegracaoGymbotProDTO> findByEmpresaId(Integer codigoEmpresa) throws Exception;

    ConfiguracaoIntegracaoGymbotProDTO  salvarConfiguracaoIntegracaoGymbotProo(List<ConfiguracaoIntegracaoGymbotProDTO> configDTO) throws ServiceException;

    String removerConfigGymbotPro(Integer codConfGymbotPro) throws ServiceException;

    String inativaOuAtivaConfigGymbotPro(Integer codConfGymbotPro) throws ServiceException;

    void enviarFluxoGymbotPro(IntegracaoGymbotProDTO codConfGymbotPro) throws ServiceException;

    boolean existeFluxoTelaClienteByEmpresa(String idEmpresa) throws ServiceException;
}
