package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.grupocolaborador.GrupoColaboradorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface GrupoColaboradorService {

    List<GrupoColaboradorDTO> montarGruposColaboradores() throws ServiceException;

    List<GrupoColaboradorDTO> montarGruposColaboradores(boolean validarPermissaoMetaFinanceira) throws ServiceException;
}
