package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dao.interfaces.NivelTurmaDao;
import com.pacto.adm.core.entities.contrato.NivelTurma;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.services.interfaces.NivelTurmaService;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

@Service
public class NivelTurmaServiceImpl implements NivelTurmaService {
    @Autowired
    NivelTurmaDao nivelTurmaDao;

    @Override
    public List<NivelTurma> findAll() throws Exception {
        return nivelTurmaDao.findAll();
    }

    public NivelTurma consultarPorCodigo(Integer codigo) throws ServiceException {
        try {
            return nivelTurmaDao.findById(codigo);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }
}
