package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.AtestadoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface AtestadoService {

    AtestadoDTO findById(Integer id) throws Exception;

    List<AtestadoDTO> findAllByMatricula(Integer matricula, PaginadorDTO paginadorDTO) throws Exception;

    AtestadoDTO saveOrUpdate(AtestadoDTO atestadoDTO, Integer matricula) throws ServiceException;

    void excluirAtestado(Integer codigoAtestado) throws Exception;

}
