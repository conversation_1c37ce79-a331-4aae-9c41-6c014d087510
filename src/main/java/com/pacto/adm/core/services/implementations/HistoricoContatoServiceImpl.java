package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dao.interfaces.HistoricoContatoDao;
import com.pacto.adm.core.services.interfaces.HistoricoContatoService;
import com.pacto.config.exceptions.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class HistoricoContatoServiceImpl implements HistoricoContatoService {

    @Autowired
    private HistoricoContatoDao historicoContatoDao;

    @Override
    public boolean buscarPorCodigoETipoFluxoGymBot(int codigoFluxo, int tipoGymBot) throws ServiceException {
        return historicoContatoDao.isFluxoGymBotJaUtilizado(codigoFluxo, tipoGymBot);
    }
}
