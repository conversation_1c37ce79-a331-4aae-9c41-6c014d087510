package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.indicacao.evento.EventoDTO;
import com.pacto.adm.core.entities.Evento;
import com.pacto.config.exceptions.ServiceException;

import java.util.Date;
import java.util.List;

public interface EventoService {

    List<Evento> findAll() throws Exception;

    List<EventoDTO> findAllToday() throws ServiceException;

    List<EventoDTO> findAllByDate(String descricao, Date date) throws ServiceException;
}
