package com.pacto.adm.core.services.implementations;
import com.pacto.adm.core.adapters.DescontoAdapter;
import com.pacto.adm.core.adapters.ProdutoAdapter;
import com.pacto.adm.core.dao.interfaces.DescontoDao;
import com.pacto.adm.core.dao.interfaces.PlanoDao;
import com.pacto.adm.core.dto.modalidade.ProdutoResumidoDTO;
import com.pacto.adm.core.dto.negociacao.DescontoDTO;
import com.pacto.adm.core.dto.negociacao.PlanoProdutoDTO;
import com.pacto.adm.core.entities.contrato.SituacaoContratoEnum;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.services.interfaces.ProdutoService;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.pacto.adm.core.dao.interfaces.ProdutoDao;
import com.pacto.adm.core.entities.Produto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.pacto.adm.core.dto.ProdutoDTO;

import com.pacto.adm.core.dao.interfaces.CategoriaDao;
import com.pacto.adm.core.services.interfaces.CategoriaService;

@Service
public class ProdutoServiceImpl implements ProdutoService {

    @Autowired
    private CategoriaDao categoriaDao;
    @Autowired
    private CategoriaService categoriaService;
    @Autowired
    private ProdutoDao produtoDao;
    @Autowired
    private DescontoDao descontoDao;
    @Autowired
    private PlanoDao planoDao;
    @Autowired
    private ProdutoAdapter produtoAdapter;
    @Autowired
    private DescontoAdapter descontoAdapter;

    public List<ProdutoDTO> produtosVenda(String quickSearch, String produtosAdicionados, Integer empresa) throws ServiceException {
        try {
            List<Produto> produtos;
            if(Uteis.nullOrEmpty(quickSearch) || quickSearch.equals("null")){
                produtos = produtoDao.ultimosVendidos(empresa, produtosAdicionados, 5);
            } else {
                produtos = produtoDao.findVendaByDescricao(quickSearch, null, empresa);
            }
            return produtoAdapter.toDtos(produtos);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ProdutoDTO> produtosPlano(String quickSearch, String produtosAdicionados, Integer plano, Integer empresa) throws ServiceException {
        try {
            if(Uteis.nullOrEmpty(quickSearch) || quickSearch.equals("null")){
                return new ArrayList<>();
            }
            List<Integer> produtosSugeridos = new ArrayList<>();
            if(!UteisValidacao.emptyNumber(plano)){
                List<PlanoProdutoDTO> planoProdutoDTOS = planoDao.produtodosPorPlano(plano, SituacaoContratoEnum.MA);
                if(planoProdutoDTOS != null && !planoProdutoDTOS.isEmpty()){
                    produtosSugeridos = planoProdutoDTOS.stream().filter(planoProdutoDTO -> !planoProdutoDTO.isObrigatorio()).map(PlanoProdutoDTO::getProduto).collect(Collectors.toList());
                }
            }
            List<Produto> produtos = produtoDao.findVendaByDescricao(quickSearch, produtosSugeridos, empresa);
            if(!Uteis.nullOrEmpty(produtosAdicionados)){
                List<Integer> produtosAdicionadosList = Arrays.stream(produtosAdicionados.split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                produtos = produtos.stream().filter(produto -> !produtosAdicionadosList.contains(produto.getCodigo())).collect(Collectors.toList());
            }
            return produtoAdapter.toDtos(produtos);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<DescontoDTO> descontosTipoProdutoVenda(String tipo, Integer empresa) throws ServiceException {
        try {
            return descontoAdapter.toDtos(descontoDao.consultarPorTipoProdutoPorEmpresa(tipo, empresa));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ProdutoResumidoDTO> findProdutosByListTipo() throws Exception {

        List<String> listaTipoProdutos = new ArrayList<String>();
        listaTipoProdutos.add("PM");
        listaTipoProdutos.add("SS");
        listaTipoProdutos.add("AA");
        listaTipoProdutos.add("AC");
        listaTipoProdutos.add("AH");
        listaTipoProdutos.add("CC");
        listaTipoProdutos.add("DE");
        listaTipoProdutos.add("DI");
        listaTipoProdutos.add("DR");
        listaTipoProdutos.add("DV");
        listaTipoProdutos.add("FR");
        listaTipoProdutos.add("MA");
        listaTipoProdutos.add("MM");
        listaTipoProdutos.add("QU");
        listaTipoProdutos.add("RD");
        listaTipoProdutos.add("RE");
        listaTipoProdutos.add("RN");
        listaTipoProdutos.add("TP");
        listaTipoProdutos.add("TR");
        listaTipoProdutos.add("MC");

        List<Produto> produtos = produtoDao.findProdutosByListTipo(listaTipoProdutos, 0, false);

        return produtos.stream().map(produto -> new ProdutoResumidoDTO(produto.getCodigo(), produto.getDescricao())).collect(Collectors.toList());
    }

    @Override
    public List<ProdutoDTO> findAllByTipoProdutoAtivo(String tipoProduto, Boolean ativo) throws ServiceException {
        try {
            return produtoAdapter.toDtos(produtoDao.findAllByTipoProdutoAtivo(tipoProduto, ativo));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }


    @Override
    public void criaProduto() throws Exception {
        Produto produto = new Produto();
        produto.setDescricao("PRODUTO GYMPASS");
        produto.setCategoriaProduto(categoriaDao.findByDescription("SERVIÇOS"));
        if (produto.getCategoriaProduto() == null) {
            categoriaService.criaCategoriaCasoNaoExista(produto);
        }
        produto.setTipoProduto("FR");
        produto.setNrDiasVigencia(1);
        produto.setValorFinal(0.0);

        produtoDao.save(produto);
    }

    public List<ProdutoDTO> findAllByTipoProduto(String tipoProduto) throws ServiceException {
        try {
            return produtoAdapter.toDtos(produtoDao.findAllByTipoProduto(tipoProduto));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
