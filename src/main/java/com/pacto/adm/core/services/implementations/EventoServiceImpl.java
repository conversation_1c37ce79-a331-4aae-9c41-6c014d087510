package com.pacto.adm.core.services.implementations;


import com.pacto.adm.core.adapters.evento.EventoAdapter;
import com.pacto.adm.core.dao.interfaces.EventoDao;
import com.pacto.adm.core.dao.interfaces.evento.EventoRepository;
import com.pacto.adm.core.dto.indicacao.evento.EventoDTO;
import com.pacto.adm.core.entities.Evento;
import com.pacto.adm.core.services.interfaces.EventoService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Calendario;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
@Service
public class EventoServiceImpl implements EventoService {

    private final EventoDao eventoDao;
    private final EventoAdapter eventoAdapter;
    private final EventoRepository eventoRepository;

    public EventoServiceImpl(EventoDao eventoDao, EventoAdapter eventoAdapter, EventoRepository eventoRepository) {
        this.eventoDao = eventoDao;
        this.eventoAdapter = eventoAdapter;
        this.eventoRepository = eventoRepository;
    }

    @Override
    public List<Evento> findAll() throws Exception {
        return eventoDao.findAll();
    }

    @Override
    public List<EventoDTO> findAllToday() throws ServiceException {
        return eventoAdapter.toDtos(eventoRepository.findAllByDescricaoAndSituacao(true, "", Calendario.hoje()));
    }

    @Override
    public List<EventoDTO> findAllByDate(String descricao, Date date) throws ServiceException {
        return eventoAdapter.toDtos(eventoRepository.findAllByDescricao( descricao, date));
    }
}
