package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.indicacao.IndicacaoAdapter;
import com.pacto.adm.core.adapters.indicado.IndicadoAdapter;
import com.pacto.adm.core.dao.interfaces.IndicacaoDao;
import com.pacto.adm.core.dao.interfaces.IndicadoDao;
import com.pacto.adm.core.dto.indicacao.IndicacaoDTO;
import com.pacto.adm.core.services.interfaces.IndicacaoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IndicacaoServiceImpl implements IndicacaoService {

    private IndicacaoDao indicacaoDao;
    private IndicacaoAdapter indicacaoAdapter;
    private IndicadoDao indicadoDao;
    private IndicadoAdapter indicadoAdapter;


    public IndicacaoServiceImpl(IndicacaoDao indicacaoDao, IndicacaoAdapter indicacaoAdapter,
                                IndicadoDao indicadoDao, IndicadoAdapter indicadoAdapter) {
        this.indicacaoDao = indicacaoDao;
        this.indicacaoAdapter = indicacaoAdapter;
        this.indicadoDao = indicadoDao;
        this.indicadoAdapter = indicadoAdapter;
    }

    public List<IndicacaoDTO> findByMatricula(Integer matricula, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<IndicacaoDTO> listaDTO = indicacaoAdapter.toDtos(indicacaoDao.findByMatricula(matricula, paginadorDTO));
            for (IndicacaoDTO indicacao : listaDTO) {
                try {
                    indicacao.setIndicados(indicadoAdapter.toDtos(indicadoDao.findByIndicacao(indicacao.getCodigo(), paginadorDTO)));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            return listaDTO;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }
}
