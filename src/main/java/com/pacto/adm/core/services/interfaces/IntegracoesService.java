package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.filtros.FiltroIntegracoesJSON;
import com.pacto.adm.core.dto.filtros.FiltroProcessarConciliadoraJSON;
import com.pacto.adm.core.dto.integracaomanychat.TagDTO;
import com.pacto.adm.core.dto.integracoes.*;
import com.pacto.adm.core.entities.kobana.IntegracaoKobana;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface IntegracoesService {

    ConfiguracoesIntegracoesEmpresasDTO findAllByFilters(FiltroIntegracoesJSON filtros) throws Exception;
    void salvarConfiguracaoIntegracaoMyWellness(ConfiguracaoIntegracaoMyWellnessDTO configuracaoIntegracaoMyWellnessDTO) throws ServiceException;

    void salvarConfiguracaoIntegracaoMentorWeb(ConfiguracaoIntegracaoMentorWebDTO configuracaoIntegracaoMentorWebDTO) throws ServiceException;

     void salvarConfiguracaoIntegracaoEstacionamento(ConfiguracaoIntegracaoEstacionamentoDTO configuracaoIntegracaoEstacionamentoDTO) throws ServiceException;
     void testarConfiguracaoIntegracaoEstacionamento(ConfiguracaoIntegracaoEstacionamentoDTO configuracaoIntegracaoEstacionamentoDTO) throws ServiceException;
     void salvarConfiguracaoIntegracaoCDLSPC(ConfiguracaoIntegracaoCDLSPCDTO configuracaoIntegracaoCDLSPCDTO) throws ServiceException;

     void salvarConfiguracaoIntegracaoDelsoft(ConfiguracaoIntegracaoDelsoftDTO configuracaoIntegracaoDelSoftDTO) throws ServiceException;

     void salvarConfiguracaoIntegracaoVitio(ConfiguracaoIntegracaoVitioDTO configuracaoIntegracaoVitioDTO) throws ServiceException;

     void salvarConfiguracaoIntegracaoParceiroFidelidade(ConfiguracaoIntegracaoParceiroFidelidadeDTO configuracaoIntegracaoParceiroFidelidadeDTO) throws ServiceException;

    void salvarConfiguracaoIntegracaoNotificacaoWebhook(ConfiguracaoIntegracaoNotificacaoWebhookDTO configuracaoIntegracaoNotificacaoWebhookDTO) throws ServiceException;

    void salvarConfiguracaoIntegracaoAmigoFit(ConfiguracaoIntegracaoAmigoFitDTO configuracaoIntegracaoAmigoFitDTO) throws ServiceException;

    void salvarConfiguracaoIntegracaoWeHelp(ConfiguracaoIntegracaoWeHelpDTO configWeHelpDTO) throws ServiceException;

    List<ConfiguracaoIntegracaoPluggyDTO> consultarConfiguracoesIntegracoesPluggy(int empresa) throws Exception;

    IntegracaoKobana consultarConfiguracaoIntegracaoKobana(int empresa) throws Exception;

    ConfiguracaoIntegracaoGymPassDTO consultarConfiguracoesIntegracoesGymPass(Integer empresa) throws Exception;
    List<ConfiguracaoIntegracaoGymPassDTO> consultarConfiguracoesIntegracoesGymPass() throws  Exception;

    ConfiguracaoIntegracaoGoGoodDTO consultarConfiguracoesIntegracoesGoGood(Integer empresa) throws Exception;

    List<ConfiguracaoIntegracaoGoGoodDTO> consultarConfiguracoesIntegracoesGogood() throws Exception;

    void salvarConfiguracaoIntegracaoGympass(ConfiguracaoIntegracaoGymPassDTO configuracaoIntegracaoGymPassDTO) throws ServiceException;

    void salvarConfiguracaoIntegracaoGoGood(ConfiguracaoIntegracaoGoGoodDTO integracaoGoGoodDTO) throws ServiceException;

    ConfiguracaoIntegracaoTotalPassDTO consultarConfiguracoesIntegracoesTotalPass(Integer empresa) throws Exception;
    List<ConfiguracaoIntegracaoTotalPassDTO> consultarConfiguracoesIntegracoesTotalPass() throws  Exception;

    void salvarConfiguracaoIntegracaoTotalpass(ConfiguracaoIntegracaoTotalPassDTO configuracaoIntegracaoTotalPassDTO) throws ServiceException;

    List<ConfiguracaoIntegracaoSpiviDTO> consultarConfiguracoesIntegracoesSpivi() throws  Exception;

    void salvarConfiguracaoIntegracaoSpivi(ConfiguracaoIntegracaoSpiviDTO configuracaoIntegracaoSpiviDTO) throws ServiceException;

    void salvarConfiguracaoIntegracaoF360(ConfiguracaoIntegracaoF360RelatorioDTO configuracaoIntegracaoF360RelatorioDTO) throws ServiceException;

    void salvarConfiguracaoIntegracaoConciliadora(ConfiguracaoIntegracaoConciliadoraDTO configuracaoIntegracaoConciliadoraDTO) throws ServiceException;

    void processarConciliadora(FiltroProcessarConciliadoraJSON filtros) throws Exception;

    void salvarConfiguracaoIntegracaoSescDf(ConfiguracaoIntegracaoSescDfDTO configuracaoIntegracaoSescDfDTO) throws ServiceException;

    void estornarReciboNaConciliadora(Integer codigoRecibo) throws Exception;

    void salvarConfiguracaoIntegracaoSms(ConfiguracaoIntegracaoSmsDTO configuracaoIntegracaoSmsDTO) throws ServiceException;

    void salvarConfiguracaoIntegracaoSistemaContabilAlterData(ConfiguracaoIntegracaoSistemaContabilAlterDataDTO configuracaoIntegracaoAlterDataDTO) throws ServiceException;
    void salvarConfiguracaoIntegracaoRecursosFacilitePay(ConfiguracaoIntegracaoRecursosFacilitePayDTO configDTO) throws ServiceException;
    void salvarConfiguracaoIntegracaoNuvemshop(ConfiguracaoIntegracaoNuvemshopDTO configDTO) throws ServiceException;
    void salvarConfiguracaoIntegracaoManyChat(ConfiguracaoIntegracaoManyChatDTO configDTO) throws ServiceException;

    List<TagDTO> consultarTagsManyChat(Integer empresa) throws ServiceException;
}
