package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dto.base.RedeEmpresaDTO;
import com.pacto.adm.core.services.interfaces.RedeEmpresaService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.JSONMapper;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class RedeEmpresaServiceImpl implements RedeEmpresaService {

    @Value("${oamd.app.url}")
    private String urlOamd;
    private final HttpServico httpServico;
    private final RequestService requestService;
    private final Map<String, RedeEmpresaDTO> mapaRedeEmpresa = new HashMap<>();

    public RedeEmpresaServiceImpl(HttpServico httpServico, RequestService requestService) {
        this.httpServico = httpServico;
        this.requestService = requestService;
    }

    public RedeEmpresaDTO obterRedeEmpresaPorChaveEmpresa(String chaveEmpresa) throws ServiceException {
        try {
            RedeEmpresaDTO redeEmpresaDTO = null;
            if (mapaRedeEmpresa.get(chaveEmpresa) != null) {
                return mapaRedeEmpresa.get(chaveEmpresa);
            }

            ResponseEntity<String> response = httpServico.doJson(
                    urlOamd + "/prest/empresaFinanceiro/consultarRede?key=" + chaveEmpresa,
                    null, HttpMethod.GET, requestService.getToken());
            JSONObject jsonResponse = new JSONObject(response.getBody());
            if (jsonResponse.has("return") && jsonResponse.optJSONObject("return") != null) {
                redeEmpresaDTO = JSONMapper.getObject(jsonResponse.getJSONObject("return"), RedeEmpresaDTO.class);
                mapaRedeEmpresa.put(chaveEmpresa, redeEmpresaDTO);
            }
            return redeEmpresaDTO;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("Falha ao obter rede empresa: " + e.getMessage());
        }
    }

    public void limparCache() {
        mapaRedeEmpresa.clear();
    }

}
