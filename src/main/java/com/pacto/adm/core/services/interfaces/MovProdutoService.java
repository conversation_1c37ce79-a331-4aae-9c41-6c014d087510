package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.AplicacaoDescontoTotaisDTO;
import com.pacto.adm.core.dto.MovProdutoDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroMovProdutoJSON;
import com.pacto.adm.core.entities.MovParcela;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.objects.EstornoMovProduto;

import java.util.List;

public interface MovProdutoService {

    List<MovProdutoDTO> findAllByCodPessoa(Integer codPessoa, FiltroMovProdutoJSON filtroMovProdutoJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    List<MovProdutoDTO> findAllByCodContrato(Integer contrato, PaginadorDTO paginadorDTO) throws ServiceException;

    List<MovProdutoDTO> findAllByCodRecibo(Integer codRecibo) throws ServiceException;

    List<MovProdutoDTO> findAllByCodParcela(Integer codParcela, PaginadorDTO paginadorDTO) throws ServiceException;

    void estornarMovProduto(EstornoMovProduto obj, MovParcela parcela, String key) throws Exception;

    MovProdutoDTO details(Integer codigoProduto) throws ServiceException;

    List<MovProdutoDTO> consultarProdutoComValidadePorCodigoPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws ServiceException;

    List<MovProdutoDTO> consultarMovProdutosPorCodigoPessoa(Integer codPessoa, FiltroMovProdutoJSON filtroMovProdutoJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    AplicacaoDescontoTotaisDTO findAllAplicacaoDesconto(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws ServiceException;
}
