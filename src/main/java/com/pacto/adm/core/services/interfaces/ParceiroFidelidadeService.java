package com.pacto.adm.core.services.interfaces;
import com.pacto.adm.core.dto.integracoes.ParceiroFidelidadeDTO;
import com.pacto.config.exceptions.ServiceException;


public interface ParceiroFidelidadeService {

    ParceiroFidelidadeDTO findByEmpresaId(Integer empresaId) throws Exception;

    ParceiroFidelidadeDTO saveOrUpdate(ParceiroFidelidadeDTO parceiroFidelidadeDTO) throws ServiceException;

}
