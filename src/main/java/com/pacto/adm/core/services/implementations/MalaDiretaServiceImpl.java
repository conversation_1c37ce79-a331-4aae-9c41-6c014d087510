package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dao.interfaces.MalaDiretaDao;
import com.pacto.adm.core.entities.MalaDireta;
import com.pacto.adm.core.services.interfaces.MalaDiretaService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Logger;

@Service
public class MalaDiretaServiceImpl implements MalaDiretaService {

    private static final int MAXIMO_RESULTADOS = 10;
    private final MalaDiretaDao malaDiretaDao;

    private Logger logger = Logger.getLogger(MalaDiretaServiceImpl.class.getName());

    public MalaDiretaServiceImpl(MalaDiretaDao malaDiretaDao) {
        this.malaDiretaDao = malaDiretaDao;
    }

    public void limparUrlFluxoBotConversa(String urlWebhooBotConversa) throws Exception {
        Map<String, Object> param = new HashMap<>();
        param.put("urlwebhoobotconversa", urlWebhooBotConversa);
        List<MalaDireta> malings = malaDiretaDao.findByParam(new StringBuilder("where obj.urlWebhooBotConversa = :urlwebhoobotconversa"), param);
        if (malings != null && !malings.isEmpty()) {
            malings.forEach(m -> {
                m.setUrlWebhooBotConversa(null);
                try {
                    malaDiretaDao.update(m);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
    }
}
