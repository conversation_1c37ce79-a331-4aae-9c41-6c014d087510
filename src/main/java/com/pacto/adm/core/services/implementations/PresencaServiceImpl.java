package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.dao.interfaces.MatriculaAlunoHorarioTurmaDao;
import com.pacto.adm.core.dao.interfaces.PresencaDao;
import com.pacto.adm.core.entities.contrato.MatriculaAlunoHorarioTurma;
import com.pacto.adm.core.entities.contrato.Presenca;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.services.interfaces.PresencaService;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class PresencaServiceImpl implements PresencaService {
    @Autowired
    PresencaDao presencaDao;
    @Autowired
    MatriculaAlunoHorarioTurmaDao matriculaAlunoHorarioTurmaDao;

    @Override
    public List<Presenca> listaPresenca(Integer codMatricula) throws ServiceException {
        List<Presenca> presencas = new ArrayList<>();
        Map<Integer, MatriculaAlunoHorarioTurma> map = new HashMap<>();
        try (SessionImplementor sessionImplementor = presencaDao.createSessionCurrentWork()){
            sessionImplementor.doWork(connection -> {
                String sql = " SELECT * from presenca where dadosturma = " + codMatricula;
                try {
                    ResultSet rs = presencaDao.createStatement(connection, sql);
                    while (rs.next()) {
                        Presenca obj = new Presenca();
                        obj.setCodigo(rs.getInt("codigo"));

                        Integer coddadosturma = rs.getInt("dadosturma");
                        MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurma = map.get(coddadosturma);
                        if (matriculaAlunoHorarioTurma == null) {
                            matriculaAlunoHorarioTurma = matriculaAlunoHorarioTurmaDao.findById(coddadosturma);
                            map.put(coddadosturma, matriculaAlunoHorarioTurma);
                        }
                        obj.setDadosTurma(matriculaAlunoHorarioTurma);
                        obj.setDataChamada(rs.getDate("datachamada"));
                        obj.setDataPresenca(rs.getDate("datapresenca"));
                        presencas.add(obj);
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    connection.close();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        if (!presencas.isEmpty()) {
            return presencas;
        }
        return null;
    }
}
