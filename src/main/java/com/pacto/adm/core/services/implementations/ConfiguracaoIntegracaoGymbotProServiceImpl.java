package com.pacto.adm.core.services.implementations;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pacto.adm.core.adapters.integracoes.ConfiguracaoIntegracaoGymbotProAdapter;
import com.pacto.adm.core.dao.interfaces.ConfiguracaoIntegracaoGymbotProDao;
import com.pacto.adm.core.dao.interfaces.HistoricoContatoDao;
import com.pacto.adm.core.dao.interfaces.MalaDiretaDao;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGymbotProDTO;
import com.pacto.adm.core.dto.integracoes.IntegracaoGymbotProDTO;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoGymbotPro;
import com.pacto.adm.core.enumerador.contato.TipoGymBotEnum;
import com.pacto.adm.core.services.interfaces.ConfiguracaoIntegracaoGymbotProService;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.adm.core.services.interfaces.MalaDiretaService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.UteisValidacao;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
public class ConfiguracaoIntegracaoGymbotProServiceImpl implements ConfiguracaoIntegracaoGymbotProService {

    private final ConfiguracaoIntegracaoGymbotProDao configDao;
    private final MalaDiretaService malaDiretaService;
    private final ConfiguracaoIntegracaoGymbotProAdapter configAdapter;
    private final HttpServico httpServico;
    private final RequestService requestService;
    private final MessageSource messageSource;
    private final MalaDiretaDao malaDiretaDao;
    private final HistoricoContatoDao historicoContatoDao;

    @Autowired
    private DiscoveryService discoveryService;

    public ConfiguracaoIntegracaoGymbotProServiceImpl(ConfiguracaoIntegracaoGymbotProDao configDao, MalaDiretaService malaDiretaService, ConfiguracaoIntegracaoGymbotProAdapter configAdapter, HttpServico httpServico, RequestService requestService, MessageSource messageSource, MalaDiretaDao malaDiretaDao, HistoricoContatoDao historicoContatoDao) {
        this.configDao = configDao;
        this.malaDiretaService = malaDiretaService;
        this.configAdapter = configAdapter;
        this.httpServico = httpServico;
        this.requestService = requestService;
        this.messageSource = messageSource;
        this.malaDiretaDao = malaDiretaDao;
        this.historicoContatoDao = historicoContatoDao;
    }

    public List<ConfiguracaoIntegracaoGymbotProDTO> findByEmpresaId(Integer codigoEmpresa) throws Exception{
       List<ConfiguracaoIntegracaoGymbotPro> configEntity = configDao.findByEmpresaId(codigoEmpresa);
        List<ConfiguracaoIntegracaoGymbotProDTO> configDTO = new ArrayList<>();
       if (configEntity != null) {
           configDTO = configAdapter.toDtos(configEntity);
       }
       return configDTO;
   }

    public ConfiguracaoIntegracaoGymbotProDTO salvarConfiguracaoIntegracaoGymbotProo(List<ConfiguracaoIntegracaoGymbotProDTO> configDTO) throws ServiceException{
        try {
            ConfiguracaoIntegracaoGymbotPro configEntity  = new ConfiguracaoIntegracaoGymbotPro();
            ExecutorService executor = Executors.newCachedThreadPool();
            for (ConfiguracaoIntegracaoGymbotProDTO dto : configDTO) {
                configEntity = configAdapter.toEntity(dto);
                if (configEntity.getCodigo() != null && configEntity.getCodigo() > 0) {
                    configEntity = configDao.update(configEntity);
                } else {
                    configEntity = configDao.save(configAdapter.toEntity(dto));
                }
            }
            return configAdapter.toDto(configEntity);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public String inativaOuAtivaConfigGymbotPro(Integer codConfGymbotPro) throws ServiceException {
        try {
            ConfiguracaoIntegracaoGymbotPro config = configDao.findById(codConfGymbotPro);
            config.setAtivo(!config.getAtivo());
            configDao.update(config);
            return "OK";
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public String removerConfigGymbotPro(Integer codConfGymbotPro) throws ServiceException {
        try {
            ConfiguracaoIntegracaoGymbotPro config = configDao.findById(codConfGymbotPro);
            boolean emUso = true;
            String mensagemRetorno = "";
            if (config != null && config.getTipoFluxo() != null) {
                if (config.getTipoFluxo() == 1 || config.getTipoFluxo() == 2){
                    emUso = historicoContatoDao.isFluxoGymBotJaUtilizado(config.getCodigo(), TipoGymBotEnum.GYMBOT_PRO.getCodigo());
                    if (config.getTipoFluxo() == 1) {
                        mensagemRetorno = "Identificador sendo utilizado na Fase CRM";
                    }else{
                        mensagemRetorno = "Identificador sendo utilizado na Tela do Cliente";
                    }
                }else{
                    emUso =  malaDiretaDao.utilizandoFluxoGymbotPro(config.getCodigo());
                    mensagemRetorno = "Identificador sendo utilizado no Contato em Grupo";
                }
            }

            if (!emUso){
                configDao.delete(config);
                return "OK";
            }
            return mensagemRetorno;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public boolean existeFluxoTelaClienteByEmpresa(String idEmpresa) throws ServiceException {
        try {
             String urlFluxo = configDao.findbyFluxoTelaAluno(idEmpresa);

             if (UteisValidacao.emptyString(urlFluxo)){
                  return false;
             }else {
                 return true;
             }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }


    public void enviarFluxoGymbotPro(IntegracaoGymbotProDTO botConversaDTO){
        try {
            String urlFluxoAluno = configDao.findbyFluxoTelaAluno(botConversaDTO.getIdEmpresa());
            botConversaDTO.setUrlWebhookGenericoGymbot(urlFluxoAluno);

            StringBuilder urlZw = new StringBuilder(discoveryService.getClientDiscovery().getServiceUrls().getZwUrl()).append("/prest/integracao/botConversa")
                    .append("?key=").append(requestService.getUsuarioAtual().getChave())
                    .append("&op=dispararFluxo");

            ObjectMapper objectMapper = new ObjectMapper();

            ResponseEntity<String> responseZw = httpServico.doJson(
                    urlZw.toString(),
                    objectMapper.writeValueAsString(botConversaDTO), HttpMethod.POST, "");

            JSONObject jsonObject = new JSONObject(responseZw);
            if (!jsonObject.optString("erro").isEmpty()) {
                String msg = "Falha ao Disparar Fluxo Gymbot! " + jsonObject.optString("erro");
                System.out.println(msg);
                throw new ServiceException(msg);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
