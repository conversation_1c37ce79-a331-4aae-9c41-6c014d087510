package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.FornecedorAdapter;
import com.pacto.adm.core.dao.interfaces.FornecedorDao;
import com.pacto.adm.core.dto.FornecedorDTO;
import com.pacto.adm.core.entities.Fornecedor;
import com.pacto.adm.core.services.interfaces.FornecedorService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class FornecedorServiceImpl implements FornecedorService {

    @Autowired
    private FornecedorDao fornecedorDao;

    @Autowired
    private FornecedorAdapter fornecedorAdapter;

    @Override
    public List<FornecedorDTO> findAllEmpresasFornecedorSesi(PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<FornecedorDTO> empsDTO = new ArrayList<>();
            List<Fornecedor> empresas = fornecedorDao.findAllByAtivo(paginadorDTO);
            if (empresas != null && !empresas.isEmpty()) {
                for (Fornecedor emp : empresas) {
                    empsDTO.add(fornecedorAdapter.toDto(emp));
                }
            }
            return empsDTO;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
