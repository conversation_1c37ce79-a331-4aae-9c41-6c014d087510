package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.ClienteRestricaoDTO;
import com.pacto.adm.core.dto.filtros.FiltroClienteRestricaoJSON;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;

import java.util.List;

public interface ClienteRestricaoService {

    List<ClienteRestricaoDTO> findAll(FiltroClienteRestricaoJSON filtrosJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    List<ClienteRestricaoDTO> findByCpf(String cpf) throws Exception;

    List<ClienteRestricaoDTO> findByCodigoMatricula(Integer codigoMatricula) throws Exception;

    void create(ClienteRestricaoDTO clienteRestricaoDTO) throws Exception;

    void delete(String cpf, String chaveEmpresa, Integer codigoEmpresa, String tipoRestricao) throws Exception;

    void delete(List<String> cpfs, String chaveEmpresa, Integer codigoEmpresa, String tipoRestricao) throws Exception;

    void atualizarRestricoesDeInadimplenciaSync(String cpf) throws Exception;

}
