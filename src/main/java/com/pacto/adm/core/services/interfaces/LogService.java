package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.ClienteDadosGymPassDTO;
import com.pacto.adm.core.dto.LogDTO;
import com.pacto.adm.core.dto.PeriodoAcessoClienteDTO;
import com.pacto.adm.core.dto.filtros.FiltroLogJSON;
import com.pacto.adm.core.entities.ClienteObservacao;
import com.pacto.adm.core.entities.Pessoa;
import com.pacto.adm.core.entities.contrato.PeriodoAcessoCliente;
import com.pacto.adm.core.entities.financeiro.VendaAvulsa;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroLogClienteJSON;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.contrato.ContratoOperacao;
import com.pacto.config.exceptions.ServiceException;

import javax.xml.crypto.Data;
import java.util.Date;
import java.util.List;

public interface LogService {

    void incluirLogInclusaoAlteracao(Object objAlterado, Object objAnterior, String nomeEntidade, String nomeEntidadeDescricao) throws Exception;

    void incluirLogInclusaoAlteracao(Object objAlterado, Object objAnterior, String nomeEntidade, String nomeEntidadeDescricao, Integer codigoPessoa) throws Exception;

    void incluirLogExcluscao(Object planoAlterado, String nomeEntidade, String nomeEntidadeDescricao) throws Exception;

    void incluirLogExcluscao(Object planoAlterado, String nomeEntidade, String nomeEntidadeDescricao, Integer codigoPessoa) throws Exception;

    void incluirLogEstornoContratoOperacao(ContratoOperacao contratoOperacao) throws Exception;

    List<LogDTO> consultarPorPessoa(Integer pessoa, PaginadorDTO paginadorDTO) throws ServiceException;

    void incluirLogGymPass(ClienteDadosGymPassDTO clienteDadosGymPassDTO, Cliente cliente) throws Exception;

    void incluirLogClienteObservacao(ClienteObservacao clienteObservacaoAnterior, ClienteObservacao clienteObservacaoAlterado, Pessoa pessoa, boolean isExcluir) throws Exception;

    void incluirLogFalhaIntegracaoSescGO(Integer codigoPessoa, String errorMessage);

    List<LogDTO> findLogsByNomeEntidadeCodPessoaChavePrimaria(String nomeEntidade, Integer chavePrimaria, Integer pessoa, PaginadorDTO paginadorDTO, FiltroLogClienteJSON filtros) throws ServiceException;

    void registrarLogErro(String nomeEntidade, int codPessoa, String msg, String responsavel);

    List<LogDTO> consultarPorEntidadeDataAlteracaoOperacaoComDadosCliente(FiltroLogJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogDTO> consultarPorNomeEntidadeEmpresaClientePeriodoAgrupado(FiltroLogJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    void save(LogDTO logDTO) throws Exception;

    void incluirLogVendaAvulsa(VendaAvulsa vendaAvulsa) throws Exception;

    List<LogDTO> consultarPorEntidade(String entidade, FiltroLogClienteJSON filtros, String chavePrimaria, PaginadorDTO paginadorDTO) throws Exception;

    List<LogDTO> consultarPorEntidadeFiltros(String nomeEntidade, FiltroLogClienteJSON  filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogDTO> consultarPorEntidadeDataAlteracaoOperacao(FiltroLogJSON filtros, boolean buscarComAdministrador, PaginadorDTO paginadorDTO) throws ServiceException;

    void incluirLogInclusaoFreepass(Cliente cliente, PeriodoAcessoCliente periodoAcessoCliente) throws Exception;

    void incluirLogExclusaoFreepass(Cliente cliente, PeriodoAcessoClienteDTO periodoAcessoCliente) throws Exception;

}
