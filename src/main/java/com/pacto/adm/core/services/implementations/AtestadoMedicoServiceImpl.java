package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.dao.interfaces.ClienteDao;
import com.pacto.adm.core.dto.*;
import com.pacto.adm.core.enumerador.TipoAtestadoMedicoEnum;
import com.pacto.adm.core.mscomunication.mediams.MediaMs;
import com.pacto.adm.core.services.interfaces.AtestadoMedicoService;
import com.pacto.adm.core.services.interfaces.ContratoOperacaoService;
import com.pacto.adm.core.services.interfaces.UsuarioService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Ordenacao;
import com.pacto.config.utils.Uteis;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

@Service
public class AtestadoMedicoServiceImpl implements AtestadoMedicoService {

    @Autowired
    private ClienteDao clienteDao;
    @Autowired
    private AtestadoServiceImpl atestadoService;
    @Autowired
    private ContratoOperacaoService contratoOperacaoService;
    @Autowired
    private MediaMs mediaMs;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private UsuarioAdapter usuarioAdapter;

    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<AtestadoMedicoDTO> findAllByMatricula(Integer matricula, PaginadorDTO paginadorDTO) throws Exception {
        List<AtestadoMedicoDTO> atestadoDTOS = montarListaAtestadosMedicos(matricula, paginadorDTO);
        Ordenacao.ordenarListaReverse(atestadoDTOS, "dataLancamento");
        return atestadoDTOS;
    }

    private List<AtestadoMedicoDTO> montarListaAtestadosMedicos(Integer matricula, PaginadorDTO paginadorDTO) throws ServiceException {
        List<AtestadoMedicoDTO> atestadoMedicoDTOS = new ArrayList<>();
        try {
            clienteDao.getCurrentSession().clear();
            try (SessionImplementor sessionImplementor = clienteDao.createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        String sql = gerarSqlAtestadosMedicos(matricula, paginadorDTO);
                        ResultSet rsAtestados = clienteDao.createStatement(connection, sql);

                        while (rsAtestados.next()) {
                            Integer pessoa = rsAtestados.getInt("pessoa");
                            AtestadoMedicoDTO atestadoMedicoDTO = new AtestadoMedicoDTO();
                            TipoAtestadoMedicoEnum tipoAtestadoMedicoEnum = TipoAtestadoMedicoEnum.getTipoAtestadoMedico(rsAtestados.getInt("tipoatestado"));
                            atestadoMedicoDTO.setTipoAtestado(tipoAtestadoMedicoEnum != null ? tipoAtestadoMedicoEnum.getCodigo() : null);
                            atestadoMedicoDTO.setDataLancamento(rsAtestados.getTimestamp("datalancamento"));
                            if (tipoAtestadoMedicoEnum == TipoAtestadoMedicoEnum.ATESTADO_APTIDAO_FISICA) {
                                AtestadoDTO atestadoDTO = atestadoService.findById(rsAtestados.getInt("atestado"));
                                UsuarioDTO usuarioDTO = usuarioAdapter.toDto(usuarioService.buscarUsuarioPorCodigo(rsAtestados.getInt("responsaveloperacao")));
                                AtestadoContratoDTO atestadoContratoDTO = new AtestadoContratoDTO();
                                atestadoContratoDTO.setResponsavelOperacao(usuarioDTO);
                                atestadoMedicoDTO.setVigenciaPorExtenso(diferencaEntreDatasPorExtenso(atestadoDTO.getMovProduto().getDataInicioVigencia(), atestadoDTO.getMovProduto().getDataFinalVigencia()));
                                atestadoMedicoDTO.setUrlArquivo(atestadoDTO.getUrlArquivo());
                                atestadoMedicoDTO.setAtestadoAptidaoFisica(atestadoDTO);
                                atestadoMedicoDTO.setAtestadoContrato(atestadoContratoDTO);
                                if (atestadoDTO.getArquivo() != null) {
                                    if (atestadoDTO.getArquivo().getNome() == null) {
                                        String nomeArquivo = atestadoDTO.getArquivo().getCodigo() + "-" + pessoa;
                                        atestadoMedicoDTO.setNomeArquivo(nomeArquivo);
                                    } else {
                                        atestadoMedicoDTO.setNomeArquivo(atestadoDTO.getArquivo().getNome());
                                    }
                                    if (atestadoDTO.getArquivo().getExtensao() == null) {
                                        atestadoMedicoDTO.setFormatoArquivo("");
                                    } else {
                                        atestadoMedicoDTO.setFormatoArquivo(atestadoDTO.getArquivo().getExtensao());
                                    }
                                }
                            } else {
                                AtestadoContratoDTO atestadoContratoDTO = new AtestadoContratoDTO();
                                ContratoOperacaoDTO contratoOperacaoDTO = contratoOperacaoService.findById(rsAtestados.getInt("atestado"));
                                atestadoMedicoDTO.setVigenciaPorExtenso(diferencaEntreDatasPorExtenso(contratoOperacaoDTO.getDataInicioEfetivacaoOperacao(), contratoOperacaoDTO.getDataFimEfetivacaoOperacao()));
                                if (!contratoOperacaoDTO.getChaveArquivo().isEmpty()) {
                                    atestadoMedicoDTO.setUrlArquivo(obterUrlArquivo(contratoOperacaoDTO.getChaveArquivo()));
                                } else {
                                    atestadoMedicoDTO.setUrlArquivo("");
                                }
                                atestadoMedicoDTO.setCodigoContrato(contratoOperacaoDTO.getContrato().getCodigo());
                                atestadoContratoDTO.setContratoOperacao(contratoOperacaoDTO);
                                atestadoMedicoDTO.setAtestadoContrato(atestadoContratoDTO);
                                atestadoContratoDTO.setJustificativaOperacao(contratoOperacaoDTO.getTipoJustificativa());
                                if (!Uteis.nullOrEmpty(contratoOperacaoDTO.getChaveArquivo())) {
                                    if (contratoOperacaoDTO.getNomeArquivo() == null) {
                                        String nomeArquivo = atestadoMedicoDTO.getAtestadoContrato().getContratoOperacao().getCodigo() + "-" + pessoa;
                                        contratoOperacaoDTO.setNomeArquivo(nomeArquivo);
                                        atestadoMedicoDTO.setNomeArquivo(nomeArquivo);
                                    } else {
                                        atestadoMedicoDTO.setNomeArquivo(contratoOperacaoDTO.getNomeArquivo());
                                    }
                                    if (contratoOperacaoDTO.getFormatoArquivo() == null) {
                                        String formatoArquivo = contratoOperacaoDTO.getChaveArquivo().substring(contratoOperacaoDTO.getChaveArquivo().lastIndexOf("."));
                                        contratoOperacaoDTO.setFormatoArquivo(formatoArquivo);
                                        atestadoMedicoDTO.setFormatoArquivo(formatoArquivo);
                                    } else {
                                        atestadoMedicoDTO.setFormatoArquivo(contratoOperacaoDTO.getFormatoArquivo());
                                    }
                                }
                            }
                            atestadoMedicoDTOS.add(atestadoMedicoDTO);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        return atestadoMedicoDTOS;
    }

    public static String diferencaEntreDatasPorExtenso(Date dataInicial, Date dataFinal) {
        Long nrDiasEntreDatas = Uteis.nrDiasEntreDatas(dataInicial, dataFinal);
        if (nrDiasEntreDatas < 30L) {
            return nrDiasEntreDatas + (nrDiasEntreDatas == 1L ? " dia" : " dias");
        } else if (nrDiasEntreDatas >= 30L && nrDiasEntreDatas < 365L) {
            Long nrMesesEntreDatas = nrDiasEntreDatas / 30L;
            return nrMesesEntreDatas + (nrMesesEntreDatas == 1L ? " mês" : " meses");
        } else if (nrDiasEntreDatas >= 365L) {
            Long nrAnosEntreDatas = nrDiasEntreDatas / 365L;
            return nrAnosEntreDatas + (nrAnosEntreDatas == 1L ? " ano" : " anos");
        } else {
            return "";
        }
    }

    private String gerarSqlAtestadosMedicos(Integer matricula, PaginadorDTO paginadorDTO) throws ServiceException {
        StringBuilder sql = new StringBuilder();
        sql.append("select sql.cliente, sql.pessoa, sql.codigomatricula,sql.atestado,sql.datalancamento,sql.tipoatestado, sql.contrato, sql.responsaveloperacao \n");
        sql.append(" from (");
        sql.append("select \n" +
                " cli.codigo as cliente, cli.pessoa, cli.codigomatricula,\n" +
                " a.codigo as atestado, \n" +
                " case when a.dataregistro is not null then a.dataregistro else mpro.datalancamento end as datalancamento,\n" +
                " 1 as tipoatestado,\n" +
                " 0 as contrato,\n" +
                " mpro.responsavellancamento as responsaveloperacao\n" +
                " from cliente cli\n" +
                " inner join movproduto mpro on mpro.pessoa = cli.pessoa \n" +
                " inner join atestado a on a.movproduto = mpro.codigo \n" +
                " where 1 = 1");
        sql.append(" and cli.codigomatricula = ").append(matricula);
        sql.append(" union ");
        sql.append(" select \n" +
                " cli.codigo as cliente, cli.pessoa, cli.codigomatricula,\n" +
                " conop.codigo as atestado, \n" +
                " conop.dataoperacao as datalancamento,\n" +
                " 2 as tipoatestado,\n" +
                " conop.contrato,\n" +
                " conop.responsavel as responsaveloperacao\n" +
                " from cliente cli\n" +
                " inner join contrato con on con.pessoa = cli.pessoa \n" +
                " inner join contratooperacao conop on conop.contrato = con.codigo and conop.tipooperacao = 'AT'\n" +
                " where 1 = 1\n");
        sql.append(" and cli.codigomatricula = " + matricula);
        sql.append(") as sql \n");
        sql.append(" order by sql.datalancamento");

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countResults(sql.toString()));
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
        }

        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() != null) {
                sql.append(" offset " + paginadorDTO.getPage());
            }
            if (paginadorDTO.getSize() != null) {
                sql.append(" limit " + paginadorDTO.getSize());
            }
        }
        return sql.toString();
    }

    private Long countResults(String sql) throws ServiceException {
        try {
            AtomicLong totalResults = new AtomicLong();
            try (SessionImplementor sessionImplementor = clienteDao.createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        StringBuilder sqlCount = new StringBuilder();
                        sqlCount.append(" SELECT count(sqlCount.*) as count FROM (");
                        sqlCount.append(sql);
                        sqlCount.append(") as sqlCount");
                        ResultSet resultSet = clienteDao.createStatement(connection, sqlCount.toString());
                        if (resultSet.next()) {
                            totalResults.set(resultSet.getLong("count"));
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        connection.close();
                    }
                });
            }
            return totalResults.get();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    private String obterUrlArquivo(String chave) {
        try {
            return mediaMs.getImageUrl(chave);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}
