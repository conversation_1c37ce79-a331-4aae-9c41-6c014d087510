package com.pacto.adm.core.services.interfaces;

import com.pacto.adm.core.dto.IntegracaoSesiDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroIntegracaoSesiJSON;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface IntegracaoSesiService {

    public List<IntegracaoSesiDTO> findAll(FiltroIntegracaoSesiJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

    public String reprocessarItem(FiltroIntegracaoSesiJSON filtro) throws ServiceException;

    public String consultarItem(FiltroIntegracaoSesiJSON filtro) throws ServiceException;

}
