package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.AtestadoAdapter;
import com.pacto.adm.core.dao.interfaces.*;
import com.pacto.adm.core.dto.AtestadoDTO;
import com.pacto.adm.core.enumerador.MidiaEntidadeEnum;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.*;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.financeiro.VendaAvulsa;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.objects.EstornoMovProduto;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.adm.core.services.interfaces.*;
import com.pacto.config.utils.Uteis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import org.hibernate.exception.ConstraintViolationException;
import java.util.Date;
import java.util.List;
import java.util.Locale;

@Service
public class AtestadoServiceImpl implements AtestadoService {

    @Autowired
    private MessageSource messageSource;
    @Autowired
    private RequestService requestService;
    @Autowired
    private AtestadoDao atestadoDao;
    @Autowired
    private MovProdutoDao movProdutoDao;
    @Autowired
    private AtestadoAdapter atestadoAdapter;
    @Autowired
    private ClienteDao clienteDao;
    @Autowired
    private EmpresaDao empresaDao;
    @Autowired
    private ClienteMensagemService clienteMensagemService;
    @Autowired
    private MovParcelaDao movParcelaDao;
    @Autowired
    private MovProdutoParcelaDao movProdutoParcelaDao;
    @Autowired
    private MovProdutoService movProdutoService;
    @Autowired
    private ArquivoService arquivoService;
    @Autowired
    private VendaAvulsaService vendaAvulsaService;
    @Autowired
    private ProdutoDao produtoDao;

    @Override
    public AtestadoDTO findById(Integer id) throws Exception {
        AtestadoDTO atestadoDTO = atestadoAdapter.toDto(atestadoDao.findById(id));
        return atestadoDTO;
    }

    @Override
    public List<AtestadoDTO> findAllByMatricula(Integer matricula, PaginadorDTO paginadorDTO) throws Exception {
        List<Atestado> atestados = atestadoDao.findAllByMatricula(matricula, paginadorDTO);
        List<AtestadoDTO> atestadoDTOS = atestadoAdapter.toDtos(atestados);
        return atestadoDTOS;
    }

    @Override
    public AtestadoDTO saveOrUpdate(AtestadoDTO atestadoDTO, Integer matricula) throws ServiceException {
        try {
            validacoes(atestadoDTO);
            atestadoDao.getCurrentSession().clear();

            Atestado atestado;
            if (Uteis.intNullOrEmpty(atestadoDTO.getCodigo())) {
                Cliente cliente = clienteDao.findByMatricula(matricula);
                Empresa empresa = empresaDao.findById(requestService.getEmpresaId());

                if (atestadoDTO.getArquivo() != null && !Uteis.nullOrEmpty(atestadoDTO.getArquivo().getDados())) {
                    atestadoDTO.getArquivo().setPessoa(cliente.getPessoa().getCodigo());
                    atestadoDTO.getArquivo().setTipo(MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO.name());
                    atestadoDTO.setArquivo(arquivoService.saveOrUpdate(atestadoDTO.getArquivo()));
                }

                atestado = atestadoAdapter.toEntity(atestadoDTO);
                Date dataInicio = atestado.getMovProduto().getDataInicioVigencia();
                Date dataFinal = atestado.getMovProduto().getDataFinalVigencia();
                Produto produtoAtestado = produtoDao.findById(atestado.getMovProduto().getProduto().getCodigo());
                produtoAtestado.setValorFinal( produtoAtestado.getValorFinal() == null ? 0 :produtoAtestado.getValorFinal());
                Usuario usuarioResponsavel = new Usuario(requestService.getUsuarioAtual().getCodZw(), requestService.getUsuarioAtual().getUsername());
                VendaAvulsa vendaAvulsa = vendaAvulsaService.gerarVendaAtestado(cliente, dataInicio, dataFinal, produtoAtestado, produtoAtestado.getValorFinal(), usuarioResponsavel, empresa);
                MovProduto movProduto = movProdutoDao.consultarPorCodigoVendaAvulsaRetornaCodigo(vendaAvulsa.getCodigo());
                atestado.getMovProduto().setProduto(produtoAtestado);
                atestado.setMovProduto(movProduto);
                atestado.setDataRegistro(new Date());
                atestado = atestadoDao.save(atestado);
                clienteMensagemService.processarProdutoAtestado(cliente.getCodigo(), movProduto.getProduto(), dataFinal, usuarioResponsavel);
            } else {
                Atestado atestadoAnt = atestadoDao.findById(atestadoDTO.getCodigo());

                if (atestadoDTO.getArquivo() != null) {
                    atestadoDTO.setArquivo(arquivoService.saveOrUpdate(atestadoDTO.getArquivo()));
                }

                atestado = atestadoAdapter.toEntity(atestadoDTO);
                atestado.setDataRegistro(atestadoAnt.getDataRegistro());
                atestado = atestadoDao.update(atestado);
            }
            AtestadoDTO dtoRetornar = atestadoAdapter.toDto(atestado);
            return dtoRetornar;
        } catch (Exception e) {
            e.printStackTrace();
            if (e.getCause() instanceof ConstraintViolationException) {
                org.hibernate.exception.ConstraintViolationException constraintViolationException = ((org.hibernate.exception.ConstraintViolationException) e.getCause());
                throw new ServiceException(constraintViolationException.getSQLException());
            }
            throw new ServiceException(e);
        }
    }

    @Override
    public void excluirAtestado(Integer codigoAtestado) throws Exception {
        atestadoDao.getCurrentSession().clear();
        movParcelaDao.getCurrentSession().clear();
        clienteDao.getCurrentSession().clear();

        Atestado atestado = atestadoDao.findById(codigoAtestado);

        EstornoMovProduto estornoMovProduto = new EstornoMovProduto();
        estornoMovProduto.setMovProduto(atestado.getMovProduto());
        estornoMovProduto.getListaMovProduto().add(atestado.getMovProduto());
        if (estornoMovProduto.getMovProduto().getCodigo() > 0) {
            MovParcela movParcela = movParcelaDao.consultarPorMovProduto(estornoMovProduto.getMovProduto().getCodigo().intValue());
            movProdutoService.estornarMovProduto(estornoMovProduto, movParcela, "");
        }

        Integer codigoPessoa = atestado.getMovProduto().getPessoa().getCodigo();
        Atestado ultimoAtestado = atestadoDao.consultarUltimoAtestado(codigoPessoa);

        Cliente cliente = clienteDao.findByPessoa(codigoPessoa);
        if (ultimoAtestado != null &&
                ultimoAtestado.getParqPositivo() != null) {
            cliente.setParqPositivo(ultimoAtestado.getParqPositivo());
        } else {
            cliente.setParqPositivo(false);
        }
        clienteDao.alterarParqCliente(cliente);
        clienteMensagemService.processarProdutoAtestado(cliente.getCodigo(), estornoMovProduto.getMovProduto().getProduto(), null, null);
    }

    private void validacoes(AtestadoDTO atestadoDTO) throws ServiceException {
        if (atestadoDTO.getMovProduto().getProduto() == null || Uteis.intNullOrEmpty(atestadoDTO.getMovProduto().getProduto().getCodigo())) {
            throw new ServiceException(messageSource.getMessage("atestado.aptidao.fisica.produto.nao.informado", null, new Locale(requestService.getLocale())));
        }
        if (atestadoDTO.getMovProduto().getDataInicioVigencia() == null) {
            throw new ServiceException(messageSource.getMessage("atestado.aptidao.fisica.data.inicio.nao.informado", null, new Locale(requestService.getLocale())));
        }
    }
}
