package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.empresa.ConfiguracaoEmpresaHubSpotAdapter;
import com.pacto.adm.core.dao.interfaces.ConfiguracaoEmpresaHubSpotDao;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoEmpresaHubSpotDTO;
import com.pacto.adm.core.entities.ConfiguracaoEmpresaHubSpot;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.adm.core.services.interfaces.ConfiguracaoEmpresaHubSpotService;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Locale;

@Service
public class ConfiguracaoEmpresaHubSpotServiceImpl implements ConfiguracaoEmpresaHubSpotService {

    @Autowired
    private DiscoveryService discoveryService;

    private final ConfiguracaoEmpresaHubSpotDao configDao;
    private final ConfiguracaoEmpresaHubSpotAdapter configAdapter;
    private final HttpServico httpServico;
    private final RequestService requestService;
    private final MessageSource messageSource;

    public ConfiguracaoEmpresaHubSpotServiceImpl(ConfiguracaoEmpresaHubSpotDao configDao, ConfiguracaoEmpresaHubSpotAdapter configAdapter, HttpServico httpServico, RequestService requestService, MessageSource messageSource) {
        this.configDao = configDao;
        this.configAdapter = configAdapter;
        this.httpServico = httpServico;
        this.requestService = requestService;
        this.messageSource = messageSource;
    }


    @Override
    public ConfiguracaoEmpresaHubSpotDTO findByEmpresaId(Integer codigoEmpresa) throws Exception {
        ConfiguracaoEmpresaHubSpot configEntity = configDao.findByEmpresaId(codigoEmpresa);
        ConfiguracaoEmpresaHubSpotDTO configDTO;
        if (!UteisValidacao.emptyNumber(configEntity.getCodigo())) {
            configDTO = configAdapter.toDto(configEntity);
        } else {
            configDTO = new ConfiguracaoEmpresaHubSpotDTO();
        }
        configDTO.setShowCallBackHubsPot(getShowCallBackHubsPot(codigoEmpresa));
        configDTO.setUrlWebHookHubspot(getUrlWebHookHubspot(codigoEmpresa));
        configDTO.setEmpresa(new EmpresaDTO());
        configDTO.getEmpresa().setCodigo(codigoEmpresa);
        return configDTO;
    }

    @Override
    public ConfiguracaoEmpresaHubSpotDTO salvarConfiguracaoEmpresaHubSpot(ConfiguracaoEmpresaHubSpotDTO configDTO) throws ServiceException {
        validarCampos(configDTO);
        try {
            configDao.getCurrentSession().clear();
            ConfiguracaoEmpresaHubSpot configEntity;
            if (Uteis.intNullOrEmpty(configDTO.getCodigo())) {
                configEntity = configAdapter.toEntity(configDTO);
                configEntity = configDao.save(configEntity);
            } else {
                configEntity = configAdapter.toEntity(configDTO);
                configEntity = configDao.update(configEntity);
            }
            ConfiguracaoEmpresaHubSpotDTO dtoRetornar = configAdapter.toDto(configEntity);
            dtoRetornar.setShowCallBackHubsPot(getShowCallBackHubsPot(configDTO.getEmpresa().getCodigo()));
            dtoRetornar.setUrlWebHookHubspot(getUrlWebHookHubspot(configDTO.getEmpresa().getCodigo()));
            return dtoRetornar;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public ConfiguracaoEmpresaHubSpotDTO aprovar(ConfiguracaoEmpresaHubSpotDTO configDTO) throws ServiceException {
        try {
            configDTO = salvarConfiguracaoEmpresaHubSpot(configDTO);

            String urlApiZwUrl = getUrlAPI();
            String url = urlApiZwUrl + "/lead/povoarParansHubspot";
            String urlCallBackHubsPost = urlApiZwUrl + getShowCallBackHubsPot(configDTO.getEmpresa().getCodigo());

            JSONObject params = new JSONObject();
            params.put("key", requestService.getUsuarioAtual().getChave());
            params.put("empresa", configDTO.getEmpresa().getCodigo());
            params.put("clientId", configDTO.getClientId());
            params.put("expiration", String.valueOf(new Date().getTime() + 300000)); // 5 minutos.
            params.put("redirect_uri", urlCallBackHubsPost);

            ResponseEntity<String> response = httpServico.doJson(url, params.toString(), HttpMethod.POST, new HttpHeaders());

            params = new JSONObject();
            params.put("empresa", configDTO.getEmpresa().getCodigo());
            params.put("empresausahub", true);
            params.put("horaexpiracao", "23:59");
            params.put("clientsecret", configDTO.getClientSecret());
            params.put("clientId", configDTO.getClientId());
            params.put("codigo", configDTO.getCodigo());
            params.put("url_instalacao", configDTO.getUrl_instalacao());
            params.put("url_redirect", urlCallBackHubsPost);

            if (configDTO.getClientId().isEmpty() || configDTO.getUrl_instalacao().isEmpty()) {
                throw new ServiceException("Preencha os dados do aplicativo hubspot");
            }

            HashMap header = new HashMap<>();
            header.put("chave", requestService.getUsuarioAtual().getChave());

            if (response.getBody().equals("OK")) {
                return configDTO;
            } else if (response.getBody().equals("FAIL")) {
                throw new ServiceException("Limite de requisições atingido. Tente novamente em 5 minutos.");
            }
        } catch (Exception e) {
            throw new ServiceException("Falha ao tentar validar Hubspot: " + e.getMessage());
        }
        return null;
    }

    public String getUrlAPI() throws Exception {
        String urlApiZwUrl = discoveryService.getClientDiscovery().getServiceUrls().getApiZwUrl();
        return (urlApiZwUrl.contains("/prest") ? urlApiZwUrl : (urlApiZwUrl + "/prest"));
    }

    public String getUrlAPIBase(Integer codigoEmpresa) throws Exception {
        return getUrlAPI() + "/lead/" + requestService.getUsuarioAtual().getChave() + "/" + codigoEmpresa + "/";
    }

    public String getUrlWebHookHubspot(Integer codigoEmpresa) throws Exception {
        return getUrlAPIBase(codigoEmpresa) + "v3/webhook/hubspot";
    }

    private String getShowCallBackHubsPot(Integer codigoEmpresa) throws Exception {
        return getUrlAPI() + "/lead/"+requestService.getUsuarioAtual().getChave()+"/"+codigoEmpresa+"/v2/oauth-callback";
    }

    public void validarCampos(ConfiguracaoEmpresaHubSpotDTO configDTO) throws ServiceException {
        if (configDTO.getEmpresa() == null || UteisValidacao.emptyNumber(configDTO.getEmpresa().getCodigo())) {
            throw new ServiceException(messageSource.getMessage("integracao.hubspot.empresa.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (configDTO.getResponsavelPadrao() == null || UteisValidacao.emptyNumber(configDTO.getResponsavelPadrao().getCodigo())) {
            throw new ServiceException(messageSource.getMessage("integracao.hubspot.responsavel.nao.informado", null, new Locale(requestService.getLocale())));
        }
        if (Uteis.nullOrEmpty(configDTO.getHoraLimite())) {
            throw new ServiceException(messageSource.getMessage("integracao.hubspot.hora.limite.nao.informada", null, new Locale(requestService.getLocale())));
        }
        if (configDTO.getAcaoObjecao() == null) {
            throw new ServiceException(messageSource.getMessage("integracao.hubspot.acao.objecao.nao.informado", null, new Locale(requestService.getLocale())));
        }
        if (UteisValidacao.emptyString(configDTO.getClientId())) {
            throw new ServiceException(messageSource.getMessage("integracao.hubspot.clienteid.nao.informado", null, new Locale(requestService.getLocale())));
        }
        if (UteisValidacao.emptyString(configDTO.getClientSecret())) {
            throw new ServiceException(messageSource.getMessage("integracao.hubspot.clientesecret.nao.informado", null, new Locale(requestService.getLocale())));
        }
        if (UteisValidacao.emptyString(configDTO.getUrl_instalacao())) {
            throw new ServiceException(messageSource.getMessage("integracao.hubspot.urlinstalacao.nao.informada", null, new Locale(requestService.getLocale())));
        }
    }
}
