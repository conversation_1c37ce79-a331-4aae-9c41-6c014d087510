package com.pacto.adm.core.services.implementations;

import com.pacto.adm.core.adapters.empresa.ConfiguracaoEmpresaBitrix24Adapter;
import com.pacto.adm.core.dao.interfaces.ConfiguracaoEmpresaBitrix24Dao;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoEmpresaBitrix24DTO;
import com.pacto.adm.core.entities.ConfiguracaoEmpresaBitrix24;
import com.pacto.adm.core.services.interfaces.ConfiguracaoEmpresaBitrix24Service;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Locale;

@Service
public class ConfiguracaoEmpresaBitrix24ServiceImpl implements ConfiguracaoEmpresaBitrix24Service {

    @Autowired
    private DiscoveryService discoveryService;

    private final ConfiguracaoEmpresaBitrix24Dao configDao;
    private final ConfiguracaoEmpresaBitrix24Adapter configAdapter;

    private final RequestService requestService;
    private final MessageSource messageSource;

    public ConfiguracaoEmpresaBitrix24ServiceImpl(ConfiguracaoEmpresaBitrix24Dao configDao, ConfiguracaoEmpresaBitrix24Adapter configAdapter, RequestService requestService, MessageSource messageSource) {
        this.configDao = configDao;
        this.configAdapter = configAdapter;
        this.requestService = requestService;
        this.messageSource = messageSource;
    }

    @Override
    public ConfiguracaoEmpresaBitrix24DTO findByEmpresa() throws Exception {
        ConfiguracaoEmpresaBitrix24 configEntity = configDao.findByEmpresaChave(requestService.getUsuarioAtual().getChave());
        ConfiguracaoEmpresaBitrix24DTO configDTO;
        if (configEntity != null) {
            configDTO = configAdapter.toDto(configEntity);
            configDTO.setUrlWebHookBitrix(getUrlWebHookBitrix());
        } else {
            configDTO = new ConfiguracaoEmpresaBitrix24DTO();
            configDTO.setUrl("");
            configDTO.setAcaoobjecao(0);
            configDTO.setUrlWebHookBitrix(getUrlWebHookBitrix());
            configDTO.setResponsavelPadrao(new UsuarioDTO());
            configDTO.setEmpresa("");
            configDTO.setEmpresa(requestService.getUsuarioAtual().getChave());

        }
        // Adicione qualquer lógica adicional necessária aqui
        return configDTO;
    }

    public String getUrlWebHookBitrix() throws Exception {
        String urlApiZwUrl = discoveryService.getClientDiscovery().getServiceUrls().getApiZwUrl().replaceAll("/prest", "");
        String urlBase = urlApiZwUrl + "/prest/lead";
        String chave = "/" + requestService.getUsuarioAtual().getChave();
        String params = "/v2/notificaBitrix";
        return urlBase + chave + params;
    }


    @Override
    public ConfiguracaoEmpresaBitrix24DTO salvarConfiguracaoEmpresaBitrix24(ConfiguracaoEmpresaBitrix24DTO configDTO) throws ServiceException {
        validarCampos(configDTO);
        try {
            configDao.getCurrentSession().clear();
            ConfiguracaoEmpresaBitrix24 configEntity;
            if (configDTO.getCodigo() == null) {
                configEntity = configAdapter.toEntity(configDTO);
                configEntity = configDao.save(configEntity);
            } else {
                configEntity = configAdapter.toEntity(configDTO);
                configEntity = configDao.update(configEntity);
            }
            ConfiguracaoEmpresaBitrix24DTO dtoRetornar = configAdapter.toDto(configEntity);
            dtoRetornar.setUrlWebHookBitrix(getUrlWebHookBitrix());
            return dtoRetornar;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }
    private void validarCampos(ConfiguracaoEmpresaBitrix24DTO configDTO) throws ServiceException {
        if (configDTO.getEmpresa() == null || UteisValidacao.emptyString(configDTO.getEmpresa())) {
            throw new ServiceException(messageSource.getMessage("integracao.hubspot.empresa.nao.informada", null, new Locale(requestService.getLocale())));
        }
    }
}
