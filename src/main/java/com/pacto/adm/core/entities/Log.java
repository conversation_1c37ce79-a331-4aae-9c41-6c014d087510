package com.pacto.adm.core.entities;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import java.util.Date;

@Entity
@Table(name = "log")
public class Log {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nomeEntidade;
    private String nomeEntidadeDescricao;
    private String chavePrimaria;
    private String chavePrimariaEntidadeSubordinada;
    private String nomeCampo;
    private String valorCampoAnterior;
    private String valorCampoAlterado;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataAlteracao;
    private String responsavelAlteracao;
    private String operacao;
    private Integer pessoa;
    private Integer cliente;
    private String origem;

    @Transient
    private Cliente clienteEntity;

    public Log() {
    }

    public Log(
            Integer codigo, String nomeEntidade, String chavePrimaria, String valorCampoAnterior, String valorCampoAlterado,
            Date dataAlteracao, String responsavelAlteracao, String operacao, Cliente clienteEntity
    ) {
        this.codigo = codigo;
        this.nomeEntidade = nomeEntidade;
        this.chavePrimaria = chavePrimaria;
        this.valorCampoAnterior = valorCampoAnterior;
        this.valorCampoAlterado = valorCampoAlterado;
        this.dataAlteracao = dataAlteracao;
        this.responsavelAlteracao = responsavelAlteracao;
        this.operacao = operacao;
        this.clienteEntity = clienteEntity;
    }

    public Log(
            Integer codigo, String nomeEntidade, String nomeEntidadeDescricao, String chavePrimaria,
            String chavePrimariaEntidadeSubordinada, String nomeCampo, String valorCampoAnterior,
            String valorCampoAlterado, Date dataAlteracao, String responsavelAlteracao, String operacao,
            Integer pessoa, Integer cliente, String origem, Cliente clienteEntity
    ) {
        this.codigo = codigo;
        this.nomeEntidade = nomeEntidade;
        this.nomeEntidadeDescricao = nomeEntidadeDescricao;
        this.chavePrimaria = chavePrimaria;
        this.chavePrimariaEntidadeSubordinada = chavePrimariaEntidadeSubordinada;
        this.nomeCampo = nomeCampo;
        this.valorCampoAnterior = valorCampoAnterior;
        this.valorCampoAlterado = valorCampoAlterado;
        this.dataAlteracao = dataAlteracao;
        this.responsavelAlteracao = responsavelAlteracao;
        this.operacao = operacao;
        this.pessoa = pessoa;
        this.cliente = cliente;
        this.origem = origem;
        this.clienteEntity = clienteEntity;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeEntidade() {
        return nomeEntidade;
    }

    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }

    public String getNomeEntidadeDescricao() {
        return nomeEntidadeDescricao;
    }

    public void setNomeEntidadeDescricao(String nomeEntidadeDescricao) {
        this.nomeEntidadeDescricao = nomeEntidadeDescricao;
    }

    public String getChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(String chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public String getChavePrimariaEntidadeSubordinada() {
        return chavePrimariaEntidadeSubordinada;
    }

    public void setChavePrimariaEntidadeSubordinada(String chavePrimariaEntidadeSubordinada) {
        this.chavePrimariaEntidadeSubordinada = chavePrimariaEntidadeSubordinada;
    }

    public String getNomeCampo() {
        return nomeCampo;
    }

    public void setNomeCampo(String nomeCampo) {
        this.nomeCampo = nomeCampo;
    }

    public String getValorCampoAnterior() {
        return valorCampoAnterior;
    }

    public void setValorCampoAnterior(String valorCampoAnterior) {
        this.valorCampoAnterior = valorCampoAnterior;
    }

    public String getValorCampoAlterado() {
        return valorCampoAlterado;
    }

    public void setValorCampoAlterado(String valorCampoAlterado) {
        this.valorCampoAlterado = valorCampoAlterado;
    }

    public Date getDataAlteracao() {
        return dataAlteracao;
    }

    public void setDataAlteracao(Date dataAlteracao) {
        this.dataAlteracao = dataAlteracao;
    }

    public String getResponsavelAlteracao() {
        return responsavelAlteracao;
    }

    public void setResponsavelAlteracao(String responsavelAlteracao) {
        this.responsavelAlteracao = responsavelAlteracao;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public String getOrigem() {
        if (origem == null) {
            return "";
        }
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public Cliente getClienteEntity() {
        return clienteEntity;
    }

    public void setClienteEntity(Cliente clienteEntity) {
        this.clienteEntity = clienteEntity;
    }
}
