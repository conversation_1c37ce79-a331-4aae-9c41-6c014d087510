package com.pacto.adm.core.entities;

import com.pacto.config.annotations.UseOnlyThisToLog;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

@Entity
@Schema(description = "Informações da pessoa", name = "Pessoa")
public class Pessoa {

    @Id
    @UseOnlyThisToLog
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "Código único identificador da pessoa", example = "003")
    private Integer codigo;
    @Schema(description = "Nome da pessoa", example = "Augusto da Silva")
    @UseOnlyThisToLog
    private String nome;
    @Schema(description = "Chave da foto", example = "")
    private String fotoKey;
    @Column(name = "cfp")
    @Schema(description = "CPF da pessoa", example = "123.456.789-10")
    private String cpf;
    @Column(name = "valorlimitecaixaabertovendaavulsa")
    @Schema(description = "Valor Limite para deixar no caixa em aberto")
    private Double valorLimiteCaixaAbertoVendaAvulsa;

    public Pessoa() {
    }

    public Pessoa(String nome) {
        this.nome = nome;
    }

    public Pessoa(Integer codigo, String nome) {
        this.codigo = codigo;
        this.nome = nome;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getCpf() { return cpf; }

    public void setCpf(String cpf) { this.cpf = cpf; }

    public Double getValorLimiteCaixaAbertoVendaAvulsa() {
        if (valorLimiteCaixaAbertoVendaAvulsa == null) {
            valorLimiteCaixaAbertoVendaAvulsa = 0.0;
        }
        return valorLimiteCaixaAbertoVendaAvulsa;
    }

    public void setValorLimiteCaixaAbertoVendaAvulsa(Double valorLimiteCaixaAbertoVendaAvulsa) {
        this.valorLimiteCaixaAbertoVendaAvulsa = valorLimiteCaixaAbertoVendaAvulsa;
    }

    //metodo to string imprimindo todos os valores
    @Override
    public String toString() {
        return "Pessoa{" +
                "codigo=" + codigo +
                ", nome='" + nome + '\'' +
                ", fotoKey='" + fotoKey + '\'' +
                ", cpf='" + cpf + '\'' +
                '}';
    }
}
