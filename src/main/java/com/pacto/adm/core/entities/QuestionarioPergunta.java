package com.pacto.adm.core.entities;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;

@Entity
public class QuestionarioPergunta {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Boolean obrigatoria;
    @ManyToOne
    @JoinColumn(name = "questionario", foreignKey = @ForeignKey(name = "fk_questionariopergunta_questionario"))
    private Questionario questionario;

    @OneToOne
    @JoinColumn(name = "pergunta", foreignKey = @ForeignKey(name = "fk_questionariopergunta_pergunta"))
    private Pergunta pergunta;
    private Integer nrQuestao;


    public QuestionarioPergunta() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getObrigatoria() {
        if (obrigatoria == null) {
            obrigatoria = false;
        }
        return obrigatoria;
    }

    public void setObrigatoria(Boolean obrigatoria) {
        this.obrigatoria = obrigatoria;
    }

    public Questionario getQuestionario() {
        return questionario;
    }

    public void setQuestionario(Questionario questionario) {
        this.questionario = questionario;
    }

    public Pergunta getPergunta() {
        return pergunta;
    }

    public void setPergunta(Pergunta pergunta) {
        this.pergunta = pergunta;
    }

    public Integer getNrQuestao() {
        return nrQuestao;
    }

    public void setNrQuestao(Integer ordem) {
        this.nrQuestao = ordem;
    }
}
