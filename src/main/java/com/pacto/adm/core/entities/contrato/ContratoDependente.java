package com.pacto.adm.core.entities.contrato;

import com.pacto.adm.core.entities.Cliente;
import com.pacto.config.annotations.RelationalField;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;


@Entity
@Table(name = "contratodependente")
public class ContratoDependente implements Cloneable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Date dataInicio;
    private Date dataFinal;
    private Date dataFinalAjustada;
    private Integer posicaoDependente;
    private Integer titular;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(name = "fk_cliente_cliente"))
    private Cliente cliente;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "contrato", foreignKey = @ForeignKey(name = "fk_dependenteplanocompartilhado_contrato"))
    private Contrato contrato;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Date getDataFinalAjustada() {
        return dataFinalAjustada;
    }

    public void setDataFinalAjustada(Date dataFinalAjustada) {
        this.dataFinalAjustada = dataFinalAjustada;
    }

    public Integer getPosicaoDependente() {
        return posicaoDependente;
    }

    public void setPosicaoDependente(Integer posicaoDependente) {
        this.posicaoDependente = posicaoDependente;
    }

    public Integer getTitular() {
        return titular;
    }

    public void setTitular(Integer titular) {
        this.titular = titular;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public Contrato getContrato() {
        return contrato;
    }

    public void setContrato(Contrato contrato) {
        this.contrato = contrato;
    }
}
