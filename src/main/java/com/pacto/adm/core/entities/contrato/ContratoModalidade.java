package com.pacto.adm.core.entities.contrato;

import javax.persistence.*;
import java.util.Set;

@Entity
public class ContratoModalidade {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer vezesSemana;
    private Double valorModalidade;
    private Double valorFinalModalidade;
    private Double valorBaseCalculo;

    @ManyToOne
    @JoinColumn(name = "modalidade", foreignKey = @ForeignKey(name = "fk_contratomodalidade_modalidade"))
    private Modalidade modalidade;

    @ManyToOne
    @JoinColumn(name = "contrato", foreignKey = @ForeignKey(name = "fk_contratomodalidade_contrato"))
    private Contrato contrato;

    @OneToMany(mappedBy = "contratoModalidade", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<ContratoModalidadeTurma> turmas;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getVezesSemana() {
        return vezesSemana;
    }

    public void setVezesSemana(Integer vezesSemana) {
        this.vezesSemana = vezesSemana;
    }

    public Double getValorModalidade() {
        return valorModalidade;
    }

    public void setValorModalidade(Double valorModalidade) {
        this.valorModalidade = valorModalidade;
    }

    public Double getValorFinalModalidade() {
        return valorFinalModalidade;
    }

    public void setValorFinalModalidade(Double valorFinalModalidade) {
        this.valorFinalModalidade = valorFinalModalidade;
    }

    public Double getValorBaseCalculo() {
        return valorBaseCalculo;
    }

    public void setValorBaseCalculo(Double valorBaseCalculo) {
        this.valorBaseCalculo = valorBaseCalculo;
    }

    public Modalidade getModalidade() {
        return modalidade;
    }

    public void setModalidade(Modalidade modalidade) {
        this.modalidade = modalidade;
    }

    public Contrato getContrato() {
        return contrato;
    }

    public void setContrato(Contrato contrato) {
        this.contrato = contrato;
    }

    public Set<ContratoModalidadeTurma> getTurmas() {
        return turmas;
    }

    public void setTurmas(Set<ContratoModalidadeTurma> turmas) {
        this.turmas = turmas;
    }
}
