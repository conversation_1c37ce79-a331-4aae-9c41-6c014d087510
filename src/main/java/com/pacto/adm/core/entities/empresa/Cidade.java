package com.pacto.adm.core.entities.empresa;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.UseOnlyThisToLog;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@Entity
@NomeEntidadeLog("Cidade")
public class Cidade {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @UseOnlyThisToLog
    private String nome;
    @ManyToOne
    @JoinColumn(name = "estado", foreignKey = @ForeignKey(name = "fk_cidade_estado"))
    private Estado estado;

    public Cidade() {
    }

    public Cidade(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Estado getEstado() {
        return estado;
    }

    public void setEstado(Estado estado) {
        this.estado = estado;
    }
}
