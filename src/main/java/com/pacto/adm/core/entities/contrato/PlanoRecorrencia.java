package com.pacto.adm.core.entities.contrato;

import org.springframework.beans.BeanUtils;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

@Entity
public class PlanoRecorrencia {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @OneToOne
    @JoinColumn(name = "plano", foreignKey = @ForeignKey(name = "fk_planorecorrencia_plano"))
    private Plano plano;
    private Double taxaAdesao;
    private Double valorAnuidade;
    private Double valorMensal;
    private Integer diaAnuidade;
    private Integer mesAnuidade;
    private Integer duracaoPlano;
    private Boolean renovavelAutomaticamente;
    private Boolean naoRenovarParcelaVencida;
    private Boolean naoCobrarAnuidadeProporcional;
    private Integer parcelaAnuidade;
    private Boolean cancelamentoProporcional;
    private Integer qtdDiasCobrarProximaParcela;
    private Integer qtdDiasCobrarAnuidadeTotal;
    private Boolean gerarParcelasValorDiferente;
    private Boolean gerarParcelasValorDiferenteRenovacao;
    private Boolean parcelarAnuidade;
    @Column(name = "anuidadenaparcela", nullable = false)
    private Boolean anuidadeNaParcela;
    @OneToMany(mappedBy = "planoRecorrencia", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<PlanoAnuidadeParcela> parcelasAnuidade;
    @OneToMany(mappedBy = "planoRecorrencia", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<PlanoRecorrenciaParcela> parcelas;
    @Column(name = "qtddiasvenccancelauto", nullable = false)
    private Integer qtdDiasAposVencimentoCancelamentoAutomatico;

    @Transient
    private Boolean aplicarDiasVencimentoContratosAtivos = false;

    @Transient
    private Boolean aplicarCancelamentoProporcionalContratosAtivos = false;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Plano getPlano() {
        return plano;
    }

    public void setPlano(Plano plano) {
        this.plano = plano;
    }

    public Double getTaxaAdesao() {
        if (taxaAdesao == null) {
            return 0D;
        }
        return new BigDecimal(taxaAdesao).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    public void setTaxaAdesao(Double taxaAdesao) {
        this.taxaAdesao = taxaAdesao;
    }

    public Double getValorAnuidade() {
        if (valorAnuidade == null) {
            return 0D;
        }
        return new BigDecimal(valorAnuidade).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    public void setValorAnuidade(Double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }

    public Double getValorMensal() {
        if (valorMensal == null) {
            return 0D;
        }
        return new BigDecimal(valorMensal).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    public void setValorMensal(Double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public Integer getDiaAnuidade() {
        return diaAnuidade;
    }

    public void setDiaAnuidade(Integer diaAnuidade) {
        this.diaAnuidade = diaAnuidade;
    }

    public Integer getMesAnuidade() {
        return mesAnuidade;
    }

    public void setMesAnuidade(Integer mesAnuidade) {
        this.mesAnuidade = mesAnuidade;
    }

    public Integer getDuracaoPlano() {
        return duracaoPlano;
    }

    public void setDuracaoPlano(Integer duracaoPlano) {
        this.duracaoPlano = duracaoPlano;
    }

    public Boolean getRenovavelAutomaticamente() {
        return renovavelAutomaticamente;
    }

    public void setRenovavelAutomaticamente(Boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public Boolean getNaoRenovarParcelaVencida() {
        return naoRenovarParcelaVencida;
    }

    public void setNaoRenovarParcelaVencida(Boolean naoRenovarParcelaVencida) {
        this.naoRenovarParcelaVencida = naoRenovarParcelaVencida;
    }

    public Boolean getNaoCobrarAnuidadeProporcional() {
        return naoCobrarAnuidadeProporcional;
    }

    public void setNaoCobrarAnuidadeProporcional(Boolean naoCobrarAnuidadeProporcional) {
        this.naoCobrarAnuidadeProporcional = naoCobrarAnuidadeProporcional;
    }

    public Integer getParcelaAnuidade() {
        return parcelaAnuidade;
    }

    public void setParcelaAnuidade(Integer parcelaAnuidade) {
        this.parcelaAnuidade = parcelaAnuidade;
    }

    public Boolean getCancelamentoProporcional() {
        return cancelamentoProporcional;
    }

    public void setCancelamentoProporcional(Boolean cancelamentoProporcional) {
        this.cancelamentoProporcional = cancelamentoProporcional;
    }

    public Integer getQtdDiasCobrarProximaParcela() {
        if (qtdDiasCobrarProximaParcela == null){
            qtdDiasCobrarProximaParcela = 0;
        }
        return qtdDiasCobrarProximaParcela;
    }

    public void setQtdDiasCobrarProximaParcela(Integer qtdDiasCobrarProximaParcela) {
        this.qtdDiasCobrarProximaParcela = qtdDiasCobrarProximaParcela;
    }

    public Integer getQtdDiasCobrarAnuidadeTotal() {
        if (qtdDiasCobrarAnuidadeTotal == null){
            qtdDiasCobrarAnuidadeTotal = 0;
        }
        return qtdDiasCobrarAnuidadeTotal;
    }

    public void setQtdDiasCobrarAnuidadeTotal(Integer qtdDiasCobrarAnuidadeTotal) {
        this.qtdDiasCobrarAnuidadeTotal = qtdDiasCobrarAnuidadeTotal;
    }

    public Boolean getGerarParcelasValorDiferente() {
        return gerarParcelasValorDiferente;
    }

    public void setGerarParcelasValorDiferente(Boolean gerarParcelasValorDiferente) {
        this.gerarParcelasValorDiferente = gerarParcelasValorDiferente;
    }

    public Boolean getGerarParcelasValorDiferenteRenovacao() {
        return gerarParcelasValorDiferenteRenovacao;
    }

    public void setGerarParcelasValorDiferenteRenovacao(Boolean gerarParcelasValorDiferenteRenovacao) {
        this.gerarParcelasValorDiferenteRenovacao = gerarParcelasValorDiferenteRenovacao;
    }

    public Boolean getParcelarAnuidade() {
        return parcelarAnuidade;
    }

    public void setParcelarAnuidade(Boolean parcelarAnuidade) {
        this.parcelarAnuidade = parcelarAnuidade;
    }

    public Set<PlanoRecorrenciaParcela> getParcelas() {
        if (parcelas == null) {
            parcelas = new HashSet<>();
        }
        return parcelas;
    }

    public void setParcelas(Set<PlanoRecorrenciaParcela> parcelas) {
        this.parcelas = parcelas;
    }

    public Boolean getAnuidadeNaParcela() {
        return anuidadeNaParcela;
    }

    public void setAnuidadeNaParcela(Boolean anuidadeNaParcela) {
        if (anuidadeNaParcela == null) {
            anuidadeNaParcela = Boolean.FALSE;
        }
        this.anuidadeNaParcela = anuidadeNaParcela;
    }

    public Set<PlanoAnuidadeParcela> getParcelasAnuidade() {
        if (parcelasAnuidade == null) {
            parcelasAnuidade = new HashSet<>();
        }
        return parcelasAnuidade;
    }

    public void setParcelasAnuidade(Set<PlanoAnuidadeParcela> parcelasAnuidade) {
        this.parcelasAnuidade = parcelasAnuidade;
    }

    public Integer getQtdDiasAposVencimentoCancelamentoAutomatico() {
        return qtdDiasAposVencimentoCancelamentoAutomatico;
    }

    public void setQtdDiasAposVencimentoCancelamentoAutomatico(Integer qtdDiasAposVencimentoCancelamentoAutomatico) {
        this.qtdDiasAposVencimentoCancelamentoAutomatico = qtdDiasAposVencimentoCancelamentoAutomatico;
    }

    public Boolean getAplicarDiasVencimentoContratosAtivos() {
        if (aplicarDiasVencimentoContratosAtivos == null){
            aplicarDiasVencimentoContratosAtivos = false;
        }
        return aplicarDiasVencimentoContratosAtivos;
    }

    public void setAplicarDiasVencimentoContratosAtivos(Boolean aplicarDiasVencimentoContratosAtivos) {
        this.aplicarDiasVencimentoContratosAtivos = aplicarDiasVencimentoContratosAtivos;
    }

    public Boolean getAplicarCancelamentoProporcionalContratosAtivos() {
        if(aplicarCancelamentoProporcionalContratosAtivos == null){
            aplicarCancelamentoProporcionalContratosAtivos = false;
        }
        return aplicarCancelamentoProporcionalContratosAtivos;
    }

    public void setAplicarCancelamentoProporcionalContratosAtivos(Boolean aplicarCancelamentoProporcionalContratosAtivos) {
        this.aplicarCancelamentoProporcionalContratosAtivos = aplicarCancelamentoProporcionalContratosAtivos;
    }
}
