package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
public class LiberacaoAcesso {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @OneToOne
    @RelationalField
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "clienteFK"))
    private Pessoa pessoa;

    @Column(name = "tipoliberacao")
    private Integer tipoLiberacao;

    private String sentido;

    @OneToOne
    @RelationalField
    @JoinColumn(name = "localacesso", foreignKey = @ForeignKey(name = "localacessoFK"))
    private LocalAcesso localAcesso;

    @OneToOne
    @RelationalField
    @JoinColumn(name = "coletor", foreignKey = @ForeignKey(name = "coletorFK"))
    private Coletor coletor;

    @OneToOne
    @RelationalField
    @JoinColumn(name = "usuario", foreignKey = @ForeignKey(name = "fk_liberacaoacesso_usuario"))
    private Usuario usuario;

    @OneToOne
    @RelationalField
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_cliente_empresa"))
    private Empresa empresa;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "datahora")
    private Date dataHora;

    private String justificativa;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "dthrjustificativa")
    private Date dataHoraJustificativa;

    @OneToOne
    @RelationalField
    @JoinColumn(name = "usuariojustificou", foreignKey = @ForeignKey(name = "usuarioFK"))
    private Usuario usuarioJustificou;

    @Column(name = "nomegenerico")
    private String nomeGenerico;
}
