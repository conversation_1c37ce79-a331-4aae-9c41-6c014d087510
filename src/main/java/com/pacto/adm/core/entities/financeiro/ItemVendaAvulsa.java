package com.pacto.adm.core.entities.financeiro;

import com.pacto.adm.core.entities.Produto;
import com.pacto.config.annotations.RelationalField;

import javax.persistence.*;
import java.util.Date;

@Entity
public class ItemVendaAvulsa {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer quantidade;
    private Double valorParcial;
    private Double valorDescontoManual;
    private boolean descontoManual;
    private Integer responsavelDesconto;
    private Integer pacote;
    private Integer pacotePersonal;
    @Transient
    private Date dataVenda;
    @Transient
    private Date dataValidade;
    @Transient
    private String movPagamentos;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "produto", foreignKey = @ForeignKey(name = "fk_itemvendaavulsa_produto"))
    private Produto produto;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "vendaavulsa", foreignKey = @ForeignKey(name = "fk_itemvendaavulsa_vendaavulsa"))
    private VendaAvulsa vendaAvulsa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "tabeladesconto", foreignKey = @ForeignKey(name = "fk_itemvendaavulsa_tabeladesconto"))
    protected Desconto tabelaDesconto;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Double getValorParcial() {
        return valorParcial;
    }

    public void setValorParcial(Double valorParcial) {
        this.valorParcial = valorParcial;
    }

    public Desconto getTabelaDesconto() {
        return tabelaDesconto;
    }

    public void setTabelaDesconto(Desconto tabelaDesconto) {
        this.tabelaDesconto = tabelaDesconto;
    }

    public Double getValorDescontoManual() {
        return valorDescontoManual;
    }

    public void setValorDescontoManual(Double valorDescontoManual) {
        this.valorDescontoManual = valorDescontoManual;
    }

    public boolean isDescontoManual() {
        return descontoManual;
    }

    public void setDescontoManual(boolean descontoManual) {
        this.descontoManual = descontoManual;
    }

    public Integer getResponsavelDesconto() {
        return responsavelDesconto;
    }

    public void setResponsavelDesconto(Integer responsavelDesconto) {
        this.responsavelDesconto = responsavelDesconto;
    }

    public Integer getPacote() {
        return pacote;
    }

    public void setPacote(Integer pacote) {
        this.pacote = pacote;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public VendaAvulsa getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(VendaAvulsa vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public Date getDataVenda() {
        return dataVenda;
    }

    public void setDataVenda(Date dataVenda) {
        this.dataVenda = dataVenda;
    }

    public Date getDataValidade() {
        return dataValidade;
    }

    public void setDataValidade(Date dataValidade) {
        this.dataValidade = dataValidade;
    }

    public String getMovPagamentos() {
        return movPagamentos;
    }

    public void setMovPagamentos(String movPagamentos) {
        this.movPagamentos = movPagamentos;
    }

    public Integer getPacotePersonal() {
        return pacotePersonal;
    }

    public void setPacotePersonal(Integer pacotePersonal) {
        this.pacotePersonal = pacotePersonal;
    }
}
