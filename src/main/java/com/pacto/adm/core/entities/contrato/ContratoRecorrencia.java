package com.pacto.adm.core.entities.contrato;

import com.pacto.adm.core.entities.Pessoa;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "contratorecorrencia")
public class ContratoRecorrencia {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Column(name = "anuidadenaparcela", columnDefinition = "DEFAULT FALSE")
    private Boolean anuidadeNaParcela;

    @Column(name = "cancelamentoproporcional", columnDefinition = "DEFAULT FALSE")
    private Boolean cancelamentoProporcional;

    @Column(name = "cancelamentoproporcionalsomenterenovacao", columnDefinition = "DEFAULT FALSE")
    private Boolean cancelamentoPropocionalSomenteRenovacao;

    @OneToOne
    @JoinColumn(name = "contrato", foreignKey = @ForeignKey(name = "fkey_contratorecorrencia_contrato"))
    private Contrato contrato;

    @Column(name = "datainutilizada")
    private Date dataInutilizada;

    @Column(name = "diasbloqueioacesso")
    private Integer diasBloqueioAcesso;

    @Column(name = "diascancelamentoautomatico")
    private Integer diasCancelamentoAutomatico;

    @Column(name = "diavencimentoanuidade")
    private Integer diaVencimentoAnuidade;

    @Column(name = "diavencimentocartao")
    private Integer diaVencimentoCartao;

    @Column(name = "fidelidade")
    private Integer fidelidade;

    @Column(name = "mesvencimentoanuidade")
    private Integer mesVencimentoAnuidade;

    @Column(name = "numerocartao", length = 50)
    private String numeroCartao;

    @Column(name = "parcelaanuidade")
    private Integer parcelaAnuidade;

    @Column(name = "parcelaranuidade", columnDefinition = "DEFAULT FALSE")
    private Boolean parcelarAnuidade;

    @OneToOne
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "fkey_contratorecorrencia_pessoa"))
    private Pessoa pessoa;

    @Column(name = "qtddiascobraranuidadetotal")
    private Integer qtdDiasCobrarAnuidadeTotal;

    @Column(name = "qtddiascobrarproximaparcela")
    private Integer qtdDiasCobrarProximaParcela;

    @Column(name = "renovavelautomaticamente", columnDefinition = "DEFAULT FALSE")
    private Boolean renovavelAutomaticamente;

    @Column(name = "ultimatransacaoaprovada", length = 50)
    private String ultimaTransacaoAprovada;

    @Column(name = "valoranuidade")
    private Double valorAnuidade;

    @Column(name = "valormensal")
    private Double valorMensal;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getAnuidadeNaParcela() {
        return anuidadeNaParcela;
    }

    public void setAnuidadeNaParcela(Boolean anuidadeNaParcela) {
        this.anuidadeNaParcela = anuidadeNaParcela;
    }

    public Boolean getCancelamentoProporcional() {
        return cancelamentoProporcional;
    }

    public void setCancelamentoProporcional(Boolean cancelamentoProporcional) {
        this.cancelamentoProporcional = cancelamentoProporcional;
    }

    public Boolean getCancelamentoPropocionalSomenteRenovacao() {
        return cancelamentoPropocionalSomenteRenovacao;
    }

    public void setCancelamentoPropocionalSomenteRenovacao(Boolean cancelamentoPropocionalSomenteRenovacao) {
        this.cancelamentoPropocionalSomenteRenovacao = cancelamentoPropocionalSomenteRenovacao;
    }

    public Contrato getContrato() {
        return contrato;
    }

    public void setContrato(Contrato contrato) {
        this.contrato = contrato;
    }

    public Date getDataInutilizada() {
        return dataInutilizada;
    }

    public void setDataInutilizada(Date dataInutilizada) {
        this.dataInutilizada = dataInutilizada;
    }

    public Integer getDiasBloqueioAcesso() {
        return diasBloqueioAcesso;
    }

    public void setDiasBloqueioAcesso(Integer diasBloqueioAcesso) {
        this.diasBloqueioAcesso = diasBloqueioAcesso;
    }

    public Integer getDiasCancelamentoAutomatico() {
        return diasCancelamentoAutomatico;
    }

    public void setDiasCancelamentoAutomatico(Integer diasCancelamentoAutomatico) {
        this.diasCancelamentoAutomatico = diasCancelamentoAutomatico;
    }

    public Integer getDiaVencimentoAnuidade() {
        return diaVencimentoAnuidade;
    }

    public void setDiaVencimentoAnuidade(Integer diasVencimentoAnuidade) {
        this.diaVencimentoAnuidade = diasVencimentoAnuidade;
    }

    public Integer getDiaVencimentoCartao() {
        return diaVencimentoCartao;
    }

    public void setDiaVencimentoCartao(Integer diasVencimentoCartao) {
        this.diaVencimentoCartao = diasVencimentoCartao;
    }

    public Integer getFidelidade() {
        return fidelidade;
    }

    public void setFidelidade(Integer fidelidade) {
        this.fidelidade = fidelidade;
    }

    public Integer getMesVencimentoAnuidade() {
        return mesVencimentoAnuidade;
    }

    public void setMesVencimentoAnuidade(Integer mesVencimentoAnuidade) {
        this.mesVencimentoAnuidade = mesVencimentoAnuidade;
    }

    public String getNumeroCartao() {
        return numeroCartao;
    }

    public void setNumeroCartao(String numeroCartao) {
        this.numeroCartao = numeroCartao;
    }

    public Integer getParcelaAnuidade() {
        return parcelaAnuidade;
    }

    public void setParcelaAnuidade(Integer parcelaAnuidade) {
        this.parcelaAnuidade = parcelaAnuidade;
    }

    public Boolean getParcelarAnuidade() {
        return parcelarAnuidade;
    }

    public void setParcelarAnuidade(Boolean parcelarAnuidade) {
        this.parcelarAnuidade = parcelarAnuidade;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getQtdDiasCobrarAnuidadeTotal() {
        return qtdDiasCobrarAnuidadeTotal;
    }

    public void setQtdDiasCobrarAnuidadeTotal(Integer qtdDiasCobrarAnuidadeTotal) {
        this.qtdDiasCobrarAnuidadeTotal = qtdDiasCobrarAnuidadeTotal;
    }

    public Integer getQtdDiasCobrarProximaParcela() {
        return qtdDiasCobrarProximaParcela;
    }

    public void setQtdDiasCobrarProximaParcela(Integer qtdDiasCobrarProximaParcela) {
        this.qtdDiasCobrarProximaParcela = qtdDiasCobrarProximaParcela;
    }

    public Boolean getRenovavelAutomaticamente() {
        return renovavelAutomaticamente;
    }

    public void setRenovavelAutomaticamente(Boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public String getUltimaTransacaoAprovada() {
        return ultimaTransacaoAprovada;
    }

    public void setUltimaTransacaoAprovada(String ultimaTransacaoAprovada) {
        this.ultimaTransacaoAprovada = ultimaTransacaoAprovada;
    }

    public Double getValorAnuidade() {
        return valorAnuidade;
    }

    public void setValorAnuidade(Double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }

    public Double getValorMensal() {
        return valorMensal;
    }

    public void setValorMensal(Double valorMensal) {
        this.valorMensal = valorMensal;
    }
}
