package com.pacto.adm.core.entities.contrato;

import com.pacto.adm.core.entities.Produto;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.config.annotations.RelationalField;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
public class PlanoEmpresa {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer codigo;
    @ManyToOne
    @JoinColumn(name = "plano", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Plano plano;
    @RelationalField
    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_configuracaoprodutoempresa_empresa"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Empresa empresa;
    protected Boolean venda;
    protected Boolean acesso;
    private Double taxaAdesao;
    private Double valorAnuidade;
    private Double valorMensal;
    private Double percentualMultaCancelamento;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Plano getPlano() {
        return plano;
    }

    public void setPlano(Plano plano) {
        this.plano = plano;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Boolean getVenda() {
        return venda;
    }

    public void setVenda(Boolean venda) {
        this.venda = venda;
    }

    public Boolean getAcesso() {
        return acesso;
    }

    public void setAcesso(Boolean acesso) {
        this.acesso = acesso;
    }

    public Double getTaxaAdesao() {
        return taxaAdesao;
    }

    public void setTaxaAdesao(Double taxaAdesao) {
        this.taxaAdesao = taxaAdesao;
    }

    public Double getValorAnuidade() {
        return valorAnuidade;
    }

    public void setValorAnuidade(Double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }

    public Double getValorMensal() {
        return valorMensal;
    }

    public void setValorMensal(Double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public Double getPercentualMultaCancelamento() {
        return percentualMultaCancelamento;
    }

    public void setPercentualMultaCancelamento(Double percentualMultaCancelamento) {
        this.percentualMultaCancelamento = percentualMultaCancelamento;
    }
}
