package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;

import javax.persistence.*;

@Entity
public class UsuarioPerfilAcesso {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer empresa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "usuario", foreignKey = @ForeignKey(name = "fk_usuarioperfilacesso_usuario"))
    private Usuario usuario;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "perfilacesso", foreignKey = @ForeignKey(name = "fk_usuarioperfilacesso_perfilacesso"))
    private PerfilAcesso perfilAcesso;

    private Boolean bloquearAcessoAcademia;
    private Boolean bloquearAcessoSistema;

    @Column(nullable = false, columnDefinition = "boolean default false")
    private Boolean unificado;

    private String modulosPermitidos;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public PerfilAcesso getPerfilAcesso() {
        return perfilAcesso;
    }

    public void setPerfilAcesso(PerfilAcesso perfilAcesso) {
        this.perfilAcesso = perfilAcesso;
    }

    public Boolean getBloquearAcessoAcademia() {
        return bloquearAcessoAcademia;
    }

    public void setBloquearAcessoAcademia(Boolean bloquearAcessoAcademia) {
        this.bloquearAcessoAcademia = bloquearAcessoAcademia;
    }

    public Boolean getBloquearAcessoSistema() {
        return bloquearAcessoSistema;
    }

    public void setBloquearAcessoSistema(Boolean bloquearAcessoSistema) {
        this.bloquearAcessoSistema = bloquearAcessoSistema;
    }

    public Boolean getUnificado() {
        return unificado;
    }

    public void setUnificado(Boolean unificado) {
        this.unificado = unificado;
    }

    public String getModulosPermitidos() {
        return modulosPermitidos;
    }

    public void setModulosPermitidos(String modulosPermitidos) {
        this.modulosPermitidos = modulosPermitidos;
    }
}
