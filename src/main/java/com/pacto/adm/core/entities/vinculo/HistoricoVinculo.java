package com.pacto.adm.core.entities.vinculo;

import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Colaborador;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.config.utils.Calendario;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Entity
@Table(name = "historicovinculo")
public class HistoricoVinculo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Temporal(TemporalType.TIMESTAMP)
    private Date dataRegistro;

    @Column(name = "origem", length = 50)
    private String origem;
    @Column(name = "tipocolaborador", length = 2, nullable = false)
    private String tipoColaborador;
    @Column(name = "tipohistoricovinculo", length = 2, nullable = false)
    private String tipoHistoricoVinculo;

    @ManyToOne
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(name = "fk_historicovinculo_cliente"))
    private Cliente cliente;

    @ManyToOne
    @JoinColumn(name = "colaborador", foreignKey = @ForeignKey(name = "fk_historicovinculo_colaborador"))
    private Colaborador colaborador;

    @ManyToOne
    @JoinColumn(name = "usuarioresponsavel", foreignKey = @ForeignKey(name = "fk_usuarioresponsavel_usuario"))
    private Usuario usuarioResponsavel;

    public HistoricoVinculo(
            Cliente cliente, Colaborador colaborador, String tipoHistoricoVinculo, String tipoColaborador,
            Date dataRegistro, String origem, Usuario usuario
    ) {
        this.cliente = cliente;
        this.colaborador = colaborador;
        this.tipoHistoricoVinculo = tipoHistoricoVinculo;
        this.dataRegistro = dataRegistro;
        this.origem = origem;
        this.usuarioResponsavel = usuario;
        this.tipoColaborador = tipoColaborador;
    }

    public HistoricoVinculo(Cliente cliente, Colaborador colaborador, String tipoHistoricoVinculo, String tipoColaborador, String origem, Usuario usuario) {
        this.cliente = cliente;
        this.colaborador = colaborador;
        this.tipoHistoricoVinculo = tipoHistoricoVinculo;
        this.origem = origem;
        this.usuarioResponsavel = usuario;
        this.tipoColaborador = tipoColaborador;
        this.dataRegistro = Calendario.hoje();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public String getTipoColaborador() {
        return tipoColaborador;
    }

    public void setTipoColaborador(String tipoColaborador) {
        this.tipoColaborador = tipoColaborador;
    }

    public String getTipoHistoricoVinculo() {
        return tipoHistoricoVinculo;
    }

    public void setTipoHistoricoVinculo(String tipoHistoricoVinculo) {
        this.tipoHistoricoVinculo = tipoHistoricoVinculo;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public Colaborador getColaborador() {
        return colaborador;
    }

    public void setColaborador(Colaborador colaborador) {
        this.colaborador = colaborador;
    }

    public Usuario getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(Usuario usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }
}
