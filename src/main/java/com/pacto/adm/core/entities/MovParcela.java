package com.pacto.adm.core.entities;

import com.pacto.config.annotations.NomeEntidadeLog;
import com.pacto.config.annotations.NotLogged;
import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.financeiro.VendaAvulsa;
import lombok.Getter;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.*;

@Entity
@NomeEntidadeLog("MovParcela")
public class MovParcela implements Cloneable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String descricao;
    private BigDecimal valorParcela;
    private String situacao;
    @Column(name = "datavencimento")
    private Date dataVencimento;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "dataregistro")
    private Date dataLancamento;
    @Getter
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "datacobranca")
    private Date dataCobranca;
    private Integer contrato;
    private Boolean movimentoCC;
    private String movPagamentoCC;
    private Double percentualJuro;
    private Double percentualMulta;
    @Column(name = "nrtentativas")
    private Integer nrTentativas;
    @Transient
    @NotLogged
    private Double valorBaseCalculo;

    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Empresa empresa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private Pessoa pessoa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "responsavel", foreignKey = @ForeignKey(name = "fk_movparcela_responsavel"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario responsavel;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "vendaavulsa", foreignKey = @ForeignKey(name = "fk_movparcela_vendaavulsa"))
    @NotFound(action = NotFoundAction.IGNORE)
    private VendaAvulsa vendaAvulsa;

    @NotLogged
    @OneToMany(mappedBy = "movParcela", cascade = {CascadeType.REFRESH}, orphanRemoval = true)
    private Set<MovProdutoParcela> movProdutosParcelas;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "aulaavulsadiaria", foreignKey = @ForeignKey(name = "fk_movparcela_aulaavulsadiaria"))
    @NotFound(action = NotFoundAction.IGNORE)
    private AulaAvulsaDiaria aulaAvulsaDiaria;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public BigDecimal getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(BigDecimal valorParcela) {
        this.valorParcela = valorParcela;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public VendaAvulsa getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(VendaAvulsa vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public boolean getMovimentoCC() {
        return movimentoCC;
    }

    public void setMovimentoCC(boolean movimentoCC) {
        this.movimentoCC = movimentoCC;
    }

    public String getMovPagamentoCC() {
        return movPagamentoCC;
    }

    public void setMovPagamentoCC(String movPagamentoCC) {
        this.movPagamentoCC = movPagamentoCC;
    }

    public Double getValorBaseCalculo() {
        return valorBaseCalculo;
    }

    public void setValorBaseCalculo(Double valorBaseCalculo) {
        this.valorBaseCalculo = valorBaseCalculo;
    }

    public Double getPercentualJuro() {
        return percentualJuro;
    }

    public void setPercentualJuro(Double percentualJuro) {
        this.percentualJuro = percentualJuro;
    }

    public Double getPercentualMulta() {
        return percentualMulta;
    }

    public void setPercentualMulta(Double percentualMulta) {
        this.percentualMulta = percentualMulta;
    }

    public Usuario getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(Usuario responsavel) {
        this.responsavel = responsavel;
    }

    public Set<MovProdutoParcela> getMovProdutosParcelas() {
        if (movProdutosParcelas == null) {
            movProdutosParcelas = new HashSet<>();
        }
        return movProdutosParcelas;
    }

    public void setMovProdutosParcelas(Set<MovProdutoParcela> produtosParcelas) {
        this.movProdutosParcelas = produtosParcelas;
    }

    @Override
    public MovParcela clone() {
        MovParcela movParcela = new MovParcela();
        BeanUtils.copyProperties(this, movParcela, "movProdutosParcelas");

        this.getMovProdutosParcelas().forEach(mpp -> {
            try {
                movParcela.getMovProdutosParcelas().add(mpp.clone());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        return movParcela;
    }

    public Date getDataCobranca() {
        return dataCobranca;
    }

    public void setDataCobranca(Date dataCobranca) {
        this.dataCobranca = dataCobranca;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MovParcela that = (MovParcela) o;
        return movimentoCC == that.movimentoCC && Objects.equals(codigo, that.codigo)
                && Objects.equals(descricao, that.descricao)
                && Objects.equals(valorParcela, that.valorParcela)
                && Objects.equals(situacao, that.situacao)
                && Objects.equals(dataVencimento, that.dataVencimento)
                && Objects.equals(dataLancamento, that.dataLancamento)
                && Objects.equals(contrato, that.contrato)
                && Objects.equals(movPagamentoCC, that.movPagamentoCC)
                && Objects.equals(percentualJuro, that.percentualJuro)
                && Objects.equals(percentualMulta, that.percentualMulta)
                && Objects.equals(valorBaseCalculo, that.valorBaseCalculo)
                && Objects.equals(empresa, that.empresa) && Objects.equals(pessoa, that.pessoa)
                && Objects.equals(responsavel, that.responsavel)
                && Objects.equals(vendaAvulsa, that.vendaAvulsa)
                && Objects.equals(movProdutosParcelas, that.movProdutosParcelas);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo, descricao, valorParcela, situacao, dataVencimento, dataLancamento, contrato, movimentoCC, movPagamentoCC, percentualJuro, percentualMulta, valorBaseCalculo, empresa, pessoa, responsavel, vendaAvulsa);
    }

    public Integer getNrTentativas() {
        return nrTentativas;
    }

    public void setNrTentativas(Integer nrTentativas) {
        this.nrTentativas = nrTentativas;
    }

    public AulaAvulsaDiaria getAulaAvulsaDiaria() {
        return aulaAvulsaDiaria;
    }

    public void setAulaAvulsaDiaria(AulaAvulsaDiaria aulaAvulsaDiaria) {
        this.aulaAvulsaDiaria = aulaAvulsaDiaria;
    }
}
