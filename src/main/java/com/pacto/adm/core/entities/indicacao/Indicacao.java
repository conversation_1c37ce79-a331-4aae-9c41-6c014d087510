package com.pacto.adm.core.entities.indicacao;

import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Colaborador;
import com.pacto.adm.core.entities.Evento;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.indicado.Indicado;
import com.pacto.adm.core.enumerador.OrigemSistemaEnum;
import com.pacto.adm.core.enumerador.origemsistema.OrigemSistemaEnumConverter;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "indicacao")
public class Indicacao {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    private Date dia;

    private String observacao;

    @Convert(converter = OrigemSistemaEnumConverter.class)
    @Column(name = "origemsistema", columnDefinition = "DEFAULT 1")
    private OrigemSistemaEnum origemSistema;

    @ManyToOne
    @JoinColumn(name = "clientequeindicou", foreignKey = @ForeignKey(name = "fk_indicacao_clientequeindicou"))
    private Cliente clienteQueIndicou;

    @ManyToOne
    @JoinColumn(name = "colaboradorqueindicou", foreignKey = @ForeignKey(name = "fk_indicacao_colaboradorqueindicou"))
    private Colaborador colaboradorQueIndicou;

    @ManyToOne
    @JoinColumn(name = "colaboradorresponsavel", foreignKey = @ForeignKey(name = "fk_indicacao_colaboradorresponsavel"))
    private Usuario colaboradorResponsavel;

    @ManyToOne
    @JoinColumn(name = "evento", foreignKey = @ForeignKey(name = "fk_indicacao_evento"))
    private Evento evento;

    @ManyToOne
    @JoinColumn(name = "responsavelcadastro", foreignKey = @ForeignKey(name = "fk_indicacao_responsavelcadastro"))
    private Usuario responsavelCadastro;

    @Transient
    private List<Indicado> indicados;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public OrigemSistemaEnum getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }

    public Cliente getClienteQueIndicou() {
        return clienteQueIndicou;
    }

    public void setClienteQueIndicou(Cliente clienteQueIndicou) {
        this.clienteQueIndicou = clienteQueIndicou;
    }

    public Colaborador getColaboradorQueIndicou() {
        return colaboradorQueIndicou;
    }

    public void setColaboradorQueIndicou(Colaborador colaboradorQueIndicou) {
        this.colaboradorQueIndicou = colaboradorQueIndicou;
    }

    public Usuario getColaboradorResponsavel() {
        return colaboradorResponsavel;
    }

    public void setColaboradorResponsavel(Usuario colaboradorResponsavel) {
        this.colaboradorResponsavel = colaboradorResponsavel;
    }

    public Evento getEvento() {
        return evento;
    }

    public void setEvento(Evento evento) {
        this.evento = evento;
    }

    public Usuario getResponsavelCadastro() {
        return responsavelCadastro;
    }

    public void setResponsavelCadastro(Usuario responsavelCadastro) {
        this.responsavelCadastro = responsavelCadastro;
    }

    public List<Indicado> getIndicados() {
        return indicados;
    }

    public void setIndicados(List<Indicado> indicados) {
        this.indicados = indicados;
    }
}
