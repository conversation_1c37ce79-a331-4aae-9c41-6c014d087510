package com.pacto.adm.core.entities.contrato;

import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Colaborador;
import com.pacto.adm.core.entities.Pessoa;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.grupodesconto.GrupoDesconto;
import com.pacto.adm.core.entities.conveniodesconto.ConvenioDesconto;
import com.pacto.adm.core.enumerador.TipoHorarioCreditoTreinoEnum;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.springframework.beans.BeanUtils;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Set;


@Entity
@Table(name = "contrato")
public class Contrato implements Cloneable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Date dataLancamento;
    private Date dataAlteracaoManual;
    private Date vigenciaDe;
    private Date vigenciaAte;
    private Date vigenciaAteAjustada;
    private Date dataPrevistaRenovar;
    private Date dataPrevistaRematricula;
    private Date dataRenovarRealizada;
    @Enumerated(EnumType.STRING)
    private SituacaoEnum situacao;
    @Enumerated(EnumType.STRING)
    private SituacaoContratoEnum situacaoContrato;
    private boolean vendaCreditoTreino;
    private boolean vendaCreditoSessao;
    private boolean permiteRenovacaoAutomatica;
    private Boolean regimeRecorrencia;
    private Double valorBaseCalculo;
    private Integer contratoResponsavelRenovacaoMatricula;
    private Integer contratoResponsavelRematriculaMatricula;
    private Integer contratoBaseadoRenovacao;
    private Integer contratoBaseadoRematricula;
    private Double valorFinal;
    private Integer origemContrato;
    private Integer origemSistema;
    private Boolean naoPermitirRenovacaoRematriculaDeContratoAnteriores;
    private String observacao;
    private Boolean bolsa;
    @Column(name = "nomemodalidades", length = 50)
    private String nomeModalidades;
    @Transient
    private Date vigenciaTurmaCreditoTreinoAte;
    @Transient
    private Double valorPago;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_contrato_empresa"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Empresa empresa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "responsavelcontrato", foreignKey = @ForeignKey(name = "fk_contrato_responsavel"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario responsavelContrato;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "responsaveldatabase", foreignKey = @ForeignKey(name = "fk_contrato_responsaveldatabase"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario responsavelDataBase;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "fk_contrato_pessoa"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Pessoa pessoa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "pessoaOriginal", foreignKey = @ForeignKey(name = "fk_contrato_pessoaoriginal"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Pessoa pessoaOriginal;

    @OneToOne(mappedBy = "contrato", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private ContratoDuracao contratoDuracao;

    @OneToMany(mappedBy = "contrato", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<ContratoModalidade> modalidades;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "plano", foreignKey = @ForeignKey(name = "fk_contrato_plano"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Plano plano;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "consultor", foreignKey = @ForeignKey(name = "fk_contrato_consultor"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Colaborador consultor;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "grupo", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private GrupoDesconto grupoDesconto;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "conveniodesconto", foreignKey = @ForeignKey(name = "fk_contrato_conveniodesconto"))
    private ConvenioDesconto convenioDesconto;

    @RelationalField
    @OneToOne(mappedBy = "contrato")
    private ContratoRecorrencia contratoRecorrencia;

    @Transient
    private Boolean permiteNovoContratoDeOutraEmpresa;

    @Transient
    private String descricaoCondicaoPagamento;

    @Transient
    private String descricaoHorario;

    @Transient
    private Cliente cliente;

    @Transient
    private String emailsCliente;

    @Transient
    private String telefonesCliente;

    @Transient
    private Boolean contratoRenovavel;

    @Transient
    private Boolean temAutorizacaoCobranca;

    @Transient
    private String situacaoContratoSW;

    public Contrato() {}

    public Contrato(
            Integer codigo, Date vigenciaDe, Date vigenciaAteAjustada, String nomeModalidades, String emailsCliente,
            String telefonesCliente, String situacaoContratoSW, Boolean contratoRenovavel, Boolean temAutorizacaoCobranca, Pessoa pessoa,
            Cliente cliente, Plano plano, ContratoDuracao contratoDuracao, Empresa empresa
    ) {
        this.codigo = codigo;
        this.vigenciaDe = vigenciaDe;
        this.vigenciaAteAjustada = vigenciaAteAjustada;
        this.nomeModalidades = nomeModalidades;
        this.emailsCliente = emailsCliente;
        this.telefonesCliente = telefonesCliente;
        this.situacaoContratoSW = situacaoContratoSW;
        this.contratoRenovavel = contratoRenovavel;
        this.temAutorizacaoCobranca = temAutorizacaoCobranca;
        this.pessoa = pessoa;
        this.cliente = cliente;
        this.plano = plano;
        this.contratoDuracao = contratoDuracao;
        this.empresa = empresa;
    }

    public Contrato(Integer codigo, Date vigenciaDe, Date vigenciaAteAjustada, Date dataAlteracaoManual, Date dataLancamento, Cliente cliente, Usuario responsavelDataBase, ContratoDuracao contratoDuracao) {
        this.codigo = codigo;
        this.vigenciaDe = vigenciaDe;
        this.vigenciaAteAjustada = vigenciaAteAjustada;
        this.dataAlteracaoManual = dataAlteracaoManual;
        this.dataLancamento = dataLancamento;
        this.cliente = cliente;
        this.responsavelDataBase = responsavelDataBase;
        this.contratoDuracao = contratoDuracao;
    }

    public Colaborador getConsultor() {
        return consultor;
    }

    public void setConsultor(Colaborador consultor) {
        this.consultor = consultor;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public SituacaoContratoEnum getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(SituacaoContratoEnum situacaocontrato) {
        this.situacaoContrato = situacaocontrato;
    }

    public Usuario getResponsavelContrato() {
        return responsavelContrato;
    }

    public void setResponsavelContrato(Usuario responsavelContrato) {
        this.responsavelContrato = responsavelContrato;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getVigenciaDe() {
        return vigenciaDe;
    }

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }

    public Integer getOrigemContrato() {
        return origemContrato;
    }

    public void setOrigemContrato(Integer origemContrato) {
        this.origemContrato = origemContrato;
    }

    public void setVigenciaDe(Date vigenciaDe) {
        this.vigenciaDe = vigenciaDe;
    }

    public Date getVigenciaAte() {
        return vigenciaAte;
    }

    public void setVigenciaAte(Date vigenciaAte) {
        this.vigenciaAte = vigenciaAte;
    }

    public Date getVigenciaAteAjustada() {
        return vigenciaAteAjustada;
    }

    public void setVigenciaAteAjustada(Date vigenciaAteAjustada) {
        this.vigenciaAteAjustada = vigenciaAteAjustada;
    }

    public Date getDataPrevistaRenovar() {
        return dataPrevistaRenovar;
    }

    public boolean isVendaCreditoSessao() {
        return vendaCreditoSessao;
    }

    public void setVendaCreditoSessao(boolean vendaCreditoSessao) {
        this.vendaCreditoSessao = vendaCreditoSessao;
    }

    public void setDataPrevistaRenovar(Date vigenciaPrevistaRenovar) {
        this.dataPrevistaRenovar = vigenciaPrevistaRenovar;
    }

    public Date getDataPrevistaRematricula() {
        return dataPrevistaRematricula;
    }

    public void setDataPrevistaRematricula(Date dataprevistarematricula) {
        this.dataPrevistaRematricula = dataprevistarematricula;
    }

    public SituacaoEnum getSituacao() {
        return situacao;
    }

    public Double getValorBaseCalculo() {
        return valorBaseCalculo;
    }

    public void setValorBaseCalculo(Double valorBaseCalculo) {
        this.valorBaseCalculo = valorBaseCalculo;
    }

    public void setSituacao(SituacaoEnum situacao) {
        this.situacao = situacao;
    }

    public boolean isPermiteRenovacaoAutomatica() {
        return permiteRenovacaoAutomatica;
    }

    public void setPermiteRenovacaoAutomatica(boolean permiteRenovacaoAutomatica) {
        this.permiteRenovacaoAutomatica = permiteRenovacaoAutomatica;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Pessoa getPessoaOriginal() {
        return pessoaOriginal;
    }

    public void setPessoaOriginal(Pessoa pessoaOriginal) {
        this.pessoaOriginal = pessoaOriginal;
    }

    public boolean isVendaCreditoTreino() {
        return vendaCreditoTreino;
    }

    public void setVendaCreditoTreino(boolean vendacreditotreino) {
        this.vendaCreditoTreino = vendacreditotreino;
    }

    public ContratoDuracao getContratoDuracao() {
        return contratoDuracao;
    }

    public void setContratoDuracao(ContratoDuracao contratoDuracao) {
        this.contratoDuracao = contratoDuracao;
    }

    public Integer getContratoResponsavelRenovacaoMatricula() {
        return contratoResponsavelRenovacaoMatricula;
    }

    public void setContratoResponsavelRenovacaoMatricula(Integer contratoResponsavelRenovacaoMatricula) {
        this.contratoResponsavelRenovacaoMatricula = contratoResponsavelRenovacaoMatricula;
    }

    public Integer getContratoResponsavelRematriculaMatricula() {
        return contratoResponsavelRematriculaMatricula;
    }

    public void setContratoResponsavelRematriculaMatricula(Integer contratoResponsavelRematriculaMatricula) {
        this.contratoResponsavelRematriculaMatricula = contratoResponsavelRematriculaMatricula;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Integer getContratoBaseadoRematricula() {
        return contratoBaseadoRematricula;
    }

    public void setContratoBaseadoRematricula(Integer contratoBaseadoRematricula) {
        this.contratoBaseadoRematricula = contratoBaseadoRematricula;
    }

    public Integer getContratoBaseadoRenovacao() {
        return contratoBaseadoRenovacao;
    }

    public void setContratoBaseadoRenovacao(Integer contratoBaseadoRenovacao) {
        this.contratoBaseadoRenovacao = contratoBaseadoRenovacao;
    }

    public Plano getPlano() {
        return plano;
    }

    public void setPlano(Plano plano) {
        this.plano = plano;
    }

    public Date getVigenciaTurmaCreditoTreinoAte() {
        return vigenciaTurmaCreditoTreinoAte;
    }

    public void setVigenciaTurmaCreditoTreinoAte(Date vigenciaTurmaCreditoTreinoAte) {
        this.vigenciaTurmaCreditoTreinoAte = vigenciaTurmaCreditoTreinoAte;
    }

    public Set<ContratoModalidade> getModalidades() {
        return modalidades;
    }

    public void setModalidades(Set<ContratoModalidade> contratoModalidade) {
        this.modalidades = contratoModalidade;
    }

    public Integer obterNrDiasRestantesProFinalDoContrato(Integer nrDiasContrato, Integer nrDiasUtilizadosPeloCliente) {
        return nrDiasContrato - nrDiasUtilizadosPeloCliente;
    }


    public String getVigenciaAteAjustada_Apresentar() {
        String data = Uteis.getData(vigenciaAteAjustada);
        if (UteisValidacao.emptyString(data)) {
            return " - ";
        }
        return data;
    }

    public String getDataPrevistaRenovar_Apresentar() {
        return Uteis.getData(dataPrevistaRenovar);
    }

    public void obterDataFinalContratoComContratoDuracao(Date dataInicio) throws Exception {
        int i = 0;
        Long nrDiasReal = Uteis.nrDiasEntreDatas(this.vigenciaDe, this.vigenciaAteAjustada);
        this.vigenciaDe = dataInicio;
        Date dataAtual = Uteis.obterDataAnterior(this.getVigenciaDe(), 1);
        Date data = (Uteis.obterDataFuturaParcela(dataAtual, this.getContratoDuracao().getNumeroMeses()));

        if (this.getContratoDuracao().getQuantidadeDiasExtra() > 0) {
            data = Calendario.somarDias(Calendario.getDataComHoraZerada(this.getVigenciaDe()), this.getContratoDuracao().getQuantidadeDiasExtra() + this.getContratoDuracao().getNumeroMeses() * 30);
        }

        if (Calendario.igual(this.vigenciaAte, this.vigenciaAteAjustada)) {
            this.setVigenciaAteAjustada(data);
            this.setDataPrevistaRenovar(data);
            this.setDataPrevistaRematricula(data);
        } else {
            Date dataAjustada = Calendario.somarDias(dataAtual, nrDiasReal.intValue());
            this.setVigenciaAteAjustada(dataAjustada);
            this.setDataPrevistaRenovar(dataAjustada);
            this.setDataPrevistaRematricula(dataAjustada);
        }
        this.setVigenciaAte(data);
        this.setDataLancamento(Calendario.hoje());
    }

    public void gerarVigenciaMatriculaPlanoCreditoTreino(Date dataInicio, Integer quantidadeCredito) throws Exception {
        if (!(getPlano().getVendaCreditoTreino() && getContratoDuracao().getContratoDuracaoCreditoTreino().getTipoHorario() == TipoHorarioCreditoTreinoEnum.HORARIO_TURMA)) {
            return;
        }
        if (getVigenciaTurmaCreditoTreinoAte() != null) {
            // neste caso já foi povoada na tela de manutenção de modalidade.
            return;
        }
        List<HorarioTurma> listaHorarioTurma = new ArrayList<HorarioTurma>();
        for (ContratoModalidade contratoModalidade : getModalidades()) {
            if (contratoModalidade.getModalidade().getUtilizarturma()) {
                for (Object obj : contratoModalidade.getTurmas()) {
                    ContratoModalidadeTurma contratoModalidadeTurma = (ContratoModalidadeTurma) obj;
                    // TODO (lucasaraujo) Verificar como o valor do campo Turma escolhida é atribuido, pois esse campo não existe no banco de dados.
                    if (contratoModalidadeTurma.getTurma().getTurmaEscolhida()) {
                        for (Object objModTurmaHorario : contratoModalidadeTurma.getTurmasHorarios()) {
                            ContratoModalidadeHorarioTurma contratoModalidadeHorarioTurmaVO = (ContratoModalidadeHorarioTurma) objModTurmaHorario;
                            // TODO (lucasaraujo) Verificar como o valor do campo Horario Turma Escolhida é atribuido, pois esse campo não existe no banco de dados.
                            if (contratoModalidadeHorarioTurmaVO.getHorarioTurma().getHorarioTurmaEscolhida()) {
                                listaHorarioTurma.add(contratoModalidadeHorarioTurmaVO.getHorarioTurma());
                            }
                        }
                    }
                }
            }
        }
        List<Date> listaDias = Uteis.getDiasEntreDatas(dataInicio, this.getVigenciaAteAjustada());
        int totalCredito = quantidadeCredito;
        Date ultimoDiaUtilizar = this.getVigenciaAteAjustada();
        for (Date dia : listaDias) {
            Calendar diaVerificar = Calendario.getInstance();
            diaVerificar.setTime(dia);
            for (HorarioTurma horarioTurmaVO : listaHorarioTurma) {
                if (horarioTurmaVO.getDiaSemanaNumero() == diaVerificar.get(Calendar.DAY_OF_WEEK)) {
                    totalCredito = totalCredito - 1;
                    break;
                }
            }
            if (totalCredito <= 0) {
                ultimoDiaUtilizar = dia;
                break;
            }
        }
        setVigenciaTurmaCreditoTreinoAte(ultimoDiaUtilizar);
    }

    @Override
    public Contrato clone() {
        Contrato contrato = new Contrato();
        BeanUtils.copyProperties(this, contrato);
        return contrato;
    }

    public Boolean getNaoPermitirRenovacaoRematriculaDeContratoAnteriores() {
        if (naoPermitirRenovacaoRematriculaDeContratoAnteriores == null) {
            naoPermitirRenovacaoRematriculaDeContratoAnteriores = false;
        }
        return naoPermitirRenovacaoRematriculaDeContratoAnteriores;
    }

    public void setNaoPermitirRenovacaoRematriculaDeContratoAnteriores(Boolean naoPermitirRenovacaoRematriculaDeContratoAnteriores) {
        this.naoPermitirRenovacaoRematriculaDeContratoAnteriores = naoPermitirRenovacaoRematriculaDeContratoAnteriores;
    }

    public Date getDataRenovarRealizada() {
        return dataRenovarRealizada;
    }

    public void setDataRenovarRealizada(Date dataRenovarRealizada) {
        this.dataRenovarRealizada = dataRenovarRealizada;
    }

    public ContratoRecorrencia getContratoRecorrencia() {
        return contratoRecorrencia;
    }

    public void setContratoRecorrencia(ContratoRecorrencia contratoRecorrencia) {
        this.contratoRecorrencia = contratoRecorrencia;
    }

    public GrupoDesconto getGrupoDesconto() {
        return grupoDesconto;
    }

    public void setGrupoDesconto(GrupoDesconto grupoDesconto) {
        this.grupoDesconto = grupoDesconto;
    }

    public ConvenioDesconto getConvenioDesconto() {
        return convenioDesconto;
    }

    public void setConvenioDesconto(ConvenioDesconto convenioDesconto) {
        this.convenioDesconto = convenioDesconto;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(Integer origemSistema) {
        this.origemSistema = origemSistema;
    }

    public Date getDataAlteracaoManual() {
        return dataAlteracaoManual;
    }

    public void setDataAlteracaoManual(Date dataAlteracaoManual) {
        this.dataAlteracaoManual = dataAlteracaoManual;
    }

    public Boolean getBolsa() {
        return bolsa;
    }

    public void setBolsa(Boolean bolsa) {
        this.bolsa = bolsa;
    }

    public Boolean getPermiteNovoContratoDeOutraEmpresa() {
        return permiteNovoContratoDeOutraEmpresa;
    }

    public void setPermiteNovoContratoDeOutraEmpresa(Boolean permiteNovoContratoDeOutraEmpresa) {
        this.permiteNovoContratoDeOutraEmpresa = permiteNovoContratoDeOutraEmpresa;
    }

    public Boolean getRegimeRecorrencia() {
        return regimeRecorrencia;
    }

    public void setRegimeRecorrencia(Boolean regimerecorrencia) {
        this.regimeRecorrencia = regimerecorrencia;
    }

    public Double getValorPago() {
        return valorPago;
    }

    public void setValorPago(Double valorPago) {
        this.valorPago = valorPago;
    }

    public String getDescricaoCondicaoPagamento() {
        return descricaoCondicaoPagamento;
    }

    public void setDescricaoCondicaoPagamento(String descricaoCondicaoPagamento) {
        this.descricaoCondicaoPagamento = descricaoCondicaoPagamento;
    }

    public String getDescricaoHorario() {
        return descricaoHorario;
    }

    public void setDescricaoHorario(String descricaoHorario) {
        this.descricaoHorario = descricaoHorario;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public Usuario getResponsavelDataBase() {
        return responsavelDataBase;
    }

    public void setResponsavelDataBase(Usuario responsavelDataBase) {
        this.responsavelDataBase = responsavelDataBase;
    }

    public String getNomeModalidades() {
        return nomeModalidades;
    }

    public void setNomeModalidades(String nomeModalidades) {
        this.nomeModalidades = nomeModalidades;
    }

    public String getEmailsCliente() {
        return emailsCliente;
    }

    public void setEmailsCliente(String emailsCliente) {
        this.emailsCliente = emailsCliente;
    }

    public String getTelefonesCliente() {
        return telefonesCliente;
    }

    public void setTelefonesCliente(String telefonesCliente) {
        this.telefonesCliente = telefonesCliente;
    }

    public Boolean getContratoRenovavel() {
        return contratoRenovavel;
    }

    public void setContratoRenovavel(Boolean contratoRenovavel) {
        this.contratoRenovavel = contratoRenovavel;
    }

    public Boolean getTemAutorizacaoCobranca() {
        return temAutorizacaoCobranca;
    }

    public void setTemAutorizacaoCobranca(Boolean temAutorizacaoCobranca) {
        this.temAutorizacaoCobranca = temAutorizacaoCobranca;
    }

    public String getSituacaoContratoSW() {
        return situacaoContratoSW;
    }

    public void setSituacaoContratoSW(String situacaoContratoSW) {
        this.situacaoContratoSW = situacaoContratoSW;
    }
}
