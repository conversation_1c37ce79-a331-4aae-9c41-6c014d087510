package com.pacto.adm.core.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
public class ConfiguracaoEmpresaHubSpot {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String clientSecret;
    private boolean empresaUsaHub;
    private String horaExpiracao;
    private String url_instalacao;
    private String url_redirect;
    private String clientId;
    private String appId;
    private String token;
    private String horaLimite;
    private int acaoObjecao;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "responsavelPadrao", foreignKey = @ForeignKey(name = "configuracaoempresardstation_usuario_fkey"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario responsavelPadrao;

    @JsonIgnore
    @OneToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "configuracaoempresardstation_empresa_fkey"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Empresa empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientsecret) {
        this.clientSecret = clientsecret;
    }

    public boolean isEmpresaUsaHub() {
        return empresaUsaHub;
    }

    public void setEmpresaUsaHub(boolean empresausahub) {
        this.empresaUsaHub = empresausahub;
    }

    public String getHoraExpiracao() {
        return horaExpiracao;
    }

    public void setHoraExpiracao(String horaexpiracao) {
        this.horaExpiracao = horaexpiracao;
    }

    public String getUrl_instalacao() {
        return url_instalacao;
    }

    public void setUrl_instalacao(String url_instalacao) {
        this.url_instalacao = url_instalacao;
    }

    public String getUrl_redirect() {
        return url_redirect;
    }

    public void setUrl_redirect(String url_redirect) {
        this.url_redirect = url_redirect;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getHoraLimite() {
        return horaLimite;
    }

    public void setHoraLimite(String horaLimite) {
        this.horaLimite = horaLimite;
    }

    public int getAcaoObjecao() {
        return acaoObjecao;
    }

    public void setAcaoObjecao(int acaoObjecao) {
        this.acaoObjecao = acaoObjecao;
    }

    public Usuario getResponsavelPadrao() {
        return responsavelPadrao;
    }

    public void setResponsavelPadrao(Usuario responsavelPadrao) {
        this.responsavelPadrao = responsavelPadrao;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
