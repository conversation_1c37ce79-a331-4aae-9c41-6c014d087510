package com.pacto.adm.core.entities;

import io.swagger.v3.oas.annotations.media.Schema;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;

@Entity
@Schema(name = "Evento", description = "Informações sobre eventos, incluindo descrição, status e período de vigência.")
public class Evento {

    @Schema(description = "Código único identificador do evento.", example = "1234")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Schema(description = "Descrição detalhada do evento.", example = "Aula de Spinning")
    private String descricao;

    @Schema(description = "Status atual do evento.", example = "Ativo")
    private String status;

    @Schema(description = "Data de início da vigência do evento.", example = "2025-06-01T08:00:00Z")
    private Date vigenciaInicial;

    @Schema(description = "Data final da vigência do evento.", example = "2025-06-01T09:00:00Z")
    private Date vigenciaFinal;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getVigenciaInicial() {
        return vigenciaInicial;
    }

    public void setVigenciaInicial(Date vigenciaInicial) {
        this.vigenciaInicial = vigenciaInicial;
    }

    public Date getVigenciaFinal() {
        return vigenciaFinal;
    }

    public void setVigenciaFinal(Date vigenciaFinal) {
        this.vigenciaFinal = vigenciaFinal;
    }
}
