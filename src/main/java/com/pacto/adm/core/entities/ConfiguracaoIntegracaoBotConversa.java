package com.pacto.adm.core.entities;

import javax.persistence.*;

@Entity
public class ConfiguracaoIntegracaoBotConversa {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Boolean ativo;
    private String urlWebHooBotConversa;
    private Integer empresa;
    private String descricao;
    private Integer tipoFluxo;
    private String fase;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUrlWebHooBotConversa() {
        return urlWebHooBotConversa;
    }

    public void setUrlWebHooBotConversa(String urlWebHooBotConversa) {
        this.urlWebHooBotConversa = urlWebHooBotConversa;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getTipoFluxo() {
        return tipoFluxo;
    }

    public void setTipoFluxo(Integer tipoFluxo) {
        this.tipoFluxo = tipoFluxo;
    }

    public String getFase() {
        return fase;
    }

    public void setFase(String fase) {
        this.fase = fase;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
