package com.pacto.adm.core.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
public class ConfiguracaoIntegracaoWordPress {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer acaoObjecao;
    private Boolean habilitada;
    private String horaLimite;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "responsavelPadrao", foreignKey = @ForeignKey(name = "configuracaointegracaowordpress_usuario_fkey"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario responsavelPadrao;

    @JsonIgnore
    @OneToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "configuracaointegracaowordpress_empresa_fkey"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Empresa empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getAcaoObjecao() {
        return acaoObjecao;
    }

    public void setAcaoObjecao(Integer acaoObjecao) {
        this.acaoObjecao = acaoObjecao;
    }

    public Boolean getHabilitada() {
        return habilitada;
    }

    public void setHabilitada(Boolean habilitada) {
        this.habilitada = habilitada;
    }

    public String getHoraLimite() {
        return horaLimite;
    }

    public void setHoraLimite(String horaLimite) {
        this.horaLimite = horaLimite;
    }

    public Usuario getResponsavelPadrao() {
        return responsavelPadrao;
    }

    public void setResponsavelPadrao(Usuario responsavelPadrao) {
        this.responsavelPadrao = responsavelPadrao;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
}
