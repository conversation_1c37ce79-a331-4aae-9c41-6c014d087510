package com.pacto.adm.core.entities;

import com.pacto.adm.core.entities.contrato.Modalidade;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@Entity
public class ProdutoSugerido implements Cloneable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "obrigatorio")
    private Boolean obrigatorio;

    @ManyToOne
    @JoinColumn(name = "produto", nullable = false, foreignKey = @ForeignKey(name = "fk_produtosugerido_produto"))
    private Produto produto;

    @ManyToOne
    @JoinColumn(name = "modalidade", nullable = false, foreignKey = @ForeignKey(name = "fk_produtosugerido_modalidade"))
    private Modalidade modalidade;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getObrigatorio() {
        return obrigatorio;
    }

    public void setObrigatorio(Boolean obrigatorio) {
        this.obrigatorio = obrigatorio;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public Modalidade getModalidade() {
        return modalidade;
    }

    public void setModalidade(Modalidade modalidade) {
        this.modalidade = modalidade;
    }

    @Override
    public ProdutoSugerido clone() {
        ProdutoSugerido produtoSugerido = new ProdutoSugerido();
        BeanUtils.copyProperties(this, produtoSugerido);
        return produtoSugerido;
    }

}
