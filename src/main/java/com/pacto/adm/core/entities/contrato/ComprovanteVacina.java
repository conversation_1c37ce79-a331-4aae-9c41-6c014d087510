package com.pacto.adm.core.entities.contrato;

import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.Pessoa;
import com.pacto.adm.core.entities.Usuario;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

@Entity
public class ComprovanteVacina implements Cloneable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer tipo;
    private Date dataAplicacao;
    private String fabricante;
    private String lote;
    private String estabelecimentoSaude;
    private String vacinador;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "comprovantevacina_pessoa_fkey"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Pessoa pessoa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "usuarioresponsavel", foreignKey = @ForeignKey(name = "comprovantevacina_usuarioresponsavel_fkey"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario usuarioResponsavel;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public Date getDataAplicacao() {
        return dataAplicacao;
    }

    public void setDataAplicacao(Date dataAplicacao) {
        this.dataAplicacao = dataAplicacao;
    }

    public String getFabricante() {
        return fabricante;
    }

    public void setFabricante(String fabricante) {
        this.fabricante = fabricante;
    }

    public String getLote() {
        return lote;
    }

    public void setLote(String lote) {
        this.lote = lote;
    }

    public String getEstabelecimentoSaude() {
        return estabelecimentoSaude;
    }

    public void setEstabelecimentoSaude(String estabelecimentoSaude) {
        this.estabelecimentoSaude = estabelecimentoSaude;
    }

    public String getVacinador() {
        return vacinador;
    }

    public void setVacinador(String vacinador) {
        this.vacinador = vacinador;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Usuario getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(Usuario usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    @Override
    public ComprovanteVacina clone() {
        ComprovanteVacina comprovanteVacina = new ComprovanteVacina();
        BeanUtils.copyProperties(this, comprovanteVacina);
        return comprovanteVacina;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ComprovanteVacina that = (ComprovanteVacina) o;
        return Objects.equals(codigo, that.codigo) && Objects.equals(tipo, that.tipo)
                && Objects.equals(dataAplicacao, that.dataAplicacao)
                && Objects.equals(fabricante, that.fabricante)
                && Objects.equals(lote, that.lote)
                && Objects.equals(estabelecimentoSaude, that.estabelecimentoSaude)
                && Objects.equals(vacinador, that.vacinador)
                && Objects.equals(pessoa, that.pessoa)
                && Objects.equals(usuarioResponsavel, that.usuarioResponsavel);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo, tipo, dataAplicacao, fabricante, lote, estabelecimentoSaude, vacinador, pessoa, usuarioResponsavel);
    }
}
