package com.pacto.adm.core.entities;

import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.config.annotations.RelationalField;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.entities.financeiro.VendaAvulsa;
import com.pacto.adm.core.entities.recibodevolucao.ReciboDevolucao;
import lombok.Getter;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.springframework.beans.BeanUtils;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

@Entity
public class MovProduto implements Cloneable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Column(name = "descricao", nullable = false)
    private String descricao;
    private Integer quantidade;
    private BigDecimal precoUnitario;
    private BigDecimal valorDesconto;
    @Getter
    private BigDecimal valorFaturado;
    private BigDecimal totalFinal;
    private String situacao;
    private Double juros;
    private Double multa;
    private Double jurosNaoRecebidos;
    private Double multaNaoRecebida;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "datalancamento")
    private Date dataLancamento;
    @Column(name = "datainiciovigencia")
    private Date dataInicioVigencia;
    @Column(name = "datafinalvigencia")
    private Date dataFinalVigencia;
    private Integer anoReferencia;
    private String mesReferencia;
    private String movPagamentoCC;
    private boolean apresentarMovProduto;
    private boolean quitado;
    private boolean renovavelAutomaticamente;
    private Integer movProdutoOriginal;

    @Column(name = "numerocupomdesconto", length = 100)
    private String numeroCupomDesconto;

    @Column(name = "statusprotheus", length = 10)
    private String statusProtheus;

    @Transient
    private Date dataCancelamento;

    @Transient
    private Date dataPagamento;


    @ManyToOne
    @JoinColumn(name = "contrato", foreignKey = @ForeignKey(name = "fk_movproduto_contrato"))
    private Contrato contrato;

    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_movproduto_empresa"), nullable = false)
    private Empresa empresa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "pessoa", foreignKey = @ForeignKey(name = "fk_movproduto_pessoa"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Pessoa pessoa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "produto", foreignKey = @ForeignKey(name = "fk_movproduto_produto"), nullable = false)
    private Produto produto;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "vendaavulsa", foreignKey = @ForeignKey(name = "fk_movproduto_vendaavulsa"))
    @NotFound(action = NotFoundAction.IGNORE)
    private VendaAvulsa vendaAvulsa;

    @RelationalField
    @ManyToOne
    @JoinColumn(name = "responsavellancamento", foreignKey = @ForeignKey(name = "fk_movproduto_responsavellancamento"))
    @NotFound(action = NotFoundAction.IGNORE)
    private Usuario responsavelLancamento;

    @OneToMany(mappedBy = "movProduto", cascade = {CascadeType.REFRESH}, orphanRemoval = true)
    private Set<MovProdutoParcela> movProdutosParcelas;
    private String linkNota;

    @Transient
    private ReciboDevolucao reciboDevolucao;
    @Transient
    private BigDecimal valorParcialmentePago;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Contrato getContrato() {
        return contrato;
    }

    public void setContrato(Contrato contrato) {
        this.contrato = contrato;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public BigDecimal getPrecoUnitario() {
        return precoUnitario;
    }

    public void setPrecoUnitario(BigDecimal precoUnitario) {
        this.precoUnitario = precoUnitario;
    }

    public BigDecimal getValorDesconto() {
        return valorDesconto;
    }

    public void setValorDesconto(BigDecimal valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataInicioVigencia() {
        return dataInicioVigencia;
    }

    public void setDataInicioVigencia(Date dataInicioVigencia) {
        this.dataInicioVigencia = dataInicioVigencia;
    }

    public Date getDataFinalVigencia() {
        return dataFinalVigencia;
    }

    public void setDataFinalVigencia(Date dataFinalVigencia) {
        this.dataFinalVigencia = dataFinalVigencia;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public BigDecimal getTotalFinal() {
        return totalFinal;
    }

    public void setTotalFinal(BigDecimal totalFinal) {
        this.totalFinal = totalFinal;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public VendaAvulsa getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(VendaAvulsa vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public boolean isApresentarMovProduto() {
        return apresentarMovProduto;
    }

    public void setApresentarMovProduto(boolean apresentarMovProduto) {
        this.apresentarMovProduto = apresentarMovProduto;
    }

    public Integer getAnoReferencia() {
        return anoReferencia;
    }

    public void setAnoReferencia(Integer anoReferencia) {
        this.anoReferencia = anoReferencia;
    }

    public String getMesReferencia() {
        return mesReferencia;
    }

    public void setMesReferencia(String mesReferencia) {
        this.mesReferencia = mesReferencia;
    }

    public String getMovPagamentoCC() {
        return movPagamentoCC;
    }

    public void setMovPagamentoCC(String movPagamentoCC) {
        this.movPagamentoCC = movPagamentoCC;
    }

    public Usuario getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(Usuario responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public boolean isQuitado() {
        return quitado;
    }

    public void setQuitado(boolean quitado) {
        this.quitado = quitado;
    }

    public Set<MovProdutoParcela> getMovProdutosParcelas() {
        if (movProdutosParcelas == null) {
            movProdutosParcelas = new HashSet<>();
        }
        return movProdutosParcelas;
    }

    public void setMovProdutosParcelas(Set<MovProdutoParcela> produtosParcelas) {
        this.movProdutosParcelas = produtosParcelas;
    }

    public Integer getMovProdutoOriginal() {
        return movProdutoOriginal;
    }

    public void setMovProdutoOriginal(Integer movProdutoOriginal) {
        this.movProdutoOriginal = movProdutoOriginal;
    }

    public MovProduto clone() {
        MovProduto movProduto = new MovProduto();
        BeanUtils.copyProperties(this, movProduto, "movProdutosParcelas");

        this.getMovProdutosParcelas().forEach(mpp -> {
            try {
                movProduto.getMovProdutosParcelas().add(mpp.clone());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        return movProduto;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MovProduto that = (MovProduto) o;
        return apresentarMovProduto == that.apresentarMovProduto
                && quitado == that.quitado
                && Objects.equals(codigo, that.codigo)
                && Objects.equals(contrato, that.contrato)
                && Objects.equals(descricao, that.descricao)
                && Objects.equals(quantidade, that.quantidade)
                && Objects.equals(precoUnitario, that.precoUnitario)
                && Objects.equals(valorDesconto, that.valorDesconto)
                && Objects.equals(totalFinal, that.totalFinal)
                && Objects.equals(situacao, that.situacao)
                && Objects.equals(dataLancamento, that.dataLancamento)
                && Objects.equals(dataInicioVigencia, that.dataInicioVigencia)
                && Objects.equals(dataFinalVigencia, that.dataFinalVigencia)
                && Objects.equals(anoReferencia, that.anoReferencia)
                && Objects.equals(mesReferencia, that.mesReferencia)
                && Objects.equals(movPagamentoCC, that.movPagamentoCC)
                && Objects.equals(movProdutoOriginal, that.movProdutoOriginal)
                && Objects.equals(empresa, that.empresa)
                && Objects.equals(pessoa, that.pessoa)
                && Objects.equals(produto, that.produto)
                && Objects.equals(vendaAvulsa, that.vendaAvulsa)
                && Objects.equals(responsavelLancamento, that.responsavelLancamento)
                && Objects.equals(movProdutosParcelas, that.movProdutosParcelas);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo, contrato, descricao, quantidade, precoUnitario, valorDesconto, totalFinal, situacao, dataLancamento, dataInicioVigencia, dataFinalVigencia, anoReferencia, mesReferencia, movPagamentoCC, apresentarMovProduto, quitado, movProdutoOriginal, empresa, pessoa, produto, vendaAvulsa, responsavelLancamento);
    }

    public String getNumeroCupomDesconto() {
        return numeroCupomDesconto;
    }

    public void setNumeroCupomDesconto(String numeroCupomDesconto) {
        this.numeroCupomDesconto = numeroCupomDesconto;
    }

    public String getStatusProtheus() {
        return statusProtheus;
    }

    public void setStatusProtheus(String statusProtheus) {
        this.statusProtheus = statusProtheus;
    }

    public String getLinkNota() {
        return linkNota;
    }

    public void setLinkNota(String linkNota) {
        this.linkNota = linkNota;
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public ReciboDevolucao getReciboDevolucao() {
        return reciboDevolucao;
    }

    public void setReciboDevolucao(ReciboDevolucao reciboDevolucao) {
        this.reciboDevolucao = reciboDevolucao;
    }

    public BigDecimal getValorParcialmentePago() {
        return valorParcialmentePago;
    }

    public void setValorParcialmentePago(BigDecimal valorParcialmentePago) {
        this.valorParcialmentePago = valorParcialmentePago;
    }

    public boolean isRenovavelAutomaticamente() {
        return renovavelAutomaticamente;
    }

    public void setRenovavelAutomaticamente(boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public void setValorFaturado(BigDecimal valorFaturado) {
        this.valorFaturado = valorFaturado;
    }

    public BigDecimal getValorFaturado() {
        return valorFaturado;
    }

    public Double getJuros() {
        return juros;
    }

    public void setJuros(Double juros) {
        this.juros = juros;
    }

    public Double getMulta() {
        return multa;
    }

    public void setMulta(Double multa) {
        this.multa = multa;
    }

    public Double getJurosNaoRecebidos() {
        return jurosNaoRecebidos;
    }

    public void setJurosNaoRecebidos(Double jurosNaoRecebidos) {
        this.jurosNaoRecebidos = jurosNaoRecebidos;
    }

    public Double getMultaNaoRecebida() {
        return multaNaoRecebida;
    }

    public void setMultaNaoRecebida(Double multaNaoRecebida) {
        this.multaNaoRecebida = multaNaoRecebida;
    }
}
