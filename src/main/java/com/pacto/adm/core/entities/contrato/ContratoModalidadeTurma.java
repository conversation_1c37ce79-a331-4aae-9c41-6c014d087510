package com.pacto.adm.core.entities.contrato;

import javax.persistence.*;
import java.util.Set;

@Entity
public class ContratoModalidadeTurma {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @ManyToOne
    @JoinColumn(name = "turma", foreignKey = @ForeignKey(name = "fk_contratomodalidadeturma_turma"))
    private Turma turma;

    @ManyToOne
    @JoinColumn(name = "contratoModalidade", foreignKey = @ForeignKey(name = "fk_contratomodalidadeturma_contratomodalidade"))
    private ContratoModalidade contratoModalidade;

    @OneToMany(mappedBy = "contratoModalidadeTurma", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<ContratoModalidadeHorarioTurma> turmasHorarios;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Turma getTurma() {
        return turma;
    }

    public void setTurma(Turma turma) {
        this.turma = turma;
    }

    public ContratoModalidade getContratoModalidade() {
        return contratoModalidade;
    }

    public void setContratoModalidade(ContratoModalidade contratoModalidade) {
        this.contratoModalidade = contratoModalidade;
    }

    public Set<ContratoModalidadeHorarioTurma> getTurmasHorarios() {
        return turmasHorarios;
    }

    public void setTurmasHorarios(Set<ContratoModalidadeHorarioTurma> turmasHorarios) {
        this.turmasHorarios = turmasHorarios;
    }
}
