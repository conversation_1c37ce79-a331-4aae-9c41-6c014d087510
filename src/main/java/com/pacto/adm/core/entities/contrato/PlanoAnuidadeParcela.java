package com.pacto.adm.core.entities.contrato;

import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.util.Objects;

@Entity
public class PlanoAnuidadeParcela {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    @JoinColumn(name = "planoRecorrencia", foreignKey = @ForeignKey(name = "planoanuidadeparcela_planorecorrencia_codigo_fk"))
    private PlanoRecorrencia planoRecorrencia;
    private Integer numero;
    private Double valor;
    private Integer parcela;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PlanoRecorrencia getPlanoRecorrencia() {
        return planoRecorrencia;
    }

    public void setPlanoRecorrencia(PlanoRecorrencia planoRecorrencia) {
        this.planoRecorrencia = planoRecorrencia;
    }

    public Integer getNumero() {
        return numero;
    }

    public void setNumero(Integer numero) {
        this.numero = numero;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getParcela() {
        return parcela;
    }

    public void setParcela(Integer parcela) {
        this.parcela = parcela;
    }
}
