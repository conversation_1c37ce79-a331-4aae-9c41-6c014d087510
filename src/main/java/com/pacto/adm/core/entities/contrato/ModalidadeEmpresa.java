package com.pacto.adm.core.entities.contrato;

import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;

@Entity
public class ModalidadeEmpresa implements Cloneable{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @ManyToOne
    @JoinColumn(name = "empresa", foreignKey = @ForeignKey(name = "fk_modalidadeempresa_empresa"))
    private Empresa empresa;

    @ManyToOne
    @JoinColumn(name = "modalidade", foreignKey = @ForeignKey(name = "fk_modalidadeempresa_modalidade"))
    private Modalidade modalidade;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Modalidade getModalidade() {
        return modalidade;
    }

    public void setModalidade(Modalidade modalidade) {
        this.modalidade = modalidade;
    }

    @Override
    public ModalidadeEmpresa clone() {
        ModalidadeEmpresa modalidadeEmpresa = new ModalidadeEmpresa();
        BeanUtils.copyProperties(this, modalidadeEmpresa);
        return modalidadeEmpresa;
    }

}
