package com.pacto.adm.core.entities;

import com.pacto.config.annotations.RelationalField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "coletor", schema = "public")
public class Coletor {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String descricao;
    private String modelo;
    private String sentidoacesso;
    @OneToOne
    @RelationalField
    @JoinColumn(name = "localAcesso", foreignKey = @ForeignKey(name = "fk_localacesso_coletor"))
    private LocalAcesso localAcesso;
}
