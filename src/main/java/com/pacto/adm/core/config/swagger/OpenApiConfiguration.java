package com.pacto.adm.core.config.swagger;

import com.pacto.adm.core.dto.enveloperesposta.statushttp.*;
import io.swagger.v3.core.converter.ModelConverters;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.models.*;
import io.swagger.v3.oas.models.examples.Example;
import io.swagger.v3.oas.models.media.Content;
import io.swagger.v3.oas.models.media.MediaType;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.responses.ApiResponse;
import io.swagger.v3.oas.models.responses.ApiResponses;
import org.springdoc.core.customizers.OpenApiCustomiser;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.LinkedHashMap;
import java.util.Map;

@OpenAPIDefinition(
        info = @Info(
                title = "${swagger.config.info.title}",
                version = "${swagger.config.info.version}",
                description = "${swagger.config.info.description}"
        ),
        security = @io.swagger.v3.oas.annotations.security.SecurityRequirement(name = "bearerAuth"),
        tags = {
                @Tag(name = "Aviso Interno", description = "Operações de gestão de avisos internos"),
                @Tag(name = "Agenda de Aulas", description = "Operações de gestão de agenda de aulas"),
                @Tag(name = "Perfil de Acesso (ADM)", description = "Operações de gestão de perfis de acesso do módulo administrativo"),
                @Tag(name = "Autorização de Acesso", description = "Operações de gestão de autorização de acessos"),
                @Tag(name = "BI Administrativo", description = "Operações para visualização de Business Inteligence do módulo Administrativo"),
                @Tag(name = "Cartão de Vacina", description = "Operações de gestão de cartão de vacina"),
                @Tag(name = "Colaborador", description = "Operações de gestão de colaboradores"),
                @Tag(name = "Clientes", description = "Operações de gestão de clientes"),
                @Tag(name = "Clientes com Atestado", description = "Operações de gestão de clientes com atestado médico"),
                @Tag(name = "Clube de Vantagens", description = "Operações de gestão do clube de vantagens"),
                @Tag(name = "Consulta de Recibos", description = "Operações de gestão de consulta de recibos de pagamentos"),
                @Tag(name = "Freepass", description = "Operações de gestão do freepass"),
                @Tag(name = "Gestão de Armários", description = "Operações de gestão de armários, como aluguel e cadastro de armários"),
                @Tag(name = "Gestão de Negativações", description = "Operações de gestão de negativações de clientes"),
                @Tag(name = "Categoria", description = "Operações de gestão de categorias"),
                @Tag(name = "Classificação", description = "Operações de gestão envolvendo classificações"),
                @Tag(name = "Gestão de Notas", description = "Operações de gestão de gestão de notas fiscais"),
                @Tag(name = "Gestão de Vendas Online", description = "Operações de gestão de vendas online, como consulta de parcelas e pagamentos"),
                @Tag(name = "Grupo com Desconto", description = "Operações de gestão de grupos com desconto"),
                @Tag(name = "Indicações", description = "Operações de gestão de indicações de clientes"),
                @Tag(name = "Plano", description = "Operações de gestão de planos e contratos da academia"),
                @Tag(name = "Registrar Acesso Manual", description = "Operações para registro de acesso à academia de forma manual"),
                @Tag(name = "Saldo Conta Corrente", description = "Operações de gestão de saldos da conta corrente dos alunos da academia"),
                @Tag(name = "Lista de Acessos", description = "Operações de gestão de consulta de acessos de clientes à academia"),
                @Tag(name = "Tipo de Modalidade", description = "Operações de gestão de tipos de modalidade"),
                @Tag(name = "Turmas", description = "Operações de gestão de turmas"),
                @Tag(name = "Lista de Clientes Simplificada", description = "Operações de gestão de consulta de dados dos clientes de forma simplificada"),
                @Tag(name = "Local de Acesso", description = "Operações de gestão de locais de acesso"),
                @Tag(name = "Nível de Turma", description = "Operações de gestão de níveis de turma"),
                @Tag(name = "Pessoa", description = "Operações de gestão de pessoas cadastradas no sistema"),
                @Tag(name = "Cliente Mensagem", description = "Operações de gestão de mensagens para clientes"),
                @Tag(name = "Configurações ADM", description = "Operações de gestão de configurações do módulo administrador"),
                @Tag(name = "Configurações SESI", description = "Operações de gestão de configurações do módulo administrador para usuários SESI"),
                @Tag(name = "Produto", description = "Operações de gestão de produtos"),
                @Tag(name = "Questionário Cliente", description = "Operações de gestão de questionários de clientes (BV)"),
                @Tag(name = "Logs", description = "Operações de gestão de logs"),
                @Tag(name = "Empresa (ADM)", description = "Operações de gestão de empresas do módulo administrativo"),
                @Tag(name = "Evento", description = "Operações de gestão de eventos"),
                @Tag(name = "Fornecedor", description = "Operações de gestão de fornecedores"),
                @Tag(name = "Health", description = "Operações de gestão de saúde da API"),
                @Tag(name = "Integrações", description = "Operações de gestão de integrações"),
                @Tag(name = "Modalidade", description = "Operações de gestão de modalidades"),
                @Tag(name = "Negociação", description = "Operações de gestão de negociação"),
                @Tag(name = "Sorteio", description = "Operações de gestão de sorteios"),
                @Tag(name = "Usuário (ADM)", description = "Operações de gestão de usuários do módulo administrativo"),
                @Tag(name = "Venda Avulsa", description = "Operações de gestão de vendas avulsas")
        }
)
@io.swagger.v3.oas.annotations.security.SecurityScheme(
        name = "bearerAuth",
        scheme = "bearer",
        type = SecuritySchemeType.HTTP,
        bearerFormat = "JWT"
)
@Configuration
public class OpenApiConfiguration implements OpenApiCustomiser {


    @Override
    public void customise(OpenAPI openAPI) {

        Components components = openAPI.getComponents();
        if (components == null) {
            components = new Components();
            openAPI.setComponents(components);
        }

        components.addSchemas("BadRequest", ModelConverters.getInstance().read(BadRequest.class).get("BadRequest"));
        components.addSchemas("Unauthorized", ModelConverters.getInstance().read(Unauthorized.class).get("Unauthorized"));
        components.addSchemas("Forbidden", ModelConverters.getInstance().read(Forbidden.class).get("Forbidden"));
        components.addSchemas("NotFound", ModelConverters.getInstance().read(NotFound.class).get("NotFound"));
        components.addSchemas("InternalServerError", ModelConverters.getInstance().read(InternalServerError.class).get("InternalServerError"));
        components.addSchemas("Conflict", ModelConverters.getInstance().read(Conflict.class).get("Conflict"));
        components.addSchemas("ErrorFront", ModelConverters.getInstance().read(ErrorFront.class).get("ErrorFront"));
        components.addSchemas("InternalServerErrorMessage", ModelConverters.getInstance().read(InternalServerErrorMessage.class).get("InternalServerErrorMessage"));


        openAPI.getPaths().forEach((pathKey, pathItem) -> {
            pathItem.readOperationsMap().forEach((httpMethod, operation) -> {

                ApiResponses existingResponses = operation.getResponses();
                if (existingResponses == null) {
                    existingResponses = new ApiResponses();
                }

                ApiResponses responsesCopy = existingResponses;

                Map<String, ApiResponse> responsesToAdd = new LinkedHashMap<>();
                responsesToAdd.put("400", Response400());
                responsesToAdd.put("401", Response401());
                responsesToAdd.put("403", Response403());
                responsesToAdd.put("404", Response404());
                responsesToAdd.put("409", Response409());
                responsesToAdd.put("422", Response422());
                responsesToAdd.put("500", Response500());


                responsesToAdd.forEach((code, response) -> {
                    if (!responsesCopy.containsKey(code)) {
                        responsesCopy.addApiResponse(code, response);
                    }
                });

                operation.setResponses(responsesCopy);
            });
        });
    }

    @Bean
    public OpenApiCustomiser filterUndocumentedEndpoints() {
        return openApi -> {
            Paths paths = openApi.getPaths();
            if (paths == null) {
                return;
            }

            paths.entrySet().removeIf(entry -> {
                PathItem pathItem = entry.getValue();
                if (pathItem == null) {
                    return true;
                }

                boolean hasDocumentedOperation = false;
                for (PathItem.HttpMethod httpMethod : PathItem.HttpMethod.values()) {
                    Operation operation = pathItem.readOperationsMap().get(httpMethod);
                    if (operation != null && isOperationDocumented(operation)) {
                        hasDocumentedOperation = true;
                        break;
                    }
                }

                return !hasDocumentedOperation;
            });
        };
    }


    private ApiResponse Response400() {
        return new ApiResponse()
                .description("Requisição inválida")
                .content(new Content().addMediaType("application/json", new MediaType()
                        .schema(new Schema<>().$ref("#/components/schemas/BadRequest"))
                        .addExamples("Erro 400", new Example()
                                .summary("Exemplo de erro 400")
                                .value("{ \"timestamp\": \"1742393075634\", \"status\": 400, \"error\": \"Bad Request\", \"path\": \"/caminho-da-requisicao/\" }"))));
    }

    private ApiResponse Response401() {
        return new ApiResponse()
                .description("Não autenticado")
                .content(new Content().addMediaType("application/json", new MediaType()
                        .schema(new Schema<>().$ref("#/components/schemas/Unauthorized"))
                        .addExamples("Erro 401", new Example()
                                .summary("Exemplo de erro 401")
                                .value("{ \"timestamp\": \"1742393075634\", \"status\": 401, \"error\": \"Unauthorized\", \"path\": \"/caminho-da-requisicao/\" }"))));
    }


    private ApiResponse Response403() {
        return new ApiResponse()
                .description("Não autorizado")
                .content(new Content().addMediaType("application/json", new MediaType()
                        .schema(new Schema<>().$ref("#/components/schemas/Forbidden"))
                        .addExamples("Erro 403", new Example()
                                .summary("Exemplo de erro 403")
                                .value("{ \"timestamp\": \"1742393075634\", \"status\": 403, \"error\": \"Forbidden\", \"path\": \"/caminho-da-requisicao/\" }"))));
    }

    private ApiResponse Response404() {
        return new ApiResponse()
                .description("Não encontrado")
                .content(new Content().addMediaType("application/json", new MediaType()
                        .schema(new Schema<>().$ref("#/components/schemas/NotFound"))
                        .addExamples("Erro 404", new Example()
                                .summary("Exemplo de erro 404")
                                .value("{ \"timestamp\": \"1742393075634\", \"status\": 404, \"error\": \"Not Found\", \"path\": \"/caminho-da-requisicao/\" }"))));
    }

    private ApiResponse Response500() {
        return new ApiResponse()
                .description("Erro interno")
                .content(new Content().addMediaType("application/json", new MediaType()
                        .schema(new Schema<>().$ref("#/components/schemas/InternalServerError"))
                        .addExamples("Erro 500", new Example()
                                .summary("Exemplo de erro 500")
                                .value("{ \"timestamp\": \"1742393075634\", \"status\": 500, \"error\": \"Internal Server Error\", \"path\": \"/caminho-da-requisicao/\" }"))
                        .addExamples("Erro 500 Mensagem", new Example()
                                .summary("Exemplo de erro 500 com mensagem de erro")
                                .value("{ \"error\": \"internal_server_error\", \"message\": \"Ocorreu um erro inesperado no servidor.\" }"))
                ));

    }

    private ApiResponse Response422() {
        return new ApiResponse()
                .description("Erro previsto no frontend (regra de negócio)")
                .content(new Content().addMediaType("application/json", new MediaType()
                        .schema(new Schema<>().$ref("#/components/schemas/ErrorFront"))
                        .addExamples("Erro 422", new Example()
                                .summary("Exemplo de erro 422")
                                .value("{ \"error\": \"erro_previsto\", \"message\": \"Mensagem amigável exibida ao usuário no frontend.\" }"))));
    }

    private ApiResponse Response409() {
        return new ApiResponse()
                .description("Conflito - Recurso em conflito")
                .content(new Content().addMediaType("application/json", new MediaType()
                        .schema(new Schema<>().$ref("#/components/schemas/Conflict"))
                        .addExamples("Erro 409", new Example()
                                .summary("Exemplo de erro 409")
                                .value("{ \"error\": \"conflito_recurso\", \"message\": \"O recurso já existe ou o estado solicitado entra em conflito com outro existente.\" }"))));
    }


    private boolean isOperationDocumented(Operation operation) {
        return operation.getSummary() != null || operation.getDescription() != null;
    }
}


