package com.pacto.adm.core.adapters.recibodevolucao;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.ContratoAdapter;
import com.pacto.adm.core.adapters.PessoaAdapter;
import com.pacto.adm.core.adapters.ReciboPagamentoAdapter;
import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.dto.recibodevolucao.ReciboDevolucaoDTO;
import com.pacto.adm.core.entities.recibodevolucao.ReciboDevolucao;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class ReciboDevolucaoAdapter implements AdapterInterface<ReciboDevolucao, ReciboDevolucaoDTO> {

    private final ContratoAdapter contratoAdapter;
    private final UsuarioAdapter usuarioAdapter;
    private final PessoaAdapter pessoaAdapter;
    private final ReciboPagamentoAdapter reciboPagamentoAdapter;

    public ReciboDevolucaoAdapter(
            ContratoAdapter contratoAdapter, UsuarioAdapter usuarioAdapter, PessoaAdapter pessoaAdapter,
            ReciboPagamentoAdapter reciboPagamentoAdapter
    ) {
        this.contratoAdapter = contratoAdapter;
        this.usuarioAdapter = usuarioAdapter;
        this.pessoaAdapter = pessoaAdapter;
        this.reciboPagamentoAdapter = reciboPagamentoAdapter;
    }

    @Override
    public ReciboDevolucaoDTO toDto(ReciboDevolucao reciboDevolucao) {
        ReciboDevolucaoDTO reciboDevolucaoDTO = new ReciboDevolucaoDTO();
        reciboDevolucaoDTO.setCodigo(reciboDevolucao.getCodigo());
        reciboDevolucaoDTO.setDataDevolucao(reciboDevolucao.getDataDevolucao().getTime());
        reciboDevolucaoDTO.setDevolucaoManual(reciboDevolucao.getDevolucaoManual());
        reciboDevolucaoDTO.setLiberacao(reciboDevolucao.getLiberacao());
        reciboDevolucaoDTO.setLiberacaoDevolucao(reciboDevolucao.getLiberacaoDevolucao());
        reciboDevolucaoDTO.setQuitacao(reciboDevolucao.getQuitacao());
        reciboDevolucaoDTO.setQuitacaoManual(reciboDevolucao.getQuitacaoManual());
        reciboDevolucaoDTO.setValorBaseContrato(reciboDevolucao.getValorBaseContrato());
        reciboDevolucaoDTO.setValorContaCorrente(reciboDevolucao.getValorContaCorrente());
        reciboDevolucaoDTO.setValorContrato(reciboDevolucao.getValorContrato());
        reciboDevolucaoDTO.setValorDevolucao(reciboDevolucao.getValorDevolucao());
        reciboDevolucaoDTO.setValorDevolvidoEmDinheiro(reciboDevolucao.getValorDevolvidoEmDinheiro());
        reciboDevolucaoDTO.setValorMultaCancelamento(reciboDevolucao.getValorMultaCancelamento());
        reciboDevolucaoDTO.setValorOriginal(reciboDevolucao.getValorOriginal());
        reciboDevolucaoDTO.setValorRealDevolucao(reciboDevolucao.getValorRealDevolucao());
        reciboDevolucaoDTO.setValorTaxaCancelamento(reciboDevolucao.getValorTaxaCancelamento());
        reciboDevolucaoDTO.setValorTotalPagoPeloCliente(reciboDevolucao.getValorTotalPagoPeloCliente());
        reciboDevolucaoDTO.setValorTotalSomaProdutoContratos(reciboDevolucao.getValorTotalSomaProdutoContratos());
        reciboDevolucaoDTO.setValorUtilizadoPeloCliente(reciboDevolucao.getValorUtilizadoPeloCliente());
        if (reciboDevolucao.getContrato() != null) {
            reciboDevolucaoDTO.setContrato(contratoAdapter.toDto(reciboDevolucao.getContrato()));
        }
//        reciboDevolucaoDTO.setMovProduto(reciboDevolucao.getMovProduto());
        if (reciboDevolucao.getPessoa() != null) {
            reciboDevolucaoDTO.setPessoa(pessoaAdapter.toDto(reciboDevolucao.getPessoa()));
        }
//        reciboDevolucaoDTO.setProdDevolucao(reciboDevolucao.getProdDevolucao());
//        reciboDevolucaoDTO.setProdRecebiveis(reciboDevolucao.getProdRecebiveis());
        if (reciboDevolucao.getPessoa() != null) {
            reciboDevolucaoDTO.setReciboEditado(reciboPagamentoAdapter.toDto(reciboDevolucao.getReciboEditado()));
        }
        if (reciboDevolucao.getPessoa() != null) {
            reciboDevolucaoDTO.setUsuario(usuarioAdapter.toDto(reciboDevolucao.getUsuario()));
        }

        return reciboDevolucaoDTO;
    }

    @Override
    public ReciboDevolucao toEntity(ReciboDevolucaoDTO reciboDevolucaoDTO) {
        ReciboDevolucao reciboDevolucao = new ReciboDevolucao();
        reciboDevolucao.setCodigo(reciboDevolucaoDTO.getCodigo());
        reciboDevolucao.setDataDevolucao(new Date(reciboDevolucaoDTO.getDataDevolucao()));
        reciboDevolucao.setDevolucaoManual(reciboDevolucaoDTO.getDevolucaoManual());
        reciboDevolucao.setLiberacao(reciboDevolucaoDTO.getLiberacao());
        reciboDevolucao.setLiberacaoDevolucao(reciboDevolucaoDTO.getLiberacaoDevolucao());
        reciboDevolucao.setQuitacao(reciboDevolucaoDTO.getQuitacao());
        reciboDevolucao.setQuitacaoManual(reciboDevolucaoDTO.getQuitacaoManual());
        reciboDevolucao.setValorBaseContrato(reciboDevolucaoDTO.getValorBaseContrato());
        reciboDevolucao.setValorContaCorrente(reciboDevolucaoDTO.getValorContaCorrente());
        reciboDevolucao.setValorContrato(reciboDevolucaoDTO.getValorContrato());
        reciboDevolucao.setValorDevolucao(reciboDevolucaoDTO.getValorDevolucao());
        reciboDevolucao.setValorDevolvidoEmDinheiro(reciboDevolucaoDTO.getValorDevolvidoEmDinheiro());
        reciboDevolucao.setValorMultaCancelamento(reciboDevolucaoDTO.getValorMultaCancelamento());
        reciboDevolucao.setValorOriginal(reciboDevolucaoDTO.getValorOriginal());
        reciboDevolucao.setValorRealDevolucao(reciboDevolucaoDTO.getValorRealDevolucao());
        reciboDevolucao.setValorTaxaCancelamento(reciboDevolucaoDTO.getValorTaxaCancelamento());
        reciboDevolucao.setValorTotalPagoPeloCliente(reciboDevolucaoDTO.getValorTotalPagoPeloCliente());
        reciboDevolucao.setValorTotalSomaProdutoContratos(reciboDevolucaoDTO.getValorTotalSomaProdutoContratos());
        reciboDevolucao.setValorUtilizadoPeloCliente(reciboDevolucaoDTO.getValorUtilizadoPeloCliente());
        if (reciboDevolucaoDTO.getContrato() != null) {
            reciboDevolucao.setContrato(contratoAdapter.toEntity(reciboDevolucaoDTO.getContrato()));
        }
//        reciboDevolucao.setMovProduto(reciboDevolucaoDTO.getMovProduto());
        if (reciboDevolucaoDTO.getPessoa() != null) {
            reciboDevolucao.setPessoa(pessoaAdapter.toEntity(reciboDevolucaoDTO.getPessoa()));
        }
//        reciboDevolucao.setProdDevolucao(reciboDevolucaoDTO.getProdDevolucao());
//        reciboDevolucao.setProdRecebiveis(reciboDevolucaoDTO.getProdRecebiveis());
        if (reciboDevolucaoDTO.getPessoa() != null) {
            reciboDevolucao.setReciboEditado(reciboPagamentoAdapter.toEntity(reciboDevolucaoDTO.getReciboEditado()));
        }
        if (reciboDevolucaoDTO.getPessoa() != null) {
            reciboDevolucao.setUsuario(usuarioAdapter.toEntity(reciboDevolucaoDTO.getUsuario()));
        }

        return reciboDevolucao;
    }
}
