package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoFogueteDTO;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoFoguete;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoFogueteAdapter implements AdapterInterface<ConfiguracaoIntegracaoFoguete, ConfiguracaoIntegracaoFogueteDTO> {

    @Override
    public ConfiguracaoIntegracaoFoguete toEntity(ConfiguracaoIntegracaoFogueteDTO configDTO) {
        if (configDTO != null) {
            ConfiguracaoIntegracaoFoguete configEntity = new ConfiguracaoIntegracaoFoguete();
            configEntity.setCodigo(configDTO.getCodigo());
            configEntity.setHabilitada(configDTO.getHabilitada());
            configEntity.setTokenApi(configDTO.getTokenApi());
            configEntity.setProduto(configDTO.getProduto());
            configEntity.setEmpresa(new Empresa());
            configEntity.getEmpresa().setCodigo(configDTO.getEmpresa().getCodigo());
            configEntity.setUrlApi(configDTO.getUrlApi());
            return configEntity;
        }
        return null;
    }

    @Override
    public ConfiguracaoIntegracaoFogueteDTO toDto(ConfiguracaoIntegracaoFoguete configEntity) {
        if (configEntity != null && !UteisValidacao.emptyNumber(configEntity.getCodigo())) {
            ConfiguracaoIntegracaoFogueteDTO configDto = new ConfiguracaoIntegracaoFogueteDTO();
            configDto.setCodigo(configEntity.getCodigo());
            configDto.setHabilitada(configEntity.getHabilitada());
            configDto.setTokenApi(configEntity.getTokenApi());
            configDto.setProduto(configEntity.getProduto());
            configDto.setEmpresa(new EmpresaDTO());
            configDto.getEmpresa().setCodigo(configEntity.getEmpresa().getCodigo());
            configDto.getEmpresa().setNome(configEntity.getEmpresa().getNome());
            configDto.setUrlApi(configEntity.getUrlApi());
            return configDto;
        }
        return null;
    }
}
