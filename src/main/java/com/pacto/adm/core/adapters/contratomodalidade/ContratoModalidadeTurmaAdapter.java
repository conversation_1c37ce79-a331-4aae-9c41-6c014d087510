package com.pacto.adm.core.adapters.contratomodalidade;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.turma.TurmaAdapter;
import com.pacto.adm.core.dto.contratomodalidade.ContratoModalidadeTurmaDTO;
import com.pacto.adm.core.entities.contrato.ContratoModalidadeTurma;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ContratoModalidadeTurmaAdapter implements AdapterInterface<ContratoModalidadeTurma, ContratoModalidadeTurmaDTO> {

    private final ContratoModalidadeHorarioTurmaAdapter contratoModalidadeHorarioTurmaAdapter;
    private final TurmaAdapter turmaAdapter;

    public ContratoModalidadeTurmaAdapter(ContratoModalidadeHorarioTurmaAdapter contratoModalidadeHorarioTurmaAdapter, TurmaAdapter turmaAdapter) {
        this.contratoModalidadeHorarioTurmaAdapter = contratoModalidadeHorarioTurmaAdapter;
        this.turmaAdapter = turmaAdapter;
    }

    @Override
    public ContratoModalidadeTurma toEntity(ContratoModalidadeTurmaDTO contratoModalidadeTurmaDTO) {
        ContratoModalidadeTurma contratoModalidadeTurma = new ContratoModalidadeTurma();
        contratoModalidadeTurma = toEntity(contratoModalidadeTurmaDTO, contratoModalidadeTurma);
        return contratoModalidadeTurma;
    }

    @Override
    public ContratoModalidadeTurma toEntity(ContratoModalidadeTurmaDTO contratoModalidadeTurmaDTO, ContratoModalidadeTurma contratoModalidadeTurma) {
        contratoModalidadeTurma.setCodigo(contratoModalidadeTurmaDTO.getCodigo());

        return contratoModalidadeTurma;
    }

    @Override
    public ContratoModalidadeTurmaDTO toDto(ContratoModalidadeTurma contratoModalidadeTurma) {
        ContratoModalidadeTurmaDTO contratoModalidadeTurmaDTO = new ContratoModalidadeTurmaDTO();

        contratoModalidadeTurmaDTO.setCodigo(contratoModalidadeTurma.getCodigo());
        contratoModalidadeTurmaDTO.setTurma(turmaAdapter.toDto(contratoModalidadeTurma.getTurma()));

        if(!contratoModalidadeTurma.getTurmasHorarios().isEmpty()) {
            contratoModalidadeTurmaDTO.setHorarios(contratoModalidadeHorarioTurmaAdapter.toDtos(contratoModalidadeTurma.getTurmasHorarios()));
        }

        return contratoModalidadeTurmaDTO;
    }
}
