package com.pacto.adm.core.adapters;

import com.pacto.adm.core.adapters.empresa.EmpresaLocalDeAcessoAdapter;
import com.pacto.adm.core.dto.ColetorDTO;
import com.pacto.adm.core.dto.LocalDeAcessoDTO;
import com.pacto.adm.core.entities.Coletor;
import com.pacto.adm.core.entities.LocalAcesso;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ColetorAdapter implements AdapterInterface<Coletor, ColetorDTO> {

    @Override
    public Coletor toEntity(ColetorDTO coletorDTO) {
        if (coletorDTO != null) {
            Coletor coletor = new Coletor();
            coletor.setCodigo(coletorDTO.getCodigo());
            coletor.setDescricao(coletorDTO.getDescricao());
            return coletor;
        }
        return null;
    }

    @Override
    public ColetorDTO toDto(Coletor coletor) {
        if (coletor != null) {
            ColetorDTO coletorDTO = new ColetorDTO();
            coletorDTO.setCodigo(coletor.getCodigo());
            coletorDTO.setDescricao(coletor.getDescricao());
            return coletorDTO;
        }
        return null;
    }
}
