package com.pacto.adm.core.adapters;

import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.dto.MovPagamentoDTO;
import com.pacto.adm.core.entities.MovPagamento;
import com.pacto.adm.core.entities.financeiro.FormaPagamento;
import org.springframework.stereotype.Component;

@Component
public class MovPagamentoAdapter implements AdapterInterface<MovPagamento, MovPagamentoDTO> {

    private final EmpresaAdapter empresaAdapter;
    private final FormaPagamentoAdapter formaPagamentoAdapter;
    private final UsuarioAdapter usuarioAdapter;

    public MovPagamentoAdapter(EmpresaAdapter empresaAdapter, FormaPagamentoAdapter formaPagamentoAdapter, UsuarioAdapter usuarioAdapter) {
        this.empresaAdapter = empresaAdapter;
        this.formaPagamentoAdapter = formaPagamentoAdapter;
        this.usuarioAdapter = usuarioAdapter;
    }

    @Override
    public MovPagamento toEntity(MovPagamentoDTO movPagamentoDTO) {
        MovPagamento movPagamento = new MovPagamento();
        movPagamento = toEntity(movPagamentoDTO, movPagamento);
        return movPagamento;
    }

    @Override
    public MovPagamento toEntity(MovPagamentoDTO movPagamentoDTO, MovPagamento movPagamento) {
        movPagamento.setReciboPagamento(movPagamentoDTO.getReciboPagamento());
        movPagamento.setCodigo(movPagamentoDTO.getCodigo());
        movPagamento.setNomePagador(movPagamentoDTO.getNomePessoaPagador());
        movPagamento.setDataLancamento(movPagamentoDTO.getDataLancamento());
        FormaPagamento formaPagamento = new FormaPagamento();
        formaPagamento.setCodigo(movPagamentoDTO.getFormaPagamento().getCodigo());
        movPagamento.setFormaPagamento(formaPagamento);
        movPagamento.setValorTotal(movPagamentoDTO.getValorTotal());

        if (movPagamentoDTO.getEmpresa() != null) {
            movPagamento.setEmpresa(empresaAdapter.toEntity(movPagamentoDTO.getEmpresa()));
        }
        return movPagamento;
    }

    @Override
    public MovPagamentoDTO toDto(MovPagamento movPagamento) {
        MovPagamentoDTO movPagamentoDTO = new MovPagamentoDTO();
        movPagamentoDTO.setReciboPagamento(movPagamento.getReciboPagamento());
        movPagamentoDTO.setCodigo(movPagamento.getCodigo());
        movPagamentoDTO.setNomePessoaPagador(movPagamento.getNomePagador());
        movPagamentoDTO.setFormaPagamento(formaPagamentoAdapter.toDto(movPagamento.getFormaPagamento()));
        movPagamentoDTO.setDescricaoFormaPagamento(movPagamento.getFormaPagamento().getDescricao());
        movPagamentoDTO.setValorTotal(movPagamento.getValorTotal());
        movPagamentoDTO.setUsuario(usuarioAdapter.toDto(movPagamento.getResponsavelPagamento()));
        movPagamentoDTO.setDataLancamento(movPagamento.getDataLancamento());
        movPagamentoDTO.setStatusConciliadora(movPagamento.getStatusConciliadora());
        if (movPagamento.getEmpresa() != null) {
            movPagamentoDTO.setEmpresa(empresaAdapter.toDto(movPagamento.getEmpresa()));
        }
        return movPagamentoDTO;
    }
}
