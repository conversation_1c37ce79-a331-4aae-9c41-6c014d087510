package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoSmsDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.util.Util;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoSmsAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoSmsDTO> {

    @Override
    public ConfiguracaoIntegracaoSmsDTO toDto(Empresa empresa) {
        if (empresa != null) {
            ConfiguracaoIntegracaoSmsDTO configSmsDTO = new ConfiguracaoIntegracaoSmsDTO();
            configSmsDTO.setTokenSMS(Util.mascararDado(empresa.getTokenSMS(), 8));
            configSmsDTO.setTokenSMSShortCode(Util.mascararDado(empresa.getTokenSMSShortCode(), 8));
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            configSmsDTO.setEmpresa(empresaDTO);
            return configSmsDTO;
        }
        return null;
    }
}
