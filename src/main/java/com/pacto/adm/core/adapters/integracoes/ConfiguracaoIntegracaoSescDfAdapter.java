package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoSescDfDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoSescDfAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoSescDfDTO> {

    @Override
    public ConfiguracaoIntegracaoSescDfDTO toDto(Empresa empresa) {
        if (empresa != null) {
            final ConfiguracaoIntegracaoSescDfDTO configSesc = new ConfiguracaoIntegracaoSescDfDTO();
            configSesc.setUsarSescDf(empresa.getUsarSescDf());
            configSesc.setToken(empresa.getTokenSescDf());

            final EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            configSesc.setEmpresa(empresaDTO);

            return configSesc;
        }
        return null;
    }
}
