package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGymbotProDTO;
import com.pacto.adm.core.entities.ConfiguracaoIntegracaoGymbotPro;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoGymbotProAdapter implements AdapterInterface<ConfiguracaoIntegracaoGymbotPro, ConfiguracaoIntegracaoGymbotProDTO> {

    @Override
    public ConfiguracaoIntegracaoGymbotProDTO toDto(ConfiguracaoIntegracaoGymbotPro configEntity) {
            ConfiguracaoIntegracaoGymbotProDTO configuracaoIntegracaoGymbotProDTO = new ConfiguracaoIntegracaoGymbotProDTO();
            configuracaoIntegracaoGymbotProDTO.setCodigo(configEntity.getCodigo());
            configuracaoIntegracaoGymbotProDTO.setAtivo(configEntity.getAtivo());
            configuracaoIntegracaoGymbotProDTO.setTipoFluxo(configEntity.getTipoFluxo());
            configuracaoIntegracaoGymbotProDTO.setDescricao(configEntity.getDescricao());
            configuracaoIntegracaoGymbotProDTO.setIdFluxo(configEntity.getIdFluxo());
            configuracaoIntegracaoGymbotProDTO.setToken(configEntity.getToken());
            configuracaoIntegracaoGymbotProDTO.setEmpresa(configEntity.getEmpresa());
            configuracaoIntegracaoGymbotProDTO.setFase(configEntity.getFase());
            return configuracaoIntegracaoGymbotProDTO;
    }

    public ConfiguracaoIntegracaoGymbotPro toEntity(ConfiguracaoIntegracaoGymbotProDTO configDto) {
        if (configDto != null ) {
            try {
                ConfiguracaoIntegracaoGymbotPro  configEntity = new ConfiguracaoIntegracaoGymbotPro();
                configEntity.setCodigo(configDto.getCodigo());
                configEntity.setAtivo(configDto.getAtivo());
                configEntity.setDescricao(configDto.getDescricao());
                configEntity.setIdFluxo(configDto.getIdFluxo());
                configEntity.setToken(configDto.getToken());
                configEntity.setEmpresa(configDto.getEmpresa());
                configEntity.setFase(configDto.getFase());
                configEntity.setTipoFluxo(configDto.getTipoFluxo());
                return configEntity;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
