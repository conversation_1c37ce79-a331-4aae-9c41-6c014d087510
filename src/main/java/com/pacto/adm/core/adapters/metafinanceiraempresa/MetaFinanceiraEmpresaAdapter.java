package com.pacto.adm.core.adapters.metafinanceiraempresa;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.ambiente.AmbienteDTO;
import com.pacto.adm.core.dto.metafinanceiraempresa.MetaFinanceiraEmpresaDTO;
import com.pacto.adm.core.entities.Ambiente;
import com.pacto.adm.core.entities.metafinanceira.MetaFinanceiraEmpresa;
import org.springframework.stereotype.Component;

@Component
public class MetaFinanceiraEmpresaAdapter implements AdapterInterface<MetaFinanceiraEmpresa, MetaFinanceiraEmpresaDTO> {

    @Override
    public MetaFinanceiraEmpresa toEntity(MetaFinanceiraEmpresaDTO metaFinanceiraEmpresaDTO) {

        if (metaFinanceiraEmpresaDTO == null) {
            return null;
        }

        return new MetaFinanceiraEmpresa(
                metaFinanceiraEmpresaDTO.getCodigo(),
                metaFinanceiraEmpresaDTO.getDescricao(),
                metaFinanceiraEmpresaDTO.getMes(),
                metaFinanceiraEmpresaDTO.getAno()
        );
    }

    @Override
    public MetaFinanceiraEmpresaDTO toDto(MetaFinanceiraEmpresa metaFinanceiraEmpresa) {

        if (metaFinanceiraEmpresa == null) {
            return null;
        }

        return new MetaFinanceiraEmpresaDTO(
                metaFinanceiraEmpresa.getCodigo(),
                metaFinanceiraEmpresa.getDescricao(),
                metaFinanceiraEmpresa.getMes(),
                metaFinanceiraEmpresa.getAno()
        );
    }
}
