package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dao.interfaces.UsuarioDao;
import com.pacto.adm.core.dto.LogTotalPassDTO;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.adm.core.entities.contrato.LogTotalPass;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class LogTotalPassAdapter implements AdapterInterface<LogTotalPass, LogTotalPassDTO> {
    @Autowired
    UsuarioAdapter usuarioAdapter;

    @Autowired
    UsuarioDao usuarioDao;

    @Override
    public LogTotalPassDTO toDto(LogTotalPass logTotalPass) {
        if (logTotalPass != null) {
            LogTotalPassDTO logTotalPassDTO = new LogTotalPassDTO();
            logTotalPassDTO.setCodigo(logTotalPass.getCodigo());
            logTotalPassDTO.setPessoa(logTotalPass.getPessoa());
            logTotalPassDTO.setApikey(logTotalPass.getApikey());
            logTotalPassDTO.setDataregistro(logTotalPass.getDataregistro());
            logTotalPassDTO.setEmpresa(logTotalPass.getEmpresa());
            logTotalPassDTO.setIp(logTotalPass.getIp());
            logTotalPassDTO.setJson(logTotalPass.getJson());
            logTotalPassDTO.setOrigem(logTotalPass.getOrigem());
            logTotalPassDTO.setResposta(logTotalPass.getResposta());
            logTotalPassDTO.setTempoResposta(logTotalPass.getTempoResposta());
            logTotalPassDTO.setUri(logTotalPass.getUri());
            logTotalPassDTO.setUsuario(logTotalPass.getUsuario());

            return logTotalPassDTO;
        }
        return null;
    }


    @Override
    public LogTotalPass toEntity(LogTotalPassDTO dto) {
        if (dto != null) {
            LogTotalPass entity = new LogTotalPass();

            entity.setCodigo(dto.getCodigo());
            entity.setPessoa(dto.getPessoa());
            entity.setApikey(dto.getApikey());
            entity.setPessoa(dto.getPessoa());
            entity.setDataregistro(dto.getDataregistro());
            entity.setEmpresa(dto.getEmpresa());
            entity.setIp(dto.getIp());
            entity.setJson(dto.getJson());
            entity.setOrigem(dto.getOrigem());
            entity.setOrigem(dto.getOrigem());
            entity.setResposta(dto.getResposta());
            entity.setTempoResposta(dto.getTempoResposta());
            entity.setUri(dto.getUri());
            entity.setTipo(dto.getTipo());
            entity.setUsuario(dto.getUsuario());

            return entity;
        }
        return null;
    }
}
