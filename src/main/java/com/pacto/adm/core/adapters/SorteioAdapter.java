package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.SorteioDTO;
import com.pacto.adm.core.entities.sorteio.Sorteio;
import com.pacto.config.utils.Uteis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SorteioAdapter implements AdapterInterface<Sorteio, SorteioDTO> {

    @Autowired
    private ClienteAdapter clienteAdapter;
    @Autowired
    private UsuarioAdapter usuarioAdapter;

    @Override
    public SorteioDTO toDto(Sorteio sorteio) {
        if (sorteio != null) {
            SorteioDTO sorteioDTO = new SorteioDTO();
            sorteioDTO.setCodigo(sorteio.getCodigo());
            sorteioDTO.setDataSorteio(sorteio.getDataSorteio());
            sorteioDTO.setData(Uteis.getData(sorteio.getDataSorteio(), "br"));
            sorteioDTO.setObservacoes(sorteio.getObservacoes());
            sorteioDTO.setCliente(clienteAdapter.toDto(sorteio.getCliente()));
            sorteioDTO.setNome(sorteio.getCliente().getPessoa().getNome());
            sorteioDTO.setUsuario(usuarioAdapter.toDto(sorteio.getUsuario()));
            sorteioDTO.setNomeUsuario(sorteio.getUsuario().getNome());
            return sorteioDTO;
        }
        return null;
    }

    @Override
    public Sorteio toEntity(SorteioDTO sorteioDTO) {
        if (sorteioDTO != null) {
            Sorteio sorteio = new Sorteio();
            sorteio.setCodigo(sorteioDTO.getCodigo());
            sorteio.setDataSorteio(sorteioDTO.getDataSorteio());
            sorteio.setObservacoes(sorteioDTO.getObservacoes());
            sorteio.setCliente(clienteAdapter.toEntity(sorteioDTO.getCliente()));
            sorteio.setUsuario(usuarioAdapter.toEntity(sorteioDTO.getUsuario()));
            return sorteio;
        }
        return null;
    }
}
