package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.AcessoClienteDTO;
import com.pacto.adm.core.entities.AcessoCliente;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;

@Component
public class AcessoClienteAdapter implements AdapterInterface<AcessoCliente, AcessoClienteDTO> {

    @Autowired
    LiberacaoAcessoAdapter liberacaoAcessoAdapter;
    @Autowired
    UsuarioAdapter usuarioAdapter;

    @Override
    public AcessoCliente toEntity(AcessoClienteDTO acessoClienteDTO) {
        if (acessoClienteDTO != null) {
            ClienteLocalDeAcessoAdapter clienteLocalDeAcessoAdapter = new ClienteLocalDeAcessoAdapter();
            LocalDeAcessoAdapter localDeAcessoAdapter = new LocalDeAcessoAdapter();
            ColetorAdapter coletorAdapter = new ColetorAdapter();

            AcessoCliente acessoCliente = new AcessoCliente();
            acessoCliente.setCodigo(acessoClienteDTO.getCodigo());
            acessoCliente.setCliente(clienteLocalDeAcessoAdapter.toEntity(acessoClienteDTO.getCliente()));
            acessoCliente.setSentido(acessoClienteDTO.getSentido());
            acessoCliente.setSituacao(acessoClienteDTO.getSituacao());
            acessoCliente.setLocalAcesso(localDeAcessoAdapter.toEntity(acessoClienteDTO.getLocalAcesso()));
            acessoCliente.setColetor(coletorAdapter.toEntity(acessoClienteDTO.getColetor()));
            if (acessoClienteDTO.getUsuario() != null) {
                acessoCliente.setUsuario(usuarioAdapter.toEntity(acessoClienteDTO.getUsuario()));
            }
            acessoCliente.setDataRegistro(acessoClienteDTO.getDataRegistro());
            acessoCliente.setTicket(acessoClienteDTO.getTicket());

            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");

            try {
                Date date = formatter.parse(acessoClienteDTO.getDtHrEntrada());
                acessoCliente.setDtHrEntrada(date);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }

            if (acessoClienteDTO.getRegistrarSaida()) {
                Date date = null;
                try {
                    date = formatter.parse(acessoClienteDTO.getDtHrSaida());
                    acessoCliente.setDtHrSaida(date);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }

                acessoCliente.setMeioIdentificacaoSaida(acessoClienteDTO.getMeioIdentificacaoSaida());
            }
            acessoCliente.setMeioIdentificacaoEntrada(acessoClienteDTO.getMeioIdentificacaoEntrada());

            if (acessoClienteDTO.getLiberacaoAcesso() != null) {
                acessoCliente.setLiberacaoAcesso(liberacaoAcessoAdapter.toEntity(acessoClienteDTO.getLiberacaoAcesso()));
            }
            return acessoCliente;
        }
        return null;
    }

    @Override
    public AcessoClienteDTO toDto(AcessoCliente acessoCliente) {
        if (acessoCliente != null) {
            ClienteLocalDeAcessoAdapter clienteLocalDeAcessoAdapter = new ClienteLocalDeAcessoAdapter();
            AcessoClienteDTO acessoClienteDTO = new AcessoClienteDTO();
            LocalDeAcessoAdapter localDeAcessoAdapter = new LocalDeAcessoAdapter();
            ColetorAdapter coletorAdapter = new ColetorAdapter();

            acessoClienteDTO.setCodigo(acessoCliente.getCodigo());
            acessoClienteDTO.setCliente(clienteLocalDeAcessoAdapter.toDto(acessoCliente.getCliente()));
            acessoClienteDTO.setSentido(acessoCliente.getSentido());
            acessoClienteDTO.setSituacao(acessoCliente.getSituacao());
            acessoClienteDTO.setTicket(acessoCliente.getTicket());

            if (acessoCliente.getLocalAcesso() != null) {
                acessoClienteDTO.setLocalAcesso(localDeAcessoAdapter.toDto(acessoCliente.getLocalAcesso()));
            }
            if (acessoCliente.getColetor() != null) {
                acessoClienteDTO.setColetor(coletorAdapter.toDto(acessoCliente.getColetor()));
            }

            if (acessoCliente.getUsuario() != null) {
                acessoClienteDTO.setUsuario(usuarioAdapter.toDto(acessoCliente.getUsuario()));
            }

            acessoClienteDTO.setDataRegistro(acessoCliente.getDataRegistro());
            if (acessoCliente.getDtHrEntrada() != null) {
                acessoClienteDTO.setDtHrEntrada(acessoCliente.getDtHrEntrada().toString());
            }
            if (acessoCliente.getDtHrSaida() != null) {
                acessoClienteDTO.setDtHrSaida(acessoCliente.getDtHrSaida().toString());
            }
            acessoClienteDTO.setMeioIdentificacaoEntrada(acessoCliente.getMeioIdentificacaoEntrada());
            acessoClienteDTO.setMeioIdentificacaoSaida(acessoCliente.getMeioIdentificacaoSaida());

            if (acessoCliente.getDtHrEntrada() != null) {
                String[] dtEntrada = acessoCliente.getDtHrEntrada().toString().split(" ");
                acessoClienteDTO.setDataDeAcesso(LocalDate.parse(dtEntrada[0]));
                acessoClienteDTO.setHoraEntradaRegistroAcesso(dtEntrada[1].substring(0, 5));
            }

            acessoClienteDTO.setRegistrarSaida(false);
            if(acessoCliente.getDtHrSaida()!=null){
                String[] saida = acessoCliente.getDtHrSaida().toString().split(" ");
                acessoClienteDTO.setDataSaida(LocalDate.parse(saida[0]));
                acessoClienteDTO.setRegistrarSaida(true);
                acessoClienteDTO.setHoraSaidaRegistroAcesso(saida[1].substring(0, 5));
            }

            if (acessoCliente.getLiberacaoAcesso() != null) {
                acessoClienteDTO.setLiberacaoAcesso(liberacaoAcessoAdapter.toDto(acessoCliente.getLiberacaoAcesso()));
            }
            return acessoClienteDTO;
        }
        return null;
    }
}
