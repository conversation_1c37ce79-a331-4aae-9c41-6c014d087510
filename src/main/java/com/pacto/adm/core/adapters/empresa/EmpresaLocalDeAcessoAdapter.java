package com.pacto.adm.core.adapters.empresa;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.integracoes.EmpresaConfigEstacionamentoAdapter;
import com.pacto.adm.core.adapters.integracoes.ParceiroFidelidadeAdapter;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.empresa.EmpresaLocalDeAcessoDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import com.pacto.adm.core.services.interfaces.EmpresaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class EmpresaLocalDeAcessoAdapter implements AdapterInterface<Empresa, EmpresaLocalDeAcessoDTO> {



    @Override
    public EmpresaLocalDeAcessoDTO toDto(Empresa empresa) {
        if (empresa != null) {
            EmpresaLocalDeAcessoDTO empresaLocalDeAcessoDTO = new EmpresaLocalDeAcessoDTO();
            empresaLocalDeAcessoDTO.setCodigo(empresa.getCodigo());
            empresaLocalDeAcessoDTO.setNome(empresa.getNome());
            empresaLocalDeAcessoDTO.setAtiva(empresa.getAtiva());
            return empresaLocalDeAcessoDTO;
        }
        return null;
    }

    @Override
    public Empresa toEntity(EmpresaLocalDeAcessoDTO empresaLocalDeAcessoDTO) {
        if (empresaLocalDeAcessoDTO != null) {
            Empresa empresa = new Empresa();
            empresa.setCodigo(empresaLocalDeAcessoDTO.getCodigo());
            empresa.setNome(empresaLocalDeAcessoDTO.getNome());
            empresa.setAtiva(empresaLocalDeAcessoDTO.isAtiva());
            return empresa;
        }
        return null;
    }
}
