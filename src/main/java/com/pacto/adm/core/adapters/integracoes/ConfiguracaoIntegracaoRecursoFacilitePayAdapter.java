package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoCDLSPCDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoRecursosFacilitePayDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoRecursoFacilitePayAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoRecursosFacilitePayDTO> {

    @Override
    public ConfiguracaoIntegracaoRecursosFacilitePayDTO toDto(Empresa empresa) {
        if (empresa != null) {
            ConfiguracaoIntegracaoRecursosFacilitePayDTO configDTO = new ConfiguracaoIntegracaoRecursosFacilitePayDTO();
            configDTO.setConcContasPagarFacilitePay(empresa.isConcContasPagarFacilitePay());
            configDTO.setConcContasReceberFacilitePay(empresa.isConcContasReceberFacilitePay());
            configDTO.setFacilitePayReguaCobranca(empresa.isFacilitePayReguaCobranca());
            configDTO.setFacilitePayReguaCobrancaEmail(empresa.isFacilitePayReguaCobrancaEmail());
            configDTO.setFacilitePayReguaCobrancaSms(empresa.isFacilitePayReguaCobrancaSms());
            configDTO.setFacilitePayReguaCobrancaApp(empresa.isFacilitePayReguaCobrancaApp());
            configDTO.setFacilitePayReguaCobrancaWhatsApp(empresa.isFacilitePayReguaCobrancaWhatsApp());
            configDTO.setFacilitePayReguaCobrancaGymbotPro(empresa.isFacilitePayReguaCobrancaGymbotPro());
            configDTO.setQtdLmtContasConcFacilitePay(empresa.getQtdLmtContasConcFacilitePay());
            configDTO.setFacilitePayCDLSPC(empresa.isFacilitePayCDLSPC());
            configDTO.setFacilitePayConciliacaoCartao(empresa.isFacilitePayConciliacaoCartao());
            configDTO.setValorMetaFacilitePay(empresa.getValorMetaFacilitePay());
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            configDTO.setEmpresa(empresaDTO);
            return configDTO;
        }
        return null;
    }

    public Empresa toEntity(ConfiguracaoIntegracaoRecursosFacilitePayDTO configDTO, Empresa empresa) {
        if (configDTO != null && configDTO.getEmpresa() != null) {
            try {
                empresa.setConcContasPagarFacilitePay(configDTO.isConcContasPagarFacilitePay());
                empresa.setConcContasReceberFacilitePay(configDTO.isConcContasReceberFacilitePay());
                empresa.setFacilitePayReguaCobranca(configDTO.isFacilitePayReguaCobranca());
                empresa.setQtdLmtContasConcFacilitePay(configDTO.getQtdLmtContasConcFacilitePay());
                empresa.setFacilitePayCDLSPC(configDTO.isFacilitePayCDLSPC());
                empresa.setFacilitePayConciliacaoCartao(configDTO.isFacilitePayConciliacaoCartao());
                empresa.setValorMetaFacilitePay(configDTO.getValorMetaFacilitePay());
                empresa.setFacilitePayReguaCobrancaEmail(configDTO.isFacilitePayReguaCobrancaEmail());
                empresa.setFacilitePayReguaCobrancaSms(configDTO.isFacilitePayReguaCobrancaSms());
                empresa.setFacilitePayReguaCobrancaApp(configDTO.isFacilitePayReguaCobrancaApp());
                empresa.setFacilitePayReguaCobrancaWhatsApp(configDTO.isFacilitePayReguaCobrancaWhatsApp());
                empresa.setFacilitePayReguaCobrancaGymbotPro(configDTO.isFacilitePayReguaCobrancaGymbotPro());
                return empresa;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
