package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGoGoodDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoGoGoodAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoGoGoodDTO> {

    public ConfiguracaoIntegracaoGoGoodDTO toDto(Empresa empresa) {
        if (empresa != null) {
            ConfiguracaoIntegracaoGoGoodDTO configuracaoIntegracaoGogoodDTO = new ConfiguracaoIntegracaoGoGoodDTO();
            configuracaoIntegracaoGogoodDTO.setTokenAcademyGoGood(empresa.getTokenAcademyGogood());
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            configuracaoIntegracaoGogoodDTO.setEmpresa(empresaDTO);
            return configuracaoIntegracaoGogoodDTO;
        }
        return null;
    }
}
