package com.pacto.adm.core.adapters.nivelturma;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.nivelturma.NivelTurmaDTO;
import com.pacto.adm.core.entities.contrato.NivelTurma;
import org.springframework.stereotype.Component;

@Component
public class NivelTurmaAdapter implements AdapterInterface<NivelTurma, NivelTurmaDTO> {

    @Override
    public NivelTurmaDTO toDto(NivelTurma nivelTurma) {
        return new NivelTurmaDTO(
                nivelTurma.getCodigo(),
                nivelTurma.getDescricao(),
                ""
        );
    }

    @Override
    public NivelTurma toEntity(NivelTurmaDTO nivelTurmaDTO) {
        return new NivelTurma(
                nivelTurmaDTO.getCodigo(),
                nivelTurmaDTO.getDescricao()
        );
    }
}
