package com.pacto.adm.core.adapters.operacaocoletiva;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.adapters.ModalidadeAdapter;
import com.pacto.adm.core.adapters.PlanoAdapter;
import com.pacto.adm.core.adapters.UsuarioAdapter;
import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.adapters.turma.TurmaAdapter;
import com.pacto.adm.core.dto.operacaocoletiva.OperacaoColetivaDTO;
import com.pacto.adm.core.entities.operacaocoletiva.OperacaoColetiva;
import org.springframework.stereotype.Component;

@Component
public class OperacaoColetivaAdapter implements AdapterInterface<OperacaoColetiva, OperacaoColetivaDTO> {

    private final EmpresaAdapter empresaAdapter;
    private final ModalidadeAdapter modalidadeAdapter;
    private final PlanoAdapter planoAdapter;
    private final TurmaAdapter turmaAdapter;
    private final UsuarioAdapter usuarioAdapter;

    public OperacaoColetivaAdapter(
            EmpresaAdapter empresaAdapter, ModalidadeAdapter modalidadeAdapter, PlanoAdapter planoAdapter,
            TurmaAdapter turmaAdapter, UsuarioAdapter usuarioAdapter
    ) {
        this.empresaAdapter = empresaAdapter;
        this.modalidadeAdapter = modalidadeAdapter;
        this.planoAdapter = planoAdapter;
        this.turmaAdapter = turmaAdapter;
        this.usuarioAdapter = usuarioAdapter;
    }


    @Override
    public OperacaoColetivaDTO toDto(OperacaoColetiva operacaoColetiva) {
        OperacaoColetivaDTO operacaoColetivaDTO = new OperacaoColetivaDTO();
        operacaoColetivaDTO.setCodigo(operacaoColetiva.getCodigo());
        operacaoColetivaDTO.setDataCadastro(operacaoColetiva.getDataCadastro());
        operacaoColetivaDTO.setDataFim(operacaoColetiva.getDataFim());
        operacaoColetivaDTO.setDataInicio(operacaoColetiva.getDataInicio());
        operacaoColetivaDTO.setDataProcessamento(operacaoColetiva.getDataProcessamento());
        if (operacaoColetiva.getEmpresa() != null) {
            operacaoColetivaDTO.setEmpresa(empresaAdapter.toDto(operacaoColetiva.getEmpresa()));
        }
        operacaoColetivaDTO.setIdadeMaxima(operacaoColetiva.getIdadeMaxima());
        operacaoColetivaDTO.setIdadeMinima(operacaoColetiva.getIdadeMinima());
        if (operacaoColetiva.getModalidade() != null) {
            operacaoColetivaDTO.setModalidade(modalidadeAdapter.toDto(operacaoColetiva.getModalidade()));
        }
        operacaoColetivaDTO.setObservacao(operacaoColetiva.getObservacao());
        if (operacaoColetiva.getPlano() != null) {
            operacaoColetivaDTO.setPlano(planoAdapter.toDto(operacaoColetiva.getPlano()));
        }
        operacaoColetivaDTO.setResultado(operacaoColetiva.getResultado());
        operacaoColetivaDTO.setStatus(operacaoColetiva.getStatus());
        operacaoColetivaDTO.setTipo(operacaoColetiva.getTipo());
        if (operacaoColetiva.getTurma() != null) {
            operacaoColetivaDTO.setTurma(turmaAdapter.toDto(operacaoColetiva.getTurma()));
        }
        if (operacaoColetiva.getUsuario() != null) {
            operacaoColetivaDTO.setUsuario(usuarioAdapter.toDto(operacaoColetiva.getUsuario()));
        }
        return operacaoColetivaDTO;
    }

    @Override
    public OperacaoColetiva toEntity(OperacaoColetivaDTO operacaoColetivaDTO) {
        OperacaoColetiva operacaoColetiva = new OperacaoColetiva();
        operacaoColetiva.setCodigo(operacaoColetivaDTO.getCodigo());
        operacaoColetiva.setDataCadastro(operacaoColetivaDTO.getDataCadastro());
        operacaoColetiva.setDataFim(operacaoColetivaDTO.getDataFim());
        operacaoColetiva.setDataInicio(operacaoColetivaDTO.getDataInicio());
        operacaoColetiva.setDataProcessamento(operacaoColetivaDTO.getDataProcessamento());
        if (operacaoColetivaDTO.getEmpresa() != null) {
            operacaoColetiva.setEmpresa(empresaAdapter.toEntity(operacaoColetivaDTO.getEmpresa()));
        }
        operacaoColetiva.setIdadeMaxima(operacaoColetivaDTO.getIdadeMaxima());
        operacaoColetiva.setIdadeMinima(operacaoColetivaDTO.getIdadeMinima());
        if (operacaoColetivaDTO.getModalidade() != null) {
            operacaoColetiva.setModalidade(modalidadeAdapter.toEntity(operacaoColetivaDTO.getModalidade()));
        }
        operacaoColetiva.setObservacao(operacaoColetivaDTO.getObservacao());
        if (operacaoColetivaDTO.getPlano() != null) {
            operacaoColetiva.setPlano(planoAdapter.toEntity(operacaoColetivaDTO.getPlano()));
        }
        operacaoColetiva.setResultado(operacaoColetivaDTO.getResultado());
        operacaoColetiva.setStatus(operacaoColetivaDTO.getStatus());
        operacaoColetiva.setTipo(operacaoColetivaDTO.getTipo());
        if (operacaoColetivaDTO.getTurma() != null) {
            operacaoColetiva.setTurma(turmaAdapter.toEntity(operacaoColetivaDTO.getTurma()));
        }
        if (operacaoColetivaDTO.getUsuario() != null) {
            operacaoColetiva.setUsuario(usuarioAdapter.toEntity(operacaoColetivaDTO.getUsuario()));
        }
        return operacaoColetiva;
    }
}
