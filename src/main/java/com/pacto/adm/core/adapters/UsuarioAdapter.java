package com.pacto.adm.core.adapters;

import com.pacto.adm.core.adapters.colaborador.ColaboradorAdapter;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.entities.Usuario;
import org.springframework.stereotype.Component;

@Component
public class UsuarioAdapter implements AdapterInterface<Usuario, UsuarioDTO> {

    private final ColaboradorAdapter colaboradorAdapter;

    public UsuarioAdapter(ColaboradorAdapter colaboradorAdapter) {
        this.colaboradorAdapter = colaboradorAdapter;
    }

    @Override
    public Usuario toEntity(UsuarioDTO usuarioDTO) {
        Usuario usuario = new Usuario();
        usuario = toEntity(usuarioDTO, usuario);
        return usuario;
    }

    @Override
    public Usuario toEntity(UsuarioDTO usuarioDTO, Usuario usuario) {
        usuario.setCodigo(usuarioDTO.getCodigo());
        usuario.setNome(usuarioDTO.getNome());
        return usuario;
    }

    @Override
    public UsuarioDTO toDto(Usuario usuario) {
        UsuarioDTO usuarioDTO = new UsuarioDTO();
        usuarioDTO.setCodigo(usuario.getCodigo());
        usuarioDTO.setNome(usuario.getNome());

        if (usuario.getColaborador() != null) {
            usuarioDTO.setColaborador(
                    colaboradorAdapter.toDto(usuario.getColaborador())
            );
        }
        return usuarioDTO;
    }

}
