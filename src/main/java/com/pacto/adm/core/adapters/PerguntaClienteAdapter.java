package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.PerguntaClienteDTO;
import com.pacto.adm.core.entities.PerguntaCliente;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PerguntaClienteAdapter implements AdapterInterface<PerguntaCliente, PerguntaClienteDTO> {

    @Autowired
    RespostaPergClienteAdapter respostaPergClienteAdapter;

    @Override
    public PerguntaCliente toEntity(PerguntaClienteDTO perguntaClienteDTO) {
        if(perguntaClienteDTO != null) {
            PerguntaCliente perguntaCliente = new PerguntaCliente();
            perguntaCliente.setCodigo(perguntaClienteDTO.getCodigo());
            perguntaCliente.setTipoPergunta(perguntaClienteDTO.getTipoPergunta());
            perguntaCliente.setRespostaPergCliente(respostaPergClienteAdapter.toEntities(perguntaClienteDTO.getRespostaPergCliente()));
            perguntaCliente.setDescricao(perguntaClienteDTO.getDescricao());
            perguntaCliente.setSimples(perguntaClienteDTO.getSimples());
            perguntaCliente.setMultipla(perguntaClienteDTO.getMultipla());
            perguntaCliente.setTextual(perguntaClienteDTO.getTextual());
            perguntaCliente.setObrigatoria(perguntaClienteDTO.getObrigatoria());

            return perguntaCliente;
        }
        return null;
    }

    @Override
    public PerguntaClienteDTO toDto(PerguntaCliente perguntaCliente) {
        if (perguntaCliente != null) {
            PerguntaClienteDTO perguntaClienteDTO = new PerguntaClienteDTO();
            perguntaClienteDTO.setCodigo(perguntaCliente.getCodigo());
            perguntaClienteDTO.setTipoPergunta(perguntaCliente.getTipoPergunta());
            perguntaClienteDTO.setMultipla(perguntaCliente.getMultipla());
            perguntaClienteDTO.setSimples(perguntaCliente.getSimples());
            perguntaClienteDTO.setDescricao(perguntaCliente.getDescricao());
            perguntaClienteDTO.setTextual(perguntaCliente.getTextual());
            perguntaClienteDTO.setRespostaPergCliente((respostaPergClienteAdapter.toDtos(perguntaCliente.getRespostaPergCliente())));
            perguntaClienteDTO.setObrigatoria(perguntaCliente.getObrigatoria());
            perguntaClienteDTO.setPerguntaCodigo(perguntaCliente.getPerguntaCodigo());

            return perguntaClienteDTO;
        }
        return null;
    }
}
