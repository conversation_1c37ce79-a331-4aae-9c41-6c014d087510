package com.pacto.adm.core.adapters;

import com.pacto.adm.core.adapters.contrato.contratoduracao.ContratoDuracaoAdapter;
import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.adm.core.entities.contrato.SituacaoContratoEnum;
import org.springframework.stereotype.Component;

@Component
public class ContratoComDataBaseAlteradaAdapter implements AdapterInterface<Contrato, ContratoDTO> {

    private final ContratoDuracaoAdapter contratoDuracaoAdapter;
    private final ClienteAdapter clienteAdapter;
    private final UsuarioAdapter usuarioAdapter;

    public ContratoComDataBaseAlteradaAdapter(ContratoDuracaoAdapter contratoDuracaoAdapter, ClienteAdapter clienteAdapter, UsuarioAdapter usuarioAdapter) {
        this.contratoDuracaoAdapter = contratoDuracaoAdapter;
        this.clienteAdapter = clienteAdapter;
        this.usuarioAdapter = usuarioAdapter;
    }

    @Override
    public Contrato toEntity(ContratoDTO contratoDTO) {
        Contrato contrato = new Contrato();
        contrato = toEntity(contratoDTO, contrato);
        return contrato;
    }

    @Override
    public Contrato toEntity(ContratoDTO contratoDTO, Contrato contrato) {
        if (contratoDTO == null) {
            return null;
        }
        contrato.setCodigo(contratoDTO.getCodigo());
        contrato.setSituacaoContrato(SituacaoContratoEnum.valueOf(contratoDTO.getTipo()));
        return contrato;
    }

    @Override
    public ContratoDTO toDto(Contrato contrato) {
        ContratoDTO contratoDTO = new ContratoDTO();
        if (contrato != null) {
            contratoDTO.setCodigo(contrato.getCodigo());
            contratoDTO.setVigenciaDe(contrato.getVigenciaDe());
            contratoDTO.setVigenciaAte(contrato.getVigenciaAte());
            contratoDTO.setVigenciaAteAjustada(contrato.getVigenciaAteAjustada());
            contratoDTO.setDataAlteracaoManual(contrato.getDataAlteracaoManual());
            contratoDTO.setDataLancamento(contrato.getDataLancamento());

            if (contrato.getContratoDuracao() != null) {
                contratoDTO.setContratoDuracao(contratoDuracaoAdapter.toDto(contrato.getContratoDuracao()));
            }
            contratoDTO.setPermiteNovoContratoDeOutraEmpresa(contrato.getPermiteNovoContratoDeOutraEmpresa());

            if (contrato.getCliente() != null) {
                contratoDTO.setCliente(
                        clienteAdapter.toDto(contrato.getCliente())
                );
            }

            if (contrato.getResponsavelDataBase() != null) {
                contratoDTO.setResponsavelDataBase(
                        usuarioAdapter.toDto(contrato.getResponsavelDataBase())
                );
            }
            return contratoDTO;
        }
        return null;
    }

}
