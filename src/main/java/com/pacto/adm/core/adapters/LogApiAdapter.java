package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.LogApiDTO;
import com.pacto.adm.core.entities.LogApi;
import org.springframework.stereotype.Component;

@Component
public class LogApiAdapter implements AdapterInterface<LogApi, LogApiDTO> {

    @Override
    public LogApiDTO toDto(LogApi logApi) {
        LogApiDTO logApiDTO = new LogApiDTO();
        logApiDTO.setCodigo(logApi.getCodigo());
        logApiDTO.setDescricaoToken(logApi.getDescricaoToken());
        logApiDTO.setDataUso(logApi.getDataUso());
        logApiDTO.setIp(logApi.getIp());
        logApiDTO.setMethod(logApi.getMethod());
        logApiDTO.setUri(logApi.getUri());
        logApiDTO.setParams(logApi.getParams());
        return logApiDTO;
    }
}
