package com.pacto.adm.core.adapters;

import com.pacto.adm.core.adapters.ambiente.AmbienteAdapter;
import com.pacto.adm.core.adapters.colaborador.ColaboradorAdapter;
import com.pacto.adm.core.adapters.nivelturma.NivelTurmaAdapter;
import com.pacto.adm.core.dto.HorarioTurmaDTO;
import com.pacto.adm.core.entities.contrato.HorarioTurma;
import org.springframework.stereotype.Component;

@Component
public class HorarioTurmaAdapter implements AdapterInterface<HorarioTurma, HorarioTurmaDTO> {

    private final AmbienteAdapter ambienteAdapter;
    private final ColaboradorAdapter colaboradorAdapter;

    private final NivelTurmaAdapter nivelTurmaAdapter;

    public HorarioTurmaAdapter(AmbienteAdapter ambienteAdapter, NivelTurmaAdapter nivelTurmaAdapter, ColaboradorAdapter colaboradorAdapter) {
        this.ambienteAdapter = ambienteAdapter;
        this.nivelTurmaAdapter = nivelTurmaAdapter;
        this.colaboradorAdapter = colaboradorAdapter;

    }

    @Override
    public HorarioTurmaDTO toDto(HorarioTurma horarioTurma) {
        HorarioTurmaDTO dto = new HorarioTurmaDTO(
                horarioTurma.getCodigo(),
                horarioTurma.getDiaSemanaNumero(),
                horarioTurma.getHoraInicial(),
                horarioTurma.getHoraFinal(),
                horarioTurma.getDiaSemana());
        if (horarioTurma.getAmbiente() != null) {
            dto.setAmbiente(ambienteAdapter.toDto(horarioTurma.getAmbiente()));
        }
        if (horarioTurma.getProfessor() != null) {
            dto.setProfessor(colaboradorAdapter.toDto(horarioTurma.getProfessor()));
        }
        if (horarioTurma.getNivelTurma() != null) {
            dto.setNivelTurma(nivelTurmaAdapter.toDto(horarioTurma.getNivelTurma()));
        }
        return dto;
    }
}
