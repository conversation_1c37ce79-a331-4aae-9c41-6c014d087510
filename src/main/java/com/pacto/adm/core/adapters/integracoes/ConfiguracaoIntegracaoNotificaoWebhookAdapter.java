package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoNotificacaoWebhookDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoNotificaoWebhookAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoNotificacaoWebhookDTO> {

    @Override
    public ConfiguracaoIntegracaoNotificacaoWebhookDTO toDto(Empresa empresa) {
        if (empresa != null) {
            ConfiguracaoIntegracaoNotificacaoWebhookDTO configuracaoIntegracaoNotificacaoWebhookDTO = new ConfiguracaoIntegracaoNotificacaoWebhookDTO();
            configuracaoIntegracaoNotificacaoWebhookDTO.setNotificarWebhook(empresa.isNotificarWebhook());
            configuracaoIntegracaoNotificacaoWebhookDTO.setUrlWebhookNotificar(empresa.getUrlWebhookNotificar());
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            configuracaoIntegracaoNotificacaoWebhookDTO.setEmpresa(empresaDTO);
            return configuracaoIntegracaoNotificacaoWebhookDTO;
        }
        return null;
    }

    @Override
    public Empresa toEntity(ConfiguracaoIntegracaoNotificacaoWebhookDTO configNotificacaoWebhookDTO, Empresa empresa) {
        if (configNotificacaoWebhookDTO != null && configNotificacaoWebhookDTO.getEmpresa() != null) {
            try {
                empresa.setNotificarWebhook(configNotificacaoWebhookDTO.isNotificarWebhook());
                empresa.setUrlWebhookNotificar(configNotificacaoWebhookDTO.getUrlWebhookNotificar());
                return empresa;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
