package com.pacto.adm.core.adapters;

import com.pacto.adm.core.adapters.empresa.EmpresaAdapter;
import com.pacto.adm.core.dto.JustificativaOperacaoDTO;
import com.pacto.adm.core.entities.contrato.JustificativaOperacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class JustificativaOperacaoAdapter implements AdapterInterface<JustificativaOperacao, JustificativaOperacaoDTO> {

    @Autowired
    EmpresaAdapter empresaAdapter;

    @Override
    public JustificativaOperacao toEntity(JustificativaOperacaoDTO justificativaOperacaoDTO) {
        if (justificativaOperacaoDTO != null) {
            JustificativaOperacao justificativaOperacao = new JustificativaOperacao();
            justificativaOperacao.setCodigo(justificativaOperacaoDTO.getCodigo());
            justificativaOperacao.setTipoOperacao(justificativaOperacaoDTO.getTipoOperacao());
            justificativaOperacao.setDescricao(justificativaOperacaoDTO.getDescricao());
            justificativaOperacao.setEmpresa(empresaAdapter.toEntity(justificativaOperacaoDTO.getEmpresa()));
            justificativaOperacao.setIsentarMultaCancelamento(justificativaOperacaoDTO.getIsentarMultaCancelamento());
            justificativaOperacao.setNaoCobrarParcelasAtrasadasCancelamento(justificativaOperacaoDTO.getNaoCobrarParcelasAtrasadasCancelamento());
            justificativaOperacao.setNecessarioAnexarComprovante(justificativaOperacaoDTO.getNecessarioAnexarComprovante());
            justificativaOperacao.setAtiva(justificativaOperacaoDTO.getAtiva());
            return justificativaOperacao;
        }
        return null;
    }

    @Override
    public JustificativaOperacaoDTO toDto(JustificativaOperacao justificativaOperacao) {
        if (justificativaOperacao != null) {
            JustificativaOperacaoDTO justificativaOperacaoDTO = new JustificativaOperacaoDTO();
            justificativaOperacaoDTO.setCodigo(justificativaOperacao.getCodigo());
            justificativaOperacaoDTO.setTipoOperacao(justificativaOperacao.getTipoOperacao());
            justificativaOperacaoDTO.setDescricao(justificativaOperacao.getDescricao());
            justificativaOperacaoDTO.setEmpresa(empresaAdapter.toDto(justificativaOperacao.getEmpresa()));
            justificativaOperacaoDTO.setIsentarMultaCancelamento(justificativaOperacao.getIsentarMultaCancelamento());
            justificativaOperacaoDTO.setNaoCobrarParcelasAtrasadasCancelamento(justificativaOperacao.getNaoCobrarParcelasAtrasadasCancelamento());
            justificativaOperacaoDTO.setNecessarioAnexarComprovante(justificativaOperacao.getNecessarioAnexarComprovante());
            justificativaOperacaoDTO.setAtiva(justificativaOperacao.getAtiva());
            return justificativaOperacaoDTO;
        }
        return null;
    }

}
