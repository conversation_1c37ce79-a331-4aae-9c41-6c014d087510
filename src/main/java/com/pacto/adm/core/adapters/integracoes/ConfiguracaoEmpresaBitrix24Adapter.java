package com.pacto.adm.core.adapters.empresa;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoEmpresaBitrix24DTO;
import com.pacto.adm.core.entities.ConfiguracaoEmpresaBitrix24;
import com.pacto.adm.core.entities.Usuario;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoEmpresaBitrix24Adapter implements AdapterInterface<ConfiguracaoEmpresaBitrix24, ConfiguracaoEmpresaBitrix24DTO> {

    @Override
    public ConfiguracaoEmpresaBitrix24DTO toDto(ConfiguracaoEmpresaBitrix24 configEntity) {
        ConfiguracaoEmpresaBitrix24DTO configDTO = new ConfiguracaoEmpresaBitrix24DTO();
        configDTO.setCodigo(configEntity.getCodigo());
        configDTO.setUrl(configEntity.getUrl());
        configDTO.setAcaoobjecao(configEntity.getAcaoobjecao());
        configDTO.setAcao(configEntity.getAcao());
        configDTO.setHabilitada(configEntity.isHabilitada());
        configDTO.setEmpresa(configEntity.getEmpresa());

        if (configEntity.getResponsavelPadrao() != null) {
            UsuarioDTO responsavelPadrao = new UsuarioDTO();
            responsavelPadrao.setCodigo(configEntity.getResponsavelPadrao().getCodigo());
            responsavelPadrao.setNome(configEntity.getResponsavelPadrao().getNome());
            configDTO.setResponsavelPadrao(responsavelPadrao);
        }
        // Mapeie outros campos, se necessário
        return configDTO;
    }

    @Override
    public ConfiguracaoEmpresaBitrix24 toEntity(ConfiguracaoEmpresaBitrix24DTO configDTO) {
        if (configDTO != null) {
            ConfiguracaoEmpresaBitrix24 configEntity = new ConfiguracaoEmpresaBitrix24();
            configEntity.setCodigo(configDTO.getCodigo());
            if (configDTO.getResponsavelPadrao() != null) {
                Usuario usuarioEntity = new Usuario();
                usuarioEntity.setCodigo(configDTO.getResponsavelPadrao().getCodigo());
                usuarioEntity.setNome(configDTO.getResponsavelPadrao().getNome());
                configEntity.setResponsavelPadrao(usuarioEntity);
            }
            configEntity.setEmpresa(configDTO.getEmpresa());
            configEntity.setUrl(configDTO.getUrl());
            configEntity.setAcaoobjecao(configDTO.getAcaoobjecao());
            configEntity.setAcao("{'getlead': 'crm.lead.get.json', 'updatelead':  'crm.lead.update.json'}");
            configEntity.setHabilitada(configDTO.isHabilitada());
            // Mapeie outros campos, se necessário
            return configEntity;
        }
        return null;
    }
}
