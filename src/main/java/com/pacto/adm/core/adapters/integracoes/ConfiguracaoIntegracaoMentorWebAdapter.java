package com.pacto.adm.core.adapters.integracoes;

import com.pacto.adm.core.adapters.AdapterInterface;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoMentorWebDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.entities.empresa.Empresa;
import org.springframework.stereotype.Component;

@Component
public class ConfiguracaoIntegracaoMentorWebAdapter implements AdapterInterface<Empresa, ConfiguracaoIntegracaoMentorWebDTO> {

    @Override
    public ConfiguracaoIntegracaoMentorWebDTO toDto(Empresa empresa) {
        if (empresa != null) {
            ConfiguracaoIntegracaoMentorWebDTO configuracaoIntegracaoMentorWebDTO = new ConfiguracaoIntegracaoMentorWebDTO();
            configuracaoIntegracaoMentorWebDTO.setHabilitada(empresa.isIntegracaoMentorWebHabilitada());
            configuracaoIntegracaoMentorWebDTO.setUrl(empresa.getIntegracaoMentorWebUrl());
            configuracaoIntegracaoMentorWebDTO.setUser(empresa.getIntegracaoMentorWebUser());
            configuracaoIntegracaoMentorWebDTO.setPassword(empresa.getIntegracaoMentorWebPassword());
            configuracaoIntegracaoMentorWebDTO.setServico(empresa.getIntegracaoMentorWebServico());
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            configuracaoIntegracaoMentorWebDTO.setEmpresa(empresaDTO);
            return configuracaoIntegracaoMentorWebDTO;
        }
        return null;
    }

    @Override
    public Empresa toEntity(ConfiguracaoIntegracaoMentorWebDTO configMentorWebDTO, Empresa empresa) {
        if (configMentorWebDTO != null && configMentorWebDTO.getEmpresa() != null) {
            try {
                empresa.setIntegracaoMentorWebHabilitada(configMentorWebDTO.isHabilitada());
                empresa.setIntegracaoMentorWebUrl(configMentorWebDTO.getUrl());
                empresa.setIntegracaoMentorWebUser(configMentorWebDTO.getUser());
                empresa.setIntegracaoMentorWebPassword(configMentorWebDTO.getPassword());
                empresa.setIntegracaoMentorWebServico(configMentorWebDTO.getServico());
                return empresa;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

}
