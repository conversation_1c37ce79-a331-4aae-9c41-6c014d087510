package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.ContratoOperacaoDTO;
import com.pacto.adm.core.entities.contrato.ContratoOperacao;

import com.pacto.config.utils.Uteis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ContratoOperacaoAdapter implements AdapterInterface<ContratoOperacao, ContratoOperacaoDTO> {

    @Autowired
    ContratoAdapter contratoAdapter;
    @Autowired
    JustificativaOperacaoAdapter justificativaOperacaoAdapter;
    @Autowired
    ArquivoAdapter arquivoAdapter;
    @Autowired
    UsuarioAdapter usuarioAdapter;
    @Value("${url.fotos.nuvem}")
    private String urlFotosNuvem;

    @Override
    public ContratoOperacao toEntity(ContratoOperacaoDTO contratoOperacaoDTO) {
        ContratoOperacao contratoOperacao = new ContratoOperacao();
        contratoOperacao = toEntity(contratoOperacaoDTO, contratoOperacao);
        return contratoOperacao;
    }

    @Override
    public ContratoOperacao toEntity(ContratoOperacaoDTO contratoOperacaoDTO, ContratoOperacao contratoOperacao) {
        contratoOperacao.setCodigo(contratoOperacaoDTO.getCodigo());
        contratoOperacao.setClienteTransfereDias(contratoOperacaoDTO.getClienteTransfereDias());
        contratoOperacao.setClienteRecebeDias(contratoOperacaoDTO.getClienteRecebeDias());
        contratoOperacao.setDescricaoCalculo(contratoOperacaoDTO.getDescricaoCalculo());
        contratoOperacao.setObservacao(contratoOperacaoDTO.getObservacao());
        contratoOperacao.setDataOperacao(contratoOperacaoDTO.getDataOperacao());
        contratoOperacao.setDataInicioEfetivacaoOperacao(contratoOperacaoDTO.getDataInicioEfetivacaoOperacao());
        contratoOperacao.setDataFimEfetivacaoOperacao(contratoOperacaoDTO.getDataFimEfetivacaoOperacao());
        contratoOperacao.setOperacaoPaga(contratoOperacaoDTO.getOperacaoPaga());
        contratoOperacao.setTipoOperacao(contratoOperacaoDTO.getTipoOperacao());
        contratoOperacao.setValor(contratoOperacaoDTO.getValor());
        contratoOperacao.setNrDiasOperacao(contratoOperacaoDTO.getNrDiasOperacao());
        contratoOperacao.setInformacoes(contratoOperacaoDTO.getInformacoes());
        contratoOperacao.setChaveArquivo(contratoOperacaoDTO.getChaveArquivo());
        contratoOperacao.setOrigemSistema(contratoOperacaoDTO.getOrigemSistema());
        contratoOperacao.setInformacoesDesfazer(contratoOperacaoDTO.getInformacoesDesfazer());
        contratoOperacao.setContrato(contratoAdapter.toEntity(contratoOperacaoDTO.getContrato()));
        contratoOperacao.setTipoJustificativa(justificativaOperacaoAdapter.toEntity(contratoOperacaoDTO.getTipoJustificativa()));
        contratoOperacao.setResponsavel(usuarioAdapter.toEntity(contratoOperacaoDTO.getResponsavel()));
        if (contratoOperacaoDTO.getResponsavelLiberacao() != null) {
            contratoOperacao.setResponsavelLiberacao(usuarioAdapter.toEntity(contratoOperacaoDTO.getResponsavelLiberacao()));
        }
        return contratoOperacao;
    }

    @Override
    public ContratoOperacaoDTO toDto(ContratoOperacao contratoOperacao) {
        ContratoOperacaoDTO contratoOperacaoDTO = new ContratoOperacaoDTO();
        contratoOperacaoDTO.setCodigo(contratoOperacao.getCodigo());
        contratoOperacaoDTO.setClienteTransfereDias(contratoOperacao.getClienteTransfereDias());
        contratoOperacaoDTO.setClienteRecebeDias(contratoOperacao.getClienteRecebeDias());
        contratoOperacaoDTO.setDescricaoCalculo(contratoOperacao.getDescricaoCalculo());
        contratoOperacaoDTO.setObservacao(contratoOperacao.getObservacao());
        contratoOperacaoDTO.setDataOperacao(contratoOperacao.getDataOperacao());
        contratoOperacaoDTO.setDataInicioEfetivacaoOperacao(contratoOperacao.getDataInicioEfetivacaoOperacao());
        contratoOperacaoDTO.setDataFimEfetivacaoOperacao(contratoOperacao.getDataFimEfetivacaoOperacao());
        contratoOperacaoDTO.setOperacaoPaga(contratoOperacao.getOperacaoPaga());
        contratoOperacaoDTO.setTipoOperacao(contratoOperacao.getTipoOperacao());
        contratoOperacaoDTO.setValor(contratoOperacao.getValor());
        contratoOperacaoDTO.setNrDiasOperacao(contratoOperacao.getNrDiasOperacao());
        contratoOperacaoDTO.setInformacoes(contratoOperacao.getInformacoes());
        contratoOperacaoDTO.setOrigemSistema(contratoOperacao.getOrigemSistema());
        contratoOperacaoDTO.setInformacoesDesfazer(contratoOperacao.getInformacoesDesfazer());
        contratoOperacaoDTO.setContrato(contratoAdapter.toDto(contratoOperacao.getContrato()));
        if (contratoOperacaoDTO.getDiasOperacao() == null) {
            Long diasOperacao = null;
            if (isDeveCalcularNrDias(contratoOperacao.getTipoOperacao())) {
                long nrDias = Uteis.nrDiasEntreDatas(contratoOperacao.getDataInicioEfetivacaoOperacao(), contratoOperacao.getDataFimEfetivacaoOperacao()) + 1;
                diasOperacao = nrDias;
            }
            contratoOperacaoDTO.setDiasOperacao(diasOperacao);
        }
        if (contratoOperacao.getTipoJustificativa() != null) {
            contratoOperacaoDTO.setTipoJustificativa(justificativaOperacaoAdapter.toDto(contratoOperacao.getTipoJustificativa()));
        }
        if (contratoOperacao.getResponsavel() != null) {
            contratoOperacaoDTO.setResponsavel(usuarioAdapter.toDto(contratoOperacao.getResponsavel()));
        }
        if (contratoOperacao.getResponsavelLiberacao() != null) {
            contratoOperacaoDTO.setResponsavelLiberacao(usuarioAdapter.toDto(contratoOperacao.getResponsavelLiberacao()));
        }
        if (contratoOperacao.getChaveArquivo() != null) {
            contratoOperacaoDTO.setChaveArquivo(contratoOperacao.getChaveArquivo());
            contratoOperacaoDTO.setUrlArquivoAtestado(Uteis.obterUrlFotoDaNuvem(contratoOperacao.getChaveArquivo()));
        } else {
            contratoOperacaoDTO.setChaveArquivo("");
            contratoOperacaoDTO.setUrlArquivoAtestado(Uteis.obterUrlFotoDaNuvem(null));
        }
        return contratoOperacaoDTO;
    }


    private boolean isDeveCalcularNrDias(String tipoOperacao) {
        return !(tipoOperacao.contains("CA")
                || tipoOperacao.contains("AH")
                || tipoOperacao.contains("IM")
                || tipoOperacao.contains("AM")
                || tipoOperacao.contains("EM")
                || tipoOperacao.contains("TS")
                || tipoOperacao.contains("TE"));
    }

}

