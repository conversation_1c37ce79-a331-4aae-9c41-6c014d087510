package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.QuestionarioPerguntaClienteDTO;
import com.pacto.adm.core.dto.RespostaPergClienteDTO;
import com.pacto.adm.core.entities.QuestionarioPerguntaCliente;
import com.pacto.adm.core.entities.RespostaPergCliente;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RespostaPergClienteAdapter implements AdapterInterface<RespostaPergCliente, RespostaPergClienteDTO> {


    @Override
    public RespostaPergCliente toEntity(RespostaPergClienteDTO respostaPergClienteDTO) {
        if(respostaPergClienteDTO != null) {
            RespostaPergCliente respostaPergCliente = new RespostaPergCliente();
            respostaPergCliente.setRespostaOpcao(respostaPergClienteDTO.getRespostaOpcao());
            respostaPergCliente.setRespostaTextual(respostaPergClienteDTO.getRespostaTextual());
            respostaPergCliente.setDescricaoRespota(respostaPergClienteDTO.getDescricaoRespota());
            respostaPergCliente.setCodigo(respostaPergClienteDTO.getCodigo());

            return respostaPergCliente;
        }

        return null;
    }

    @Override
    public RespostaPergClienteDTO toDto(RespostaPergCliente respostaPergCliente) {
        if (respostaPergCliente != null) {
            RespostaPergClienteDTO respostaPergClienteDTO = new RespostaPergClienteDTO();
            respostaPergClienteDTO.setCodigo(respostaPergCliente.getCodigo());
            respostaPergClienteDTO.setRespostaOpcao(respostaPergCliente.getRespostaOpcao());
            respostaPergClienteDTO.setDescricaoRespota(respostaPergCliente.getDescricaoRespota());
            respostaPergClienteDTO.setRespostaTextual(respostaPergCliente.getRespostaTextual());
            return respostaPergClienteDTO;
        }
        return null;
    }
}
