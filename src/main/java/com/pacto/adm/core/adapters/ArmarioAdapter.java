package com.pacto.adm.core.adapters;

import com.pacto.adm.core.dto.ArmarioDTO;
import com.pacto.adm.core.entities.Armario;
import org.springframework.stereotype.Component;

@Component
public class ArmarioAdapter implements AdapterInterface<Armario, ArmarioDTO> {

    public ArmarioAdapter() {
    }

    @Override
    public Armario toEntity(ArmarioDTO dto) {
        Armario obj = new Armario();
        obj = toEntity(dto, obj);
        return obj;
    }

    @Override
    public Armario toEntity(ArmarioDTO dto, Armario obj) {
        obj.setCodigo(dto.getCodigo());
        obj.setDataCadastro(dto.getDataCadastro());
        obj.setDescricao(dto.getDescricao());
        obj.setTamanhoArmario(dto.getTamanhoArmario());
        obj.setEmpresa(dto.getEmpresa());
        obj.setResponsavelCadastro(dto.getResponsavelCadastro());
        obj.setDataCadastro(dto.getDataCadastro());
        obj.setStatus(dto.getStatus());
        obj.setNumeracao(dto.getNumeracao());
        obj.setAluguelAtual(dto.getAluguelAtual());
        obj.setGrupo(dto.getGrupo());
        return obj;
    }

    @Override
    public ArmarioDTO toDto(Armario obj) {
        ArmarioDTO dto = new ArmarioDTO();
        if (obj != null) {
            dto.setCodigo(obj.getCodigo());
            dto.setDataCadastro(obj.getDataCadastro());
            dto.setDescricao(obj.getDescricao());
            dto.setTamanhoArmario(obj.getTamanhoArmario());
            dto.setEmpresa(obj.getEmpresa());
            dto.setResponsavelCadastro(obj.getResponsavelCadastro());
            dto.setStatus(obj.getStatus());
            dto.setNumeracao(obj.getNumeracao());
            dto.setAluguelAtual(obj.getAluguelAtual());
            dto.setGrupo(obj.getGrupo());
            return dto;
        }
        return null;
    }

}
