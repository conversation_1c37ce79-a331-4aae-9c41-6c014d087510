package com.pacto.adm.core.swagger.respostas.justificativaoperacao;

import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.entities.contrato.JustificativaOperacao;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas das requisições envolvendo justificativas de operação com paginação")
public class ExemploRespostaListJustificativaOperacaoPaginacao extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<JustificativaOperacao> content;

    public List<JustificativaOperacao> getContent() {
        return content;
    }

    public void setContent(List<JustificativaOperacao> content) {
        this.content = content;
    }

    public static final String resposta = "{"
            + "  \"content\": [{"
            + "    \"codigo\": 1,"
            + "    \"tipoOperacao\": \"CA\","
            + "    \"descricao\": \"CT.12345.CA.Transferência para outro aluno\","
            + "    \"isentarMultaCancelamento\": false,"
            + "    \"naoCobrarParcelasAtrasadasCancelamento\": false,"
            + "    \"necessarioAnexarComprovante\": true,"
            + "    \"ativa\": true,"
            + "    \"empresa\": {"
            + "      \"codigo\": 1,"
            + "      \"nome\": \"Academia Exemplo\""
            + "    }"
            + "  }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
