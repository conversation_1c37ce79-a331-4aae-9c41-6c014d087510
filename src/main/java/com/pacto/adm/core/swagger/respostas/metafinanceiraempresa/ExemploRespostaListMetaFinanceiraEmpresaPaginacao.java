package com.pacto.adm.core.swagger.respostas.metafinanceiraempresa;

import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.metafinanceiraempresa.MetaFinanceiraEmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas das requisições envolvendo metas financeiras de empresas com paginação")
public class ExemploRespostaListMetaFinanceiraEmpresaPaginacao extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<MetaFinanceiraEmpresaDTO> content;

    public List<MetaFinanceiraEmpresaDTO> getContent() {
        return content;
    }

    public void setContent(List<MetaFinanceiraEmpresaDTO> content) {
        this.content = content;
    }

    public static final String resposta = "{"
            + "  \"content\": [{"
            + "    \"codigo\": 1,"
            + "    \"descricao\": \"Meta de faturamento mensal para expansão\","
            + "    \"mes\": 10,"
            + "    \"ano\": 2024"
            + "  }, {"
            + "    \"codigo\": 2,"
            + "    \"descricao\": \"Meta de receita trimestral\","
            + "    \"mes\": 11,"
            + "    \"ano\": 2024"
            + "  }],"
            + "  \"totalElements\": 2,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
