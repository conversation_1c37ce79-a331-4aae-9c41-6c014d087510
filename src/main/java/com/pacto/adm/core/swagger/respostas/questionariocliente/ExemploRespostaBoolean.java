package com.pacto.adm.core.swagger.respostas.questionariocliente;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas das requisições que retornam valores booleanos")
public class ExemploRespostaBoolean {

    @Schema(description = "Conteúdo da resposta contendo o valor booleano encontrado", example = "true")
    private Boolean content;

    public Boolean getContent() {
        return content;
    }

    public void setContent(Boolean content) {
        this.content = content;
    }

    public static final String resposta = "{\"content\": true}";
}
