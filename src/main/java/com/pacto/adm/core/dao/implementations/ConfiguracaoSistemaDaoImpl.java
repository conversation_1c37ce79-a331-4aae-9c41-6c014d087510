package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ConfiguracaoSistemaDao;
import com.pacto.adm.core.entities.ConfiguracaoSistema;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public class ConfiguracaoSistemaDaoImpl extends DaoGenericoImpl<ConfiguracaoSistema, Integer> implements ConfiguracaoSistemaDao {


    @Override
    public ConfiguracaoSistema get() throws Exception {
        List<ConfiguracaoSistema> configuracaoSistema = findAll();
        if(configuracaoSistema == null || configuracaoSistema.size() == 0 ){
            throw new Exception("Nenhuma configuração do sistema foi encontrada");
        }

        if(configuracaoSistema.size() > 1){
            throw new Exception("Mais de um registro foi encontrado em configuracaosistema");
        }

        return configuracaoSistema.get(0);
    }
}
