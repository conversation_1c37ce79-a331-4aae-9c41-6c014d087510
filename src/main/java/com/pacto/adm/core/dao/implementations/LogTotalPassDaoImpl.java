package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.LogTotalPassDao;
import com.pacto.adm.core.entities.contrato.LogTotalPass;
import com.pacto.config.dto.PaginadorDTO;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class LogTotalPassDaoImpl extends DaoGenericoImpl<LogTotalPass, Integer> implements LogTotalPassDao {

    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<LogTotalPass> findByPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append(" WHERE obj.pessoa = :codPessoa");

        params.put("codPessoa", codPessoa);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("origem")) {
                    where.append(" order by obj.origem " + sortOrder);
                } else if (sortField.equalsIgnoreCase("dataregistro")) {
                    where.append(" order by obj.dataregistro " + sortOrder);
                } else if (sortField.equalsIgnoreCase("usuario")) {
                    where.append(" order by obj.usuario " + sortOrder);
                } else if (sortField.equalsIgnoreCase("tempoResposta")) {
                    where.append(" order by obj.tempoResposta " + sortOrder);
                }
            } else {
                where.append(" order by obj.dataregistro desc ");
            }
        } else {
            where.append(" order by obj.codigo desc ");
        }

        return findByParam(where, params, maxResults, indiceInicial);
    }
}
