package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.dto.ConfigConsultaTurmaDTO;
import com.pacto.adm.core.dto.filtros.FiltroNegociacaoPlanoJSON;
import com.pacto.adm.core.dto.negociacao.*;
import com.pacto.adm.core.entities.contrato.Plano;
import com.pacto.adm.core.entities.contrato.SituacaoContratoEnum;
import com.pacto.config.exceptions.ServiceException;

import java.sql.Connection;
import java.util.Date;
import java.util.List;

public interface PlanoDao extends DaoGenerico<Plano, Integer> {

    List<Plano> findVigentesByEmpresa(Integer empresa,
                                      Integer planoForcar,
                                      Boolean consultarEmPlanoEmpresaVendaPermitida,
                                      FiltroNegociacaoPlanoJSON filtros,Integer codigoCliente,
                                      Integer contratoRenovar) throws Exception;

    List<Plano> findByCodigos(List<Integer> codigos) throws Exception;

    List<AgendaTurmaDTO> montarAgendaTurma(Integer modalidade,
                                           Integer nivel,
                                           Integer professor,
                                           String periodo,
                                           String disponibilidade,
                                           Integer cliente,
                                           Integer contratoBaseado,
                                           Date dataBase,
                                           Integer empresa) throws ServiceException;

    ConfigConsultaTurmaDTO configAgenda(Integer empresa, Integer modalidade) throws ServiceException;

    List<DiaCartaoDTO> diasCartao(Integer plano, Integer diaAtual) throws Exception;

    List<DiaCartaoDTO> diasProRata(Integer diaAtual, String dias);

    List<PlanoDuracaoDTO> duracoesPorPlano(Integer plano, boolean addObsOperacao) throws Exception;

    List<DescontoDTO> descontosPorPlano(Integer empresa) throws Exception;

    List<PlanoHorarioDTO> horariosPorPlano(Integer plano, boolean addObsOperacao) throws Exception;

    List<PlanoModalidadeDTO> modalidadesPorPlano(Integer plano, Boolean creditoLivreMarcarAula, Integer contratoBaseado) throws Exception;

    List<PacoteDTO> pacotesPorPlano(Integer plano) throws Exception;

    List<PlanoProdutoDTO> produtodosPorPlano(Integer plano,
                                             SituacaoContratoEnum tipoContrato) throws Exception;

    List<Integer> horariosFixar(Integer ... horarios) throws Exception;

    Integer contratoEPlano(Integer plano, Integer contratoBaseado) throws Exception;
}
