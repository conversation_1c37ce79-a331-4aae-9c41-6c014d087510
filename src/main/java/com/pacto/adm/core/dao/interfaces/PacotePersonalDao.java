package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.PacotePersonal;
import com.pacto.adm.core.entities.Produto;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface PacotePersonalDao extends DaoGenerico<PacotePersonal, Integer> {

    List<PacotePersonal> findByProduto(Integer produto) throws Exception;

}
