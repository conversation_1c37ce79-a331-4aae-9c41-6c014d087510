package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.filtros.FiltroUsuarioJSON;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface UsuarioDao extends DaoGenerico<Usuario, Integer> {

    List<Usuario> findAll(FiltroUsuarioJSON filtros) throws Exception;

    List<Usuario> consultarUsuariosMeta(FiltroUsuarioJSON filtros) throws Exception;

    List<UsuarioDTO> consultarSimples(Integer empresa) throws ServiceException;

}
