package com.pacto.adm.core.dao.interfaces.questionariocliente;

import com.pacto.adm.core.entities.ClienteMensagem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ClienteMensagemRepository extends JpaRepository<ClienteMensagem, Integer> {

    boolean existsByClienteCodigoAndQuestionarioCliente(Integer clienteCodigo, Integer questionarioCliente);

    List<ClienteMensagem> findByClienteCodigoAndTipoMensagem(Integer clienteCodigo, String tipoMensagem);
}
