package com.pacto.adm.core.dao.interfaces;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.contrato.HistoricoContrato;

import java.util.List;

import java.util.Date;

public interface HistoricoContratoDao extends DaoGenerico<HistoricoContrato, Integer> {

    HistoricoContrato obterUltimoHistoricoContratoPorContrato(Integer codigoContrato) throws Exception;

    List<HistoricoContrato> findAllByContrato(Integer codigoContrato, PaginadorDTO paginadorDTO) throws Exception;

    HistoricoContrato obterUltimoHistoricoContratoPorContratoTipoHistorico(Integer codigoContrato, String tipoHistorico);

    HistoricoContrato obterHistoricoContratoPorCodigoContratoDataInicioDataFim(Integer codigoContrato, Date dataInicio);

    boolean obterHistoricoContratoPorCodigoContratoDescricao(Integer codContrato, String descricao) throws Exception;
}
