package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.Cliente;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ClienteRepository extends JpaRepository<Cliente, Integer> {

    @Modifying
    @Query("UPDATE Cliente c SET c.freepass.codigo = :codProdutoFreepass, c.responsavelfreepass = :codResponsavelFreepass")
    void atualizarFreepass(
            @Param("codProdutoFreepass") Integer codProdutoFreepass,
            @Param("codResponsavelFreepass") Integer codResponsavelFreepass
    );

    @Query("SELECT c.pessoa.codigo FROM Cliente c WHERE c.codigo = :codCliente")
    Integer findCodPessoaByCodCliente(@Param("codCliente") Integer codCliente);

}
