package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.DescontoDao;
import com.pacto.adm.core.entities.Produto;
import com.pacto.adm.core.entities.financeiro.Desconto;
import com.pacto.config.exceptions.ServiceException;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

@Repository
public class DescontoDaoImpl extends DaoGenericoImpl<Desconto, Integer> implements DescontoDao {

    public List<Desconto> consultarPorTipoProdutoPorEmpresa(String tipoProduto, Integer empresa) throws Exception{
        List<Desconto> descontos = new ArrayList<>();
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(con -> {
                    try {
                        StringBuilder sql = new StringBuilder();
                        sql.append("SELECT d.* ");
                        sql.append("FROM Desconto d ");
                        sql.append("LEFT JOIN descontoempresa de on de.desconto = d.codigo ");
                        sql.append("WHERE upper( d.tipoProduto ) like('");
                        sql.append(tipoProduto.toUpperCase());
                        sql.append("%') AND ");
                        sql.append("d.ativo = TRUE ");
                        sql.append("AND ( de.empresa = ");
                        sql.append(empresa);
                        sql.append(" OR d.aplicarempresas IS FALSE OR d.aplicarempresas IS NULL) ");
                        sql.append("ORDER BY d.tipoProduto");
                        PreparedStatement pst = con.prepareStatement(sql.toString());
                        ResultSet rs = pst.executeQuery();
                        while (rs.next()) {
                            Desconto desconto = new Desconto();
                            desconto.setTipoProduto(rs.getString("tipoproduto"));
                            desconto.setTipoDesconto(rs.getString("tipodesconto"));
                            desconto.setValor(rs.getDouble("valor"));
                            desconto.setDescricao(rs.getString("descricao"));
                            desconto.setCodigo(rs.getInt("codigo"));
                            desconto.setAtivo(rs.getBoolean("ativo"));
                            descontos.add(desconto);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return descontos;
    }

}
