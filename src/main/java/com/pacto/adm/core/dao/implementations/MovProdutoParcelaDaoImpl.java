package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.MovProdutoParcelaDao;
import com.pacto.adm.core.entities.MovProdutoParcela;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.sql.PreparedStatement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class MovProdutoParcelaDaoImpl extends DaoGenericoImpl<MovProdutoParcela, Integer> implements MovProdutoParcelaDao {
    private static final int MAXIMO_RESULTADOS = 10;


    public List<MovProdutoParcela> findByMovProduto(Integer codigoMovProduto) {

        getCurrentSession().clear();
        StringBuilder hql = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        hql.append("SELECT mpp FROM MovProdutoParcela mpp ");
        hql.append(" WHERE mpp.movProduto.codigo = :codigoMovProduto");
        params.put("codigoMovProduto", codigoMovProduto);
        hql.append(" ORDER BY mpp.movParcela.dataVencimento DESC");

        Query query = getCurrentSession().createQuery(hql.toString());
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            query.setParameter(entry.getKey(), entry.getValue());
        }
        return query.getResultList();
    }

    public List<MovProdutoParcela> findAllByRecibo(Integer codRecibo) throws Exception {
        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.reciboPagamento.codigo = :codRecibo\n");
        params.put("codRecibo", codRecibo);

        return findByParam(where, params);
    }

    public List<MovProdutoParcela> findAllByParcela(Integer codParcela) throws Exception {
        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.movParcela.codigo = :codParcela\n");
        params.put("codParcela", codParcela);

        return findByParam(where, params);
    }

    @Override
    public void alterarSomenteReciboPagamento(MovProdutoParcela movProdutoParcela) throws Exception {
        getCurrentSession().clear();

        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try {
                    String sql = "UPDATE MovProdutoParcela SET reciboPagamento = ? WHERE codigo = ?";
                    try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                        if (UteisValidacao.emptyNumber(movProdutoParcela.getReciboPagamento().getCodigo())) {
                            sqlAlterar.setNull(1, 0);
                        } else {
                            sqlAlterar.setInt(1, movProdutoParcela.getReciboPagamento().getCodigo());
                        }
                        sqlAlterar.setInt(2, movProdutoParcela.getCodigo());
                        sqlAlterar.execute();
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    con.close();
                }
            });
        }
    }

    @Override
    public List<MovProdutoParcela> consultarPorCodigoMovProdutos(Integer codProduto) throws Exception {
        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.movProduto.codigo = :codProduto\n");
        params.put("codProduto", codProduto);

        return findByParam(where, params);
    }

    @Override
    public List<MovProdutoParcela> findByMovProduto(Integer codMovProduto, PaginadorDTO paginadorDTO) throws Exception {
        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("INNER JOIN obj.movProduto mPrd\n");
        where.append("INNER JOIN obj.movParcela mPar\n");
        where.append("WHERE mPrd.codigo = :codigoMovProduto\n");
        params.put("codigoMovProduto", codMovProduto);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sort = paginadorDTO.getSort().split(",")[0].trim();
                String orderDirection = paginadorDTO.getSort().split(",")[1];
                if (sort.equalsIgnoreCase("codigo")) {
                    where.append(" ORDER BY mPar.codigo ").append(orderDirection);
                } else if (sort.equalsIgnoreCase("contrato")) {
                    where.append(" ORDER BY mPar.contrato ").append(orderDirection);
                } else if (sort.equalsIgnoreCase("descricao")) {
                    where.append(" ORDER BY mPar.descricao ").append(orderDirection);
                } else if (sort.equalsIgnoreCase("dataVencimento")) {
                    where.append(" ORDER BY mPar.dataVencimento ").append(orderDirection);
                } else if (sort.equalsIgnoreCase("valor")) {
                    where.append(" ORDER BY mPar.valorParcela ").append(orderDirection);
                } else if (sort.equalsIgnoreCase("situacao")) {
                    where.append(" ORDER BY mPar.situacao ").append(orderDirection);
                } else if (sort.equalsIgnoreCase("empresa")) {
                    where.append(" ORDER BY mPar.empresa.nome ").append(orderDirection);
                }
            } else {
                where.append(" ORDER BY mPar.dataVencimento DESC");
            }
        } else {
            where.append(" ORDER BY mPar.dataVencimento DESC");
        }

        return findByParam(where, params, maxResults, indiceInicial);
    }
}
