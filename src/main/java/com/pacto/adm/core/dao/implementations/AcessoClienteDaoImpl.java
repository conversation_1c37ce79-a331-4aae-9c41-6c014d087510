package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.AcessoClienteDao;
import com.pacto.adm.core.entities.AcessoCliente;
import com.pacto.config.dto.PaginadorDTO;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class AcessoClienteDaoImpl extends DaoGenericoImpl<AcessoCliente, Integer> implements AcessoClienteDao {

    private static final int MAXIMO_RESULTADOS = 10;
    @Override
    public List<AcessoCliente> listarRegistrDeAcessoCliente(Integer codigoCliente) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT acesso FROM AcessoCliente acesso WHERE acesso.cliente = "+codigoCliente);

        Query query = getCurrentSession().createQuery(sql.toString());

        if (query.getResultList().size() > 0) {
            return (List<AcessoCliente>) query.getResultList();
        } else {
            return null;
        }
    }

    @Override
    public List<AcessoCliente>listarRegistrDeAcessoClienteDia(Integer matricula, LocalDate data) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT acesso FROM AcessoCliente acesso ");
        sql.append("inner join Cliente cli on  acesso.cliente = cli.codigo ");
        sql.append("WHERE CAST(acesso.dtHrEntrada AS date) = '"+data+"' ");
        sql.append("and cli.codigoMatricula = "+matricula+" order  by acesso.dtHrSaida desc");

        Query query = getCurrentSession().createQuery(sql.toString());

        if (query.getResultList().size() > 0) {
            return (List<AcessoCliente>) query.getResultList();
        } else {
            return null;
        }
    }

    public List<AcessoCliente> findAllByPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception {
        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.cliente.pessoa.codigo = :codPessoa\n");
        params.put("codPessoa", codPessoa);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("dtHrEntrada")) {
                    where.append(" order by obj.dtHrEntrada " + sortOrder);
                } else if(sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + sortOrder);
                } else if(sortField.equalsIgnoreCase("dtHrSaida")) {
                    where.append(" order by obj.dtHrSaida " + sortOrder);
                }
            } else {
                where.append(" order by obj.dtHrEntrada desc");
            }
        } else {
            where.append(" order by obj.dtHrEntrada desc");
        }

        return findByParam(where, params, maxResults, indiceInicial);
    }
}
