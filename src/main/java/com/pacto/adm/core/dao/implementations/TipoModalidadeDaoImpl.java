package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.adapters.TipoModalidadeAdapter;
import com.pacto.adm.core.dao.interfaces.TipoModalidadeDao;
import com.pacto.adm.core.dto.filtros.FiltroTipoModalidadeJSON;
import com.pacto.adm.core.entities.Classificacao;
import com.pacto.adm.core.entities.contrato.TipoModalidade;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.utils.Uteis;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.Map;
import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class TipoModalidadeDaoImpl extends DaoGenericoImpl<TipoModalidade, Integer> implements TipoModalidadeDao {

    private final EntityManager entityManager;

    private static final int MAXIMO_RESULTADOS = 10;

    public TipoModalidadeDaoImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }


    @Override
    public List<TipoModalidade> findAll(FiltroTipoModalidadeJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append(" WHERE 1 = 1");

        if (Uteis.notNullAndNotEmpty(filtros.getParametro())) {
            where.append(" AND upper(obj.nome) like CONCAT('%',:nome,'%')\n");
            params.put("nome", filtros.getParametro());

            if (filtros.getParametro().matches("\\d+")) {
                where.append(" OR obj.codigo = :codigo\n");
                params.put("codigo", Integer.parseInt(filtros.getParametro()));

                where.append(" OR obj.identificador = :identificador\n");
                params.put("identificador", Integer.parseInt(filtros.getParametro()));
            }
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if(sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + sortOrder);
                } else if(sortField.equalsIgnoreCase("nome")) {
                    where.append(" order by obj.nome " + sortOrder);
                } else if(sortField.equalsIgnoreCase("nrVezes")) {
                    where.append(" order by obj.identificador " + sortOrder);
                }
            } else {
                where.append(" order by obj.codigo desc");
            }
        } else {
            where.append(" order by obj.codigo desc");
        }

        return findByParam(where, params, maxResults, indiceInicial);
    }

    @Override
    public TipoModalidade findByIdentificador(Integer identificador) throws Exception {
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append(" SELECT tp FROM TipoModalidade tp WHERE tp.identificador = :identificador ");
        params.put("identificador", identificador);
        Query query = getCurrentSession().createQuery(where.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        if (query.getResultList().size() > 0) {
            return (TipoModalidade) query.getResultList().get(0);
        } else {
            return null;
        }

    }
}
