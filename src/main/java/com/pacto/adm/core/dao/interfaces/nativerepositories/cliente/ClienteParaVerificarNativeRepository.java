package com.pacto.adm.core.dao.interfaces.nativerepositories.cliente;

import com.pacto.adm.core.dto.cliente.ClienteParaVerificarDTO;
import com.pacto.adm.core.dto.filtros.bi.FiltroBIDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface ClienteParaVerificarNativeRepository {
    List<ClienteParaVerificarDTO> consultarClientesParaVerificar(FiltroBIDTO filtroBIDTO, PaginadorDTO paginadorDTO, Boolean verificado) throws Exception;
}
