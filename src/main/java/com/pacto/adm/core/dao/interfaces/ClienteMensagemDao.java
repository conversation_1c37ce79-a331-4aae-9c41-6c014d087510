package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.dto.filtros.FiltroClienteMensagemJSON;
import com.pacto.adm.core.entities.ClienteMensagem;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;

public interface ClienteMensagemDao extends DaoGenerico<ClienteMensagem, Integer> {

    ClienteMensagem consultarObjClienteMensagemPorProdutoVencido(Integer cliente, Integer produto) throws Exception;

    List<ClienteMensagem> findAllByPessoa(Integer codPessoa, FiltroClienteMensagemJSON filtroJSON, PaginadorDTO paginadorDTO) throws Exception;

}
