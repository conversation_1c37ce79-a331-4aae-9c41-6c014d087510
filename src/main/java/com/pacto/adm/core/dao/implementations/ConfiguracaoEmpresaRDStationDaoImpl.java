package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ConfiguracaoEmpresaRDStationDao;
import com.pacto.adm.core.entities.ConfiguracaoEmpresaRDStation;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ConfiguracaoEmpresaRDStationDaoImpl extends  DaoGenericoImpl<ConfiguracaoEmpresaRDStation, Integer> implements ConfiguracaoEmpresaRDStationDao {

    @Override
    public ConfiguracaoEmpresaRDStation findByEmpresaId(Integer empresaId) {
        getCurrentSession().clear();

        Map<String, Object> params = new HashMap<>();
        StringBuilder s = new StringBuilder("SELECT obj FROM ConfiguracaoEmpresaRDStation obj ");
        s.append(" WHERE obj.empresa.codigo = :empresa ");
        params.put("empresa", empresaId);
        Query query = getCurrentSession().createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        ConfiguracaoEmpresaRDStation configuracaoEmpresaRDStation = new ConfiguracaoEmpresaRDStation();
        try {
            configuracaoEmpresaRDStation = (ConfiguracaoEmpresaRDStation) query.getResultList().get(0);
        } catch (Exception e) {
        }
        return configuracaoEmpresaRDStation;
    }
}
