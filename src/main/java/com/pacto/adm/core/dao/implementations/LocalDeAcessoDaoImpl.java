package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.LocalDeAcessoDao;
import com.pacto.adm.core.entities.LocalAcesso;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class LocalDeAcessoDaoImpl extends DaoGenericoImpl<LocalAcesso, Integer> implements LocalDeAcessoDao {

    @Override
    public List<LocalAcesso> findByEmpresa(Integer codigoEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT local FROM LocalAcesso local WHERE local.empresa = "+codigoEmpresa);

        Query query = getCurrentSession().createQuery(sql.toString());

        if (query.getResultList().size() > 0) {
            return (List<LocalAcesso>) query.getResultList();
        } else {
            return null;
        }
    }
}
