package com.pacto.adm.core.dao.interfaces.nativerepositories.movproduto;

import com.pacto.adm.core.dto.AplicacaoDescontoTotaisDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.config.dto.PaginadorDTO;

public interface MovProdutoNativeRepository {
    AplicacaoDescontoTotaisDTO findAllAplicacaoDesconto(FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON, PaginadorDTO paginadorDTO) throws Exception;
}
