package com.pacto.adm.core.dao.interfaces.questionario.questionarioCliente;

import com.pacto.adm.core.entities.QuestionarioCliente;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface QuestionarioClienteRepository extends JpaRepository<QuestionarioCliente, Integer> {

    @Query(
            "SELECT qc FROM QuestionarioCliente qc\n" +
                    "INNER JOIN qc.questionario q\n" +
                    "WHERE q.tipoQuestionario = :tipoQuestionario\n" +
                    "AND qc.cliente = :codCliente\n" +
                    "ORDER BY qc.data DESC"
    )
    @Transactional(readOnly = true)
    Page<QuestionarioCliente> findByClienteAndTipoQuestionario(
            @Param("codCliente") Integer codCliente,
            @Param("tipoQuestionario") String tipoQuestionario,
            Pageable pageable
    );
}
