package com.pacto.adm.core.dao.interfaces;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroIntegracaoSesiJSON;
import com.pacto.adm.core.entities.sesi.IntegracaoSesi;

import java.util.List;

public interface IntegracaoSesiDao extends DaoGenerico<IntegracaoSesi, Integer> {

    List<IntegracaoSesi> findAll(FiltroIntegracaoSesiJSON filtros, PaginadorDTO paginadorDTO) throws Exception;

}
