package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.dto.PessoaDTO;
import com.pacto.adm.core.entities.Pessoa;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;

public interface PessoaDao extends DaoGenerico<Pessoa, Integer> {

    boolean salvaSenhaDeAcesso(Pessoa pessoa, String senhaAcesso, Boolean habilitaSenhaAcesso);

    List<Pessoa> findByNome(String nome, Integer matricula, PaginadorDTO paginadorDTO) throws Exception;

    List<PessoaDTO> consultaColaboradorOuCliente(String nome, Integer matricula, boolean ultimosAcessos) throws Exception;


    Pessoa findByCliente(Integer codigoCliente) throws Exception;

}
