package com.pacto.adm.core.dao.interfaces.nativerepositories.colaborador;

import com.pacto.adm.core.dto.colaborador.ColaboradorAniversarianteDTO;
import com.pacto.adm.core.dto.filtros.bi.FiltroBIDTO;
import com.pacto.config.dto.PaginadorDTO;

import java.util.List;

public interface ColaboradorNativeRepository {

    List<ColaboradorAniversarianteDTO> colaboradorAniversariante(FiltroBIDTO filtroBIDTO, PaginadorDTO paginadorDTO) throws Exception;

}
