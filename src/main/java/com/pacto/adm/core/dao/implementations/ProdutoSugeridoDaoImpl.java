package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ProdutoSugeridoDao;
import com.pacto.adm.core.entities.ProdutoSugerido;
import com.pacto.adm.core.entities.contrato.Contrato;
import com.pacto.config.dto.PaginadorDTO;
import org.springframework.stereotype.Repository;


import javax.persistence.Query;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ProdutoSugeridoDaoImpl extends DaoGenericoImpl<ProdutoSugerido, Integer> implements ProdutoSugeridoDao {

    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<ProdutoSugerido> findAllByCodModalidade(Integer codModalidade) throws Exception {

        getCurrentSession().clear();
        StringBuilder hql = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        hql.append("SELECT ps FROM ProdutoSugerido ps ");
        hql.append(" WHERE ps.modalidade.codigo = :codModalidade");
        params.put("codModalidade", codModalidade);

        Query query = getCurrentSession().createQuery(hql.toString());
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            query.setParameter(entry.getKey(), entry.getValue());
        }
        return query.getResultList();
    }

    @Override
    public List<ProdutoSugerido> findAllByCodModalidadePaginado(Integer codModalidade, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append(" WHERE obj.modalidade.codigo = :codModalidade");
        params.put("codModalidade", codModalidade);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
        }
        where.append(" order by obj.codigo desc");

        return findByParam(where, params, maxResults, indiceInicial);
    }
}
