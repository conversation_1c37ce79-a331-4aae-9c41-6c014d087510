package com.pacto.adm.core.dao.interfaces;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.MovPagamento;

import java.util.List;

public interface MovPagamentoDao extends DaoGenerico<MovPagamento, Integer> {

    List<MovPagamento> findAllByPessoa(Integer codPessoa, List<Integer> codigos, PaginadorDTO paginadorDTO) throws Exception;
    List<MovPagamento> findAllByContrato(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception;

    List<MovPagamento> findAllByRecibo(Integer codRecibo) throws Exception;
}
