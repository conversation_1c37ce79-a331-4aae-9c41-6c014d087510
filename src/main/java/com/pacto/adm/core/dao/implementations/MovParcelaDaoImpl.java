package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.MovParcelaDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroMovParcelaJSON;
import com.pacto.adm.core.entities.MovParcela;
import com.pacto.adm.core.enumerador.SituacaoRemessaEnum;
import com.pacto.adm.core.enumerador.TipoRemessaEnum;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class MovParcelaDaoImpl extends DaoGenericoImpl<MovParcela, Integer> implements MovParcelaDao {
    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<MovParcela> findAllByPessoa(Integer codPessoa) throws Exception {
        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.pessoa.codigo = :codPessoa\n");
        params.put("codPessoa", codPessoa);

        return findByParam(where, params);
    }

    public List<MovParcela> fidAllByCodigoContrato(Integer codContrato, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.contrato = :codContrato\n");
        params.put("codContrato", codContrato);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + sortOrder);
                } else if(sortField.equalsIgnoreCase("contrato")) {
                    where.append(" order by obj.contrato.codigo " + sortOrder);
                } else if(sortField.equalsIgnoreCase("empresa")) {
                    where.append(" order by obj.empresa.nome " + sortOrder);
                } else if(sortField.equalsIgnoreCase("descricao")) {
                    where.append(" order by obj.descricao " + sortOrder);
                } else if(sortField.equalsIgnoreCase("valorParcela")) {
                    where.append(" order by obj.valorParcela " + sortOrder);
                } else if(sortField.equalsIgnoreCase("situacao")) {
                    where.append(" order by obj.situacao " + sortOrder);
                }
            } else {
                where.append(" order by obj.dataLancamento desc");
            }
        } else {
            where.append(" order by obj.dataLancamento desc");
        }

        return findByParam(where, params, maxResults, indiceInicial);
    }

    @Override
    public MovParcela consultarPorMovProduto(Integer codigoMovProduto) throws Exception {
        getCurrentSession().clear();

        StringBuilder hql = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        hql.append(" SELECT obj FROM MovParcela obj ");
        hql.append(" INNER JOIN MovProdutoParcela mpp ON mpp.movParcela.codigo = obj.codigo \n");
        hql.append(" WHERE (mpp.movProduto.produto.tipoProduto <> 'DE' or mpp.movProduto.produto.tipoProduto <> 'DV' or mpp.movProduto.produto.tipoProduto <> 'QU') \n");
        hql.append(" AND mpp.movProduto.codigo = :codigoMovProduto \n");
        params.put("codigoMovProduto", codigoMovProduto);

        Query query = getCurrentSession().createQuery(hql.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        if (query.getResultList().size() > 0) {
            return (MovParcela) query.getResultList().get(0);
        } else {
            return null;
        }
    }

    @Override
    public MovParcela consultarPorCodigoVendaAvulsa(Integer codigoVendaAvulsa, String pg) throws Exception {
        getCurrentSession().clear();

        StringBuilder hql = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        hql.append(" SELECT obj FROM MovParcela obj ");
        hql.append(" WHERE obj.vendaAvulsa.codigo = :codigoVendaAvulsa ");
        hql.append(" ORDER BY  obj.dataLancamento ");

        params.put("codigoVendaAvulsa", codigoVendaAvulsa);

        Query query = getCurrentSession().createQuery(hql.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        if (query.getResultList().size() > 0) {
            return (MovParcela) query.getResultList().get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<MovParcela> consultarPorCodigoPessoa(Integer codPessoa, Integer matricula,
                                                     FiltroMovParcelaJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        return consultarPorCodigoPessoa(codPessoa, matricula, filtros, paginadorDTO, true);
    }


    @Override
    public List<MovParcela> consultarPorCodigoPessoa(Integer codPessoa, Integer matricula,
                                                     FiltroMovParcelaJSON filtros, PaginadorDTO paginadorDTO, boolean definirMaxResult) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder hqlSelect = new StringBuilder();
        StringBuilder hqlWhere = new StringBuilder();
        StringBuilder hqlFinal = new StringBuilder();

        Map<String, Object> params = new HashMap<>();

        hqlSelect.append("SELECT distinct obj, emp FROM MovParcela obj  \n");
        hqlWhere.append("LEFT JOIN MovProdutoParcela mpp ON mpp.movParcela = obj.codigo \n");
        hqlWhere.append("INNER JOIN obj.empresa emp \n");

        if (!UteisValidacao.emptyNumber(codPessoa)) {
            hqlWhere.append("WHERE obj.pessoa.codigo = :codPessoa");
            params.put("codPessoa", codPessoa);
        } if (!UteisValidacao.emptyNumber(matricula)) {
            hqlWhere.append("LEFT JOIN Cliente cl ON cl.pessoa.codigo = obj.pessoa.codigo \n");
            hqlWhere.append("WHERE cl.codigoMatricula = :codigoMatricula");
            params.put("codigoMatricula", matricula);
        }

        // nao retornar parcelas de Multa/Juros.
        if (filtros.isIgnorarParcelasMultaJuros()) {
            hqlWhere.append(" AND (COALESCE(mpp.movProduto.movProdutoOriginal, 0) = 0) \n");
        }

        if (filtros.getSituacao() != null && !filtros.getSituacao().isEmpty()) {
            if (filtros.getSituacao().equalsIgnoreCase("atraso")) {
                hqlWhere.append(" AND obj.situacao = :situacao");
                params.put("situacao", "EA");
                hqlWhere.append(" AND obj.dataVencimento < CURRENT_DATE ");
            } else {
                hqlWhere.append(" AND obj.situacao = :situacao");
                params.put("situacao", filtros.getSituacao());
            }
        }

        if (filtros.isIgnorarEmRemessa()) {
            hqlWhere.append(" AND NOT EXISTS( ");
            hqlWhere.append("SELECT ri.codigo from RemessaItem ri ");
            hqlWhere.append("INNER JOIN Remessa re on ri.remessa = re.codigo ");
            hqlWhere.append("INNER JOIN RemessaItemMovparcela  rmov on  ri.codigo = rmov.remessaItem where ");
            hqlWhere.append(" (ri.movParcela = obj.codigo  or ");
            hqlWhere.append(" (obj.codigo = rmov.movParcela AND ri.tipo IN (").append(TipoRemessaEnum.BOLETO.getId()).append(",").append(TipoRemessaEnum.ITAU_BOLETO.getId()).append(")))\n");
            hqlWhere.append(" AND  re.situacaoRemessa IN (" + SituacaoRemessaEnum.GERADA.getId() + "," + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + ")) \n");
        }

        hqlFinal.append(hqlSelect);
        hqlFinal.append(hqlWhere);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("distinct obj.codigo", hqlWhere, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sort = paginadorDTO.getSort().split(",")[0].trim();
                String orderDirection = paginadorDTO.getSort().split(",")[1];

                if (sort.equalsIgnoreCase("codigo")) {
                    hqlFinal.append(" ORDER BY obj.codigo ").append(orderDirection);
                } else if (sort.equalsIgnoreCase("contrato")) {
                    hqlFinal.append(" ORDER BY obj.contrato ").append(orderDirection);
                } else if (sort.equalsIgnoreCase("descricao")) {
                    hqlFinal.append(" ORDER BY obj.descricao ").append(orderDirection);
                } else if (sort.equalsIgnoreCase("dataVencimento")) {
                    hqlFinal.append(" ORDER BY obj.dataVencimento ").append(orderDirection);
                } else if (sort.equalsIgnoreCase("valor")) {
                    hqlFinal.append(" ORDER BY obj.valorParcela ").append(orderDirection);
                } else if (sort.equalsIgnoreCase("situacao")) {
                    hqlFinal.append(" ORDER BY obj.situacao ").append(orderDirection);
                } else if (sort.equalsIgnoreCase("empresa")) {
                    hqlFinal.append(" ORDER BY emp.nome ").append(orderDirection);
                }
            } else {
                hqlFinal.append(" ORDER BY obj.codigo DESC ");
            }
        } else {
            hqlFinal.append(" ORDER BY obj.codigo DESC ");
        }

        Query q = getCurrentSession().createQuery(hqlFinal.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                q.setParameter(p, params.get(p));
            }
        }

        if (maxResults != 0 && definirMaxResult) {
            q.setMaxResults(maxResults);
        }
        if (indiceInicial != 0) {
            q.setFirstResult(indiceInicial);
        }

        List<Object[]> result = q.getResultList();
        List<MovParcela> movParcelas = new ArrayList<>();
        for (Object[] obj : result) {
            movParcelas.add((MovParcela) obj[0]);
        }

        return movParcelas;
    }

}
