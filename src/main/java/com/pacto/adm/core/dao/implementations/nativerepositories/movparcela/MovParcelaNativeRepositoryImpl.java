package com.pacto.adm.core.dao.implementations.nativerepositories.movparcela;

import com.pacto.adm.core.dao.interfaces.nativerepositories.movparcela.MovParcelaNativeRepository;
import com.pacto.adm.core.dto.ClienteDTO;
import com.pacto.adm.core.dto.PessoaDTO;
import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import com.pacto.adm.core.dto.filtros.bi.FiltroBIDTO;
import com.pacto.adm.core.dto.movparcela.ParcelaEmAbertoColaboradorDTO;
import com.pacto.adm.core.dto.movparcela.ParcelaEmAbertoDTO;
import com.pacto.adm.core.dto.movparcela.ParcelaEmAtrasoDTO;
import com.pacto.adm.core.util.QueryUtils;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.Tuple;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class MovParcelaNativeRepositoryImpl implements MovParcelaNativeRepository {

    @PersistenceContext(unitName = "zwClientPU")
    private EntityManager entityManager;

    @Override
    public List<ParcelaEmAtrasoDTO> consultarPendenciaParcelaEmAbertoAtraso(FiltroBIDTO filtroBIDTO, PaginadorDTO paginadorDTO) throws Exception {

        if (filtroBIDTO.getFim() == null) {
            throw new Exception("Deve ser informado o filtro da data final!");
        }

        String sql = sqlPendenciaParcelaEmAbertoAtraso(filtroBIDTO, paginadorDTO);

        Query query = entityManager.createNativeQuery(sql, Tuple.class);
        setParametersPendenciaParcelaEmAbertoAtraso(query, filtroBIDTO);
        QueryUtils.setQueryPagination(query, paginadorDTO);

        List<Tuple> resultado = query.getResultList();

        List<ParcelaEmAtrasoDTO> aniversariantes = resultado.stream().map(
                tuple -> new ParcelaEmAtrasoDTO(
                        new ClienteDTO(
                                tuple.get("cli", Integer.class),
                                tuple.get("matriculaCli", Integer.class),
                                tuple.get("situacaoCliente", String.class),
                                tuple.get("telefonescliente", String.class),
                                new PessoaDTO(
                                        tuple.get("codigopessoa", Integer.class),
                                        tuple.get("nome", String.class),
                                        tuple.get("cpf", String.class)
                                )
                        ),
                        tuple.get("nomePlano", String.class),
                        tuple.get("codContrato", Integer.class),
                        tuple.get("dataInicio", Date.class),
                        tuple.get("dataFim", Date.class),
                        tuple.get("duracaoContrato", Integer.class),
                        tuple.get("nomemodalidades", String.class),
                        tuple.get("qtdParcelas", BigInteger.class),
                        BigDecimal.valueOf(tuple.get("valorEmAberto", Float.class))
                )
        ).collect(Collectors.toList());

        if (paginadorDTO != null) {
            Query countQuery = entityManager.createNativeQuery("select count(sqlCount.*) from (" + sql + ") as sqlCount");
            setParametersPendenciaParcelaEmAbertoAtraso(countQuery, filtroBIDTO);
            paginadorDTO.setQuantidadeTotalElementos(((BigInteger) countQuery.getSingleResult()).longValue());
        }

        return aniversariantes;
    }

    private String sqlPendenciaParcelaEmAbertoAtraso(FiltroBIDTO filtroBIDTO, PaginadorDTO paginadorDTO) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT cli, codigopessoa, nome, nomePlano, codPessoa, matriculaCli, situacaoCliente, codContrato, dataInicio, \n");
        sql.append(" dataFim, duracaoContrato, nomemodalidades, telefonescliente, cpf,count(1) AS qtdParcelas,sum(valorparcela) valorEmAberto FROM  (\n");
        sql.append("SELECT dw.codigocliente AS cli, dw.codigopessoa,movparcela.valorparcela,\n");
        sql.append("dw.nomecliente as nome ,pl.descricao as nomePlano,dw.codigopessoa as codPessoa, dw.matricula as matriculaCli,dw.situacao as situacaoCliente,movparcela.contrato as codContrato,\n");
        sql.append("dw.datavigenciade as dataInicio,dw.datavigenciaateajustada as dataFim,cd.numeroMeses as duracaoContrato,ct.nomemodalidades,dw.telefonescliente,dw.cpf, movparcela.codigo\n");
        sql.append(" FROM movparcela\n");
        sql.append(" INNER JOIN Situacaoclientesinteticodw dw on dw.codigopessoa = movparcela.pessoa\n");

        if (!CollectionUtils.isEmpty(filtroBIDTO.getColaboradores())) {
            sql.append("INNER JOIN vinculo ON vinculo.cliente = dw.codigocliente\n");
        }
        sql.append(" LEFT JOIN Contrato ct ON  ct.codigo = movparcela.contrato\n");
        sql.append("LEFT JOIN ContratoDuracao cd ON cd.contrato = ct.codigo\n");
        sql.append("Left Join contratocondicaopagamento ccp on ccp.contrato = ct.codigo\n");
        sql.append("Left join condicaopagamento cp on cp.codigo = ccp.condicaopagamento\n");
        sql.append("LEFT JOIN Plano pl ON pl.codigo = ct.plano\n");
        sql.append("left join vendaavulsa venda on venda.codigo = movparcela.vendaavulsa\n");
        sql.append("WHERE movparcela.situacao = 'EA' ");
        sql.append("AND NOT movparcela.regimerecorrencia ");
        sql.append("AND NOT movparcela.parcelaDCC ");
        sql.append("and (coalesce(cp.tipoconveniocobranca, 0) = 0 )\n");
        sql.append(" and (movparcela.contrato is not null or movparcela.aulaavulsadiaria is not null or  venda.tipocomprador <> 'CO') ");
        sql.append("AND movparcela.datavencimento < :dataVencimento ");

        if (!UteisValidacao.emptyNumber(filtroBIDTO.getEmpresa())) {
            sql.append("AND movparcela.empresa = :codigoEmpresa\n");
        }

        if (!CollectionUtils.isEmpty(filtroBIDTO.getColaboradores())) {
            sql.append("AND v.colaborador IN :colaboradores\n");
        }
        if (filtroBIDTO.getInicio() != null) {
            StringBuilder subSQL = new StringBuilder();
            subSQL.append(" AND EXISTS(");
            subSQL.append("SELECT cli.codigo FROM cliente cli ");
            subSQL.append("INNER JOIN pessoa pes ON pes.codigo = cli.pessoa ");
            subSQL.append("WHERE cli.codigo = dw.codigocliente AND movparcela.datavencimento >= :dataVencimentoZerada");
            subSQL.append(") ");
            sql.append(subSQL);
        }

        if (StringUtils.hasText(filtroBIDTO.getQuickSearchValue())) {
            sql.append("AND ");
            QueryUtils.buildSqlQuickSearchByType(sql, new LinkedHashMap<String, Class<?>>() {{
                put("dw.nomecliente", null);
                put("dw.matricula", Integer.class);
                put("dw.situacao", null);
                put("dw.cpf", null);
                put("movparcela.contrato", Integer.class);
                put("pl.descricao", null);
                put("cd.numeroMeses", Integer.class);
                put("dw.telefonescliente", null);
            }});
        }

        sql.append(" GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16\n");
        sql.append(") consulta GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14");

        String sortField = "nome";
        String sortOrder = "ASC";
        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(3L);
            }

            if (paginadorDTO.getSort() != null) {
                sortField = paginadorDTO.getSort().split(",")[0];
                sortOrder = paginadorDTO.getSort().split(",")[1];

                if (sortField.equals("matricula")) {
                    sortField = "matriculaCli";
                }

            }

        }
        sql.append("ORDER BY ").append(sortField).append(" ").append(sortOrder).append("\n");

        return sql.toString();
    }

    private void setParametersPendenciaParcelaEmAbertoAtraso(Query query, FiltroBIDTO filtroBIDTO) throws Exception {
        query.setParameter("dataVencimento", Uteis.getDataJDBC(filtroBIDTO.getFim()));

        if (filtroBIDTO.getInicio() != null) {
            try {
                query.setParameter("dataVencimentoZerada", Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(filtroBIDTO.getInicio()), "00:00:00"));
            } catch (Exception e) {
                throw new Exception("Erro ao converter as datas!");
            }
        }

        if (!UteisValidacao.emptyNumber(filtroBIDTO.getEmpresa())) {
            query.setParameter("codigoEmpresa", filtroBIDTO.getEmpresa());
        }

        if (!CollectionUtils.isEmpty(filtroBIDTO.getColaboradores())) {
            query.setParameter("colaboradores", filtroBIDTO.getColaboradores());
        }

        if (StringUtils.hasText(filtroBIDTO.getQuickSearchValue())) {
            QueryUtils.addQuickSearchParams(filtroBIDTO.getQuickSearchValue(), query);
        }
    }

    @Override
    public List<ParcelaEmAbertoDTO> consultarPendenciaParcelaEmAbertoAPagar(FiltroBIDTO filtroBIDTO, PaginadorDTO paginadorDTO) throws Exception {

        String sql = sqlPendenciaParcelaEmAbertoAPagar(filtroBIDTO, paginadorDTO);

        Query query = entityManager.createNativeQuery(sql, Tuple.class);
        setParametersPendenciaParcelaEmAbertoAPagar(query, filtroBIDTO);
        QueryUtils.setQueryPagination(query, paginadorDTO);

        List<Tuple> resultado = query.getResultList();

        List<ParcelaEmAbertoDTO> aniversariantes = resultado.stream().map(
                tuple -> new ParcelaEmAbertoDTO(
                        new ClienteDTO(
                                tuple.get("cli", Integer.class),
                                tuple.get("matriculaCli", Integer.class),
                                tuple.get("situacaoCliente", String.class),
                                tuple.get("telefonescliente", String.class),
                                new PessoaDTO(
                                        tuple.get("codigopessoa", Integer.class),
                                        tuple.get("nome", String.class),
                                        tuple.get("cpf", String.class)
                                )
                        ),
                        tuple.get("nomePlano", String.class),
                        tuple.get("codContrato", Integer.class),
                        tuple.get("dataInicio", Date.class),
                        tuple.get("dataFim", Date.class),
                        tuple.get("duracaoContrato", Integer.class),
                        tuple.get("nomemodalidades", String.class),
                        BigDecimal.valueOf(tuple.get("valorEmAberto", Float.class))
                )
        ).collect(Collectors.toList());

        if (paginadorDTO != null) {
            Query countQuery = entityManager.createNativeQuery("select count(sqlCount.*) from (" + sql + ") as sqlCount");
            setParametersPendenciaParcelaEmAbertoAPagar(countQuery, filtroBIDTO);
            paginadorDTO.setQuantidadeTotalElementos(((BigInteger) countQuery.getSingleResult()).longValue());
        }

        return aniversariantes;
    }

    private String sqlPendenciaParcelaEmAbertoAPagar(FiltroBIDTO filtroBIDTO, PaginadorDTO paginadorDTO) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM  (");
        sql.append("SELECT dw.codigocliente AS cli, dw.codigopessoa,sum(movparcela.valorparcela) as valorEmAberto,\n");
        sql.append("dw.nomecliente as nome,pl.descricao as nomePlano,dw.codigopessoa as codPessoa,dw.matricula as matriculaCli,dw.situacao as situacaoCliente,movparcela.contrato as codContrato,\n");
        sql.append("dw.datavigenciade as dataInicio,dw.datavigenciaateajustada as dataFim,cd.numeroMeses as duracaoContrato,ct.nomemodalidades,dw.telefonescliente,dw.cpf\n");
        sql.append(" FROM movparcela\n");
        sql.append(" INNER JOIN Situacaoclientesinteticodw dw on dw.codigopessoa = movparcela.pessoa\n");

        if (!CollectionUtils.isEmpty(filtroBIDTO.getColaboradores())) {
            sql.append("INNER JOIN vinculo ON vinculo.cliente = dw.codigocliente\n");
        }
        sql.append(" LEFT JOIN Contrato ct ON  ct.codigo = movparcela.contrato\n");
        sql.append("LEFT JOIN ContratoDuracao cd ON cd.contrato = ct.codigo\n");
        sql.append(" Left Join contratocondicaopagamento ccp on ccp.contrato = movparcela.contrato\n");
        sql.append(" Left join condicaopagamento cp on cp.codigo = ccp.condicaopagamento\n");
        sql.append("left join vendaavulsa venda on venda.codigo = movparcela.vendaavulsa\n");
        sql.append("LEFT JOIN Plano pl ON pl.codigo = ct.plano\n");
        sql.append("WHERE movparcela.situacao = 'EA' ");
        sql.append("AND NOT movparcela.regimerecorrencia ");
        sql.append("AND NOT movparcela.parcelaDCC ");
        sql.append(" and coalesce(cp.tipoconveniocobranca, 0) = 0 ");
        sql.append(" and (movparcela.contrato is not null or movparcela.aulaavulsadiaria is not null or  venda.tipocomprador <> 'CO') ");

        if (!UteisValidacao.emptyNumber(filtroBIDTO.getEmpresa())) {
            sql.append("AND movparcela.empresa = :codigoEmpresa\n");
        }

        if (!CollectionUtils.isEmpty(filtroBIDTO.getColaboradores())) {
            sql.append("AND vinculo.colaborador IN :colaboradores\n");
        }

        sql.append("AND movparcela.datavencimento\\:\\:DATE >= :dataVencimento\\:\\:DATE ");

        if (StringUtils.hasText(filtroBIDTO.getQuickSearchValue())) {
            sql.append("AND ");
            QueryUtils.buildSqlQuickSearchByType(sql, new LinkedHashMap<String, Class<?>>() {{
                put("dw.nomecliente", null);
                put("dw.matricula", Integer.class);
                put("dw.situacao", null);
                put("dw.cpf", null);
                put("movparcela.contrato", Integer.class);
                put("pl.descricao", null);
                put("dw.telefonescliente", null);
            }});
        }

        sql.append(" GROUP BY pl.descricao,cli,matriculaCli,dw.nomePlano,codPessoa, codContrato,situacaoCliente, nome,dataInicio,dataFim,duracaoContrato,ct.nomemodalidades,dw.telefonescliente,dw.cpf");
        sql.append(") consulta ");

        String sortField = "nome";
        String sortOrder = "ASC";
        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(3L);
            }

            if (paginadorDTO.getSort() != null) {
                sortField = paginadorDTO.getSort().split(",")[0];
                sortOrder = paginadorDTO.getSort().split(",")[1];

                if (sortField.equals("matricula")) {
                    sortField = "matriculaCli";
                }

            }

        }
        sql.append("ORDER BY ").append(sortField).append(" ").append(sortOrder).append("\n");

        return sql.toString();
    }

    private void setParametersPendenciaParcelaEmAbertoAPagar(Query query, FiltroBIDTO filtroBIDTO) throws Exception {

        query.setParameter("dataVencimento", Uteis.getData(Calendario.hoje()));

        if (!UteisValidacao.emptyNumber(filtroBIDTO.getEmpresa())) {
            query.setParameter("codigoEmpresa", filtroBIDTO.getEmpresa());
        }

        if (!CollectionUtils.isEmpty(filtroBIDTO.getColaboradores())) {
            query.setParameter("colaboradores", filtroBIDTO.getColaboradores());
        }

        if (StringUtils.hasText(filtroBIDTO.getQuickSearchValue())) {
            QueryUtils.addQuickSearchParams(filtroBIDTO.getQuickSearchValue(), query);
        }
    }

    @Override
    public List<ParcelaEmAbertoColaboradorDTO> consultarPendenciaParcelaEmAbertoAPagarColaborador(FiltroBIDTO filtroBIDTO, PaginadorDTO paginadorDTO) throws Exception {

        String sql = sqlPendenciaParcelaEmAbertoAPagarColaborador(filtroBIDTO, paginadorDTO);

        Query query = entityManager.createNativeQuery(sql, Tuple.class);
        setParametersPendenciaParcelaEmAbertoAPagarColaborador(query, filtroBIDTO);
        QueryUtils.setQueryPagination(query, paginadorDTO);

        List<Tuple> resultado = query.getResultList();

        List<ParcelaEmAbertoColaboradorDTO> aniversariantes = resultado.stream().map(
                tuple -> new ParcelaEmAbertoColaboradorDTO(
                        new ColaboradorDTO(
                                tuple.get("codigoColaborador", Integer.class),
                                tuple.get("situacao", String.class),
                                new PessoaDTO(
                                        tuple.get("pessoa", Integer.class),
                                        tuple.get("nome", String.class),
                                        tuple.get("cpf", String.class)
                                )
                        )
                )
        ).collect(Collectors.toList());

        if (paginadorDTO != null) {
            Query countQuery = entityManager.createNativeQuery("select count(sqlCount.*) from (" + sql + ") as sqlCount");
            setParametersPendenciaParcelaEmAbertoAPagarColaborador(countQuery, filtroBIDTO);
            paginadorDTO.setQuantidadeTotalElementos(((BigInteger) countQuery.getSingleResult()).longValue());
        }

        return aniversariantes;
    }

    private String sqlPendenciaParcelaEmAbertoAPagarColaborador(FiltroBIDTO filtroBIDTO, PaginadorDTO paginadorDTO) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM  (");
        sql.append("SELECT DISTINCT col.codigo AS codigoColaborador, col.pessoa,\n");
        sql.append("p.nome as nome,col.situacao, p.cfp AS cpf\n");
        sql.append(" FROM movparcela\n");
        sql.append(" INNER JOIN Colaborador col ON col.pessoa = movparcela.pessoa\n");
        sql.append("LEFT JOIN Pessoa p ON col.pessoa = p.codigo\n");
        sql.append(" left join vendaavulsa venda on venda.codigo = movparcela.vendaavulsa \n");
        sql.append("WHERE movparcela.situacao = 'EA' ");
        sql.append("AND NOT movparcela.regimerecorrencia ");
        sql.append("and ((movparcela.contrato is null and movparcela.aulaavulsadiaria is null) or venda.tipocomprador = 'CO') ");

        if (!UteisValidacao.emptyNumber(filtroBIDTO.getEmpresa())) {
            sql.append("AND col.empresa = :codigoEmpresa\n");
        }

        if (!CollectionUtils.isEmpty(filtroBIDTO.getColaboradores())) {
            sql.append("AND vinculo.colaborador IN :colaboradores\n");
        }


        if (StringUtils.hasText(filtroBIDTO.getQuickSearchValue())) {
            sql.append("AND ");
            QueryUtils.buildSqlQuickSearchByType(sql, new LinkedHashMap<String, Class<?>>() {{
                put("p.nome", null);
                put("col.situacao", Integer.class);
                put("p.cfp", null);
            }});
        }

        sql.append(") consulta ");

        String sortField = "nome";
        String sortOrder = "ASC";
        if (paginadorDTO != null) {
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(3L);
            }

            if (paginadorDTO.getSort() != null) {
                sortField = paginadorDTO.getSort().split(",")[0];
                sortOrder = paginadorDTO.getSort().split(",")[1];

                if (sortField.equals("codigo")) {
                    sortField = "codigoColaborador";
                }

            }

        }
        sql.append("ORDER BY ").append(sortField).append(" ").append(sortOrder).append("\n");

        return sql.toString();
    }

    private void setParametersPendenciaParcelaEmAbertoAPagarColaborador(Query query, FiltroBIDTO filtroBIDTO) throws Exception {

        if (!UteisValidacao.emptyNumber(filtroBIDTO.getEmpresa())) {
            query.setParameter("codigoEmpresa", filtroBIDTO.getEmpresa());
        }

        if (!CollectionUtils.isEmpty(filtroBIDTO.getColaboradores())) {
            query.setParameter("colaboradores", filtroBIDTO.getColaboradores());
        }

        if (StringUtils.hasText(filtroBIDTO.getQuickSearchValue())) {
            QueryUtils.addQuickSearchParams(filtroBIDTO.getQuickSearchValue(), query);
        }
    }

}
