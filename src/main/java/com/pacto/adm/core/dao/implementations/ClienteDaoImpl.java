package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ClienteDao;
import com.pacto.adm.core.dto.ClienteDadosGymPassDTO;
import com.pacto.adm.core.dto.ClienteDadosPessoaisDTO;
import com.pacto.adm.core.dto.ConfiguracaoSorteioDTO;
import com.pacto.adm.core.dto.PlanoDTO;
import com.pacto.adm.core.dto.*;
import com.pacto.adm.core.dto.integracaomanychat.ClienteSimplificadoDTO;
import com.pacto.adm.core.dto.negociacao.ClienteNegociacaoDTO;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Produto;
import com.pacto.adm.core.enumerador.SituacaoClienteEnum;
import com.pacto.config.utils.UteisValidacao;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.atomic.AtomicBoolean;

@Repository
public class ClienteDaoImpl extends DaoGenericoImpl<Cliente, Integer> implements ClienteDao {

    private static final int MAXIMO_RESULTADOS = 50;
    @Value("${url.fotos.nuvem}")
    private String urlFotosNuvem;

    @Override
    public Cliente findByPessoa(Integer codigoPessoa) throws Exception {
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append(" SELECT cli FROM Cliente cli WHERE cli.pessoa.codigo = :codigoPessoa");
        params.put("codigoPessoa", codigoPessoa);

        Query query = getCurrentSession().createQuery(where.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }

        try {
            if (query.getResultList().size() > 0) {
                return (Cliente) query.getSingleResult();
            } else {
                return null;
            }
        }catch (Exception e){
            return (Cliente) query.getResultList().get(0);
        }
    }

    @Override
    public Cliente findByMatricula(Integer matricula) throws Exception {
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append(" SELECT cli FROM Cliente cli WHERE cli.codigoMatricula = :matricula");
        params.put("matricula", matricula);

        Query query = getCurrentSession().createQuery(where.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }

        if (query.getResultList().size() > 0) {
            return (Cliente) query.getResultList().get(0);
        } else {
            return null;
        }
    }


    @Override
    @Deprecated
    public void salvaFreePass(Cliente cliente, ClienteDadosGymPassDTO clienteDadosGymPassDTO) throws Exception {
        String sql = "UPDATE Cliente SET freePass = ? , responsavelFreePass = ? WHERE codigo = ?";

        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                    sqlAlterar.setInt(1, cliente.getFreepass().getCodigo());
                    sqlAlterar.setInt(2, cliente.getResponsavelfreepass());
                    sqlAlterar.setInt(3, cliente.getCodigo());

                    sqlAlterar.execute();
                } catch (SQLException e) {
                    throw new RuntimeException("Erro ao executar o SQL", e);
                } finally {
                    con.close();
                }
            });
        }
    }

    @Override
    public void salvaFreePass(Cliente cliente) throws Exception {
        String sql = "UPDATE Cliente SET freePass = ? , responsavelFreePass = ? WHERE codigo = ?";

        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                    sqlAlterar.setInt(1, cliente.getFreepass().getCodigo());
                    sqlAlterar.setInt(2, cliente.getResponsavelfreepass());
                    sqlAlterar.setInt(3, cliente.getCodigo());

                    sqlAlterar.execute();
                } catch (SQLException e) {
                    throw new RuntimeException("Erro ao executar o SQL", e);
                } finally {
                    con.close();
                }
            });
        }
    }

    @Override
    public void salvarGymPass(Integer matricula, ClienteDadosGymPassDTO clienteDadosGymPassDTO) throws Exception {
        Cliente cliente = findByMatricula(matricula);
        String sql;
        if (cliente != null) {
            sql = "UPDATE cliente SET gympasstypenumber = ?, gympassuniquetoken = ? WHERE codigomatricula = ?;";
        } else {
            throw new Exception("Nâo foi possível encontrar o cliente com a matrícula informada!");
        }
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try {
                    PreparedStatement sqlAlterar = con.prepareStatement(sql);
                    sqlAlterar.setString(1, clienteDadosGymPassDTO.getGymPassTypeNumber());
                    sqlAlterar.setString(2, clienteDadosGymPassDTO.getGymPassUniqueToken());
                    sqlAlterar.setInt(3, matricula);

                    sqlAlterar.execute();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    con.close();
                }
            });
        }
    }

    @Override
    public void deleteGymPass(Integer matricula) throws Exception {
        Cliente cliente = findByMatricula(matricula);
        String sql;
        if (cliente != null) {
            sql = "UPDATE cliente SET gympasstypenumber = '', gympassuniquetoken = null WHERE codigomatricula = ?;";
        } else {
            throw new Exception("Nâo foi possível encontrar o cliente com a matrícula informada!");
        }
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try {
                    PreparedStatement sqlAlterar = con.prepareStatement(sql);
                    sqlAlterar.setInt(1, matricula);

                    sqlAlterar.execute();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    con.close();
                }
            });
        }
    }

    @Override
    public void alterarParqCliente(Cliente cliente) {
        String sql = "UPDATE Cliente set parqpositivo=? WHERE codigo = ?";
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try {
                    PreparedStatement sqlAlterar = con.prepareStatement(sql);
                    sqlAlterar.setBoolean(1, cliente.isParqPositivo());
                    sqlAlterar.setInt(2, cliente.getCodigo());
                    sqlAlterar.execute();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    con.close();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Cliente sortearCliente(ConfiguracaoSorteioDTO configuracaoSorteioDTO) throws Exception {
        getCurrentSession().clear();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append(" LEFT JOIN obj.pessoa pes\n");
        where.append(" LEFT JOIN Contrato con ON con.pessoa = obj.pessoa.codigo \n");
        where.append(" WHERE obj.empresa.codigo = :codEmpresa\n");
        params.put("codEmpresa", configuracaoSorteioDTO.getEmpresa());

        if (!configuracaoSorteioDTO.getSituacoesCliente().isEmpty()) {
            where.append(" AND obj.situacao IN (:situacoes)\n");
            params.put("situacoes", configuracaoSorteioDTO.getSituacoesCliente());
        }

        if (!configuracaoSorteioDTO.getPlanos().isEmpty()) {
            List<Integer> codPlanos = new ArrayList<>();
            for (PlanoDTO pln : configuracaoSorteioDTO.getPlanos()) {
                try {
                    codPlanos.add(pln.getCodigo());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            where.append(" AND con.plano.codigo IN (:codPlanos)\n");
            params.put("codPlanos", codPlanos);

        }

        List<Cliente> clientes = findByParam(where, params);

        Random gerador = new Random();
        return findById(clientes.get(gerador.nextInt(clientes.size() + 1)).getCodigo());
    }

    @Override
    public void saveFreepass(Cliente cliente, Produto freepass, Integer usuarioResponsavelFreepass) throws Exception {
        cliente.setFreepass(freepass);
        cliente.setResponsavelfreepass(usuarioResponsavelFreepass);
        update(cliente);
    }

    @Override
    public List<Cliente> findByNome(String nome) throws Exception {
        if(nome == null){
            return new ArrayList<>();
        }
        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        if(nome != null){
            where.append(" WHERE LOWER(obj.pessoa.nome) LIKE '").append(nome.toLowerCase()).append("%'");
        }
        where.append(" order by obj.pessoa.nome ");
        return findByParam(where, params, maxResults, indiceInicial);
    }

    public Double valorParcelasAberto(Integer codigoCliente) throws ServiceException{
        try {
            final Double[] valor = new Double[1];
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        StringBuilder query = new StringBuilder();
                        query.append("select sum(valorparcela) from movparcela m ");
                        query.append("inner join cliente c on c.pessoa = m.pessoa ");
                        query.append("where m.situacao = 'EA' and datavencimento <= '");
                        query.append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("' ");
                        query.append("and c.codigo = ").append(codigoCliente);
                        PreparedStatement pst = connection.prepareStatement(query.toString());
                        ResultSet rs = pst.executeQuery();
                        if (rs.next()){
                            valor[0] = rs.getDouble(1);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
            return valor[0];
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }


    public List<String> telefoneCliente(Integer codigoCliente) throws ServiceException{
        try {
            List<String> telefoneCliente = new ArrayList<>();
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        StringBuilder query = new StringBuilder();
                        query.append(" select numero from telefone t \n");
                        query.append(" inner join cliente c on c.pessoa = t.pessoa \n");
                        query.append(" where c.codigo = ").append(codigoCliente);
                        query.append(" order by t.tipotelefone \n");
                        PreparedStatement pst = connection.prepareStatement(query.toString());
                        ResultSet rs = pst.executeQuery();
                        while (rs.next()){
                            telefoneCliente.add(rs.getString("numero"));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
            return telefoneCliente;
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    public List<ClienteNegociacaoDTO> consultarClientesSimplificado(String nome, Integer empresa) throws ServiceException {

        List<ClienteNegociacaoDTO> clientes = new ArrayList<>();
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        Integer matricula = null;
                        try {
                            matricula = Integer.parseInt(nome);
                        }catch (Exception e){}
                        StringBuilder query = new StringBuilder();
                        query.append(" select s.datanascimento, cpf, cli.empresa, codigocliente, cli.matricula, cli.situacao, ");
                        query.append(" s.datavigenciade, s.datavigenciaateajustada, c.nome as categoria, c.codigo as codigocategoria, ");
                        query.append(" p.fotokey, p.nome, p.codigo as pessoa, p.idvindi ");
                        query.append(" from situacaoclientesinteticodw s ");
                        query.append(" inner join cliente cli on cli.codigo = s.codigocliente ");
                        query.append(" inner join pessoa p on p.codigo = cli.pessoa ");
                        query.append(" left join categoria c on c.codigo = cli.categoria ");
                        if(matricula == null){
                            query.append(" where s.nomeconsulta like remove_acento_upper('%").append(nome).append("%') ");
                        } else {
                            query.append(" where cli.codigomatricula  = ").append(matricula);
                        }
                        query.append(" order by p.nome limit 100");
                        PreparedStatement pst = connection.prepareStatement(query.toString());
                        ResultSet rs = pst.executeQuery();
                        while (rs.next()){
                            ClienteNegociacaoDTO cliente = new ClienteNegociacaoDTO();
                            cliente.setCodigo(rs.getInt("codigocliente"));
                            cliente.setEmpresa(rs.getInt("empresa"));
                            cliente.setPessoa(rs.getInt("pessoa"));
                            cliente.setNome(rs.getString("nome").toLowerCase());
                            if (rs.getString("cpf") != null) {
                                cliente.setCpf(rs.getString("cpf").replaceAll("[.\\-]", ""));
                            }
                            String fotokey = rs.getString("fotokey");
                            if(fotokey != null && !fotokey.isEmpty()){
                                cliente.setUrlFoto(urlFotosNuvem + "/" + fotokey);
                            }
                            try {
                                cliente.setIdadeEmMeses(idadeEmMeses(rs.getDate("datanascimento")));
                            }catch (Exception e){}

                            cliente.setCategoria(rs.getString("categoria"));
                            cliente.setCodigoCategoria(rs.getInt("codigocategoria"));
                            cliente.setSituacao(rs.getString("situacao"));
                            cliente.setMatricula(rs.getString("matricula"));
                            cliente.setInicio(Uteis.getDataAplicandoFormatacao(rs.getDate("datavigenciade"), "dd/MM/YYYY"));
                            cliente.setFim(Uteis.getDataAplicandoFormatacao(rs.getDate("datavigenciaateajustada"), "dd/MM/YYYY"));
                            cliente.setPossuiIdVindi(!UteisValidacao.emptyNumber(rs.getInt("idvindi")));
                            cliente.setIdVindi(rs.getInt("idvindi"));
                            clientes.add(cliente);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return clientes;
    }

    public Integer idadeEmMeses(Date nascimento) {
        final Calendar calendarioInicial = Calendar.getInstance();
        final Calendar calendarioFinal = Calendar.getInstance();
        calendarioInicial.setTime(nascimento);
        calendarioFinal.setTime(Calendario.hoje());
        final int diferencaAnos = calendarioFinal.get(Calendar.YEAR) - calendarioInicial.get(Calendar.YEAR);
        return diferencaAnos * 12 + calendarioFinal.get(Calendar.MONTH) - calendarioInicial.get(Calendar.MONTH);
    }


    @Override
    @Deprecated
    public void salvaDadosChurn(ClienteDadosPessoaisDTO cliente) throws Exception {
        String sql = "   UPDATE situacaoclientesinteticodw SET riscochurn = ?, riscochurnlancamento = ?, sugestaogpt = ? where codigocliente = ?;";

        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                    sqlAlterar.setInt(1, cliente.getRiscoChurn().intValue());
                    sqlAlterar.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                    sqlAlterar.setString(3, cliente.getSugestaoGPT());
                    sqlAlterar.setInt(4, cliente.getCodigoCliente());
                    sqlAlterar.execute();
                } catch (SQLException e) {
                    throw new RuntimeException("Erro ao executar o SQL", e);
                } finally {
                    con.close();
                }
            });
        }
    }

    public List<ClienteSimplificadoDTO> consultarClienteSimplificadoPorCpf(String cpf) throws ServiceException {
        List<ClienteSimplificadoDTO> clientes = new ArrayList<>();
        String cpfConsulta = Uteis.removerMascara(cpf);
        if (UteisValidacao.emptyString(cpfConsulta)) {
            return clientes;
        }
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        String sql = "SELECT \n" +
                                " sit.matricula,\n" +
                                " sit.nomecliente,\n" +
                                " sit.nomeplano,\n" +
                                " coalesce(sit.situacao, '') as situacao,\n" +
                                " con.vigenciaateajustada,\n" +
                                " e.nome as nomeempresa, \n" +
                                " (select count(con_sub.codigo) from contrato con_sub where con_sub.pessoa = sit.codigopessoa and con_sub.situacao in ('AT','TR')) as qtdContratos \n" +
                                "FROM situacaoclientesinteticodw sit\n" +
                                " LEFT JOIN empresa e ON e.codigo = sit.empresacliente \n" +
                                " LEFT JOIN contrato con ON con.codigo = sit.codigocontrato \n" +
                                "WHERE sit.cpfconsulta = ? \n";
                        PreparedStatement pst = connection.prepareStatement(sql);
                        pst.setString(1, cpfConsulta);
                        ResultSet rs = pst.executeQuery();
                        while (rs.next()){
                            ClienteSimplificadoDTO clienteSimplDTO = new ClienteSimplificadoDTO();
                            clienteSimplDTO.setCodigoMatricula(rs.getInt("matricula"));
                            clienteSimplDTO.setNome(rs.getString("nomecliente"));
                            clienteSimplDTO.setNomeUnidade(rs.getString("nomeempresa"));
                            clienteSimplDTO.setNomePlano(rs.getString("nomeplano"));
                            SituacaoClienteEnum situacao = SituacaoClienteEnum.getSituacaoCliente(rs.getString("situacao"));
                            if (situacao != null) {
                                clienteSimplDTO.setSituacaoDescricao(situacao.getDescricao());
                            }
                            Date dataFim = rs.getDate("vigenciaateajustada");
                            int diasRestantesContrato = dataFim != null ? Uteis.diferencaEmDias(Calendario.hoje(), dataFim) : 0;
                            String periodoRestante = "";
                            if (diasRestantesContrato > 0) {
                                if (diasRestantesContrato > 30) {
                                    int meses = diasRestantesContrato / 30;
                                    periodoRestante += String.format("%d %s", meses, meses > 1 ? "meses" : "mês");
                                    if (diasRestantesContrato % 30 > 0) {
                                        int dias = diasRestantesContrato % 30;
                                        periodoRestante += String.format(" e %d %s", dias, dias > 1 ? "dias" : "dia");
                                    }
                                } else {
                                    periodoRestante = String.format("%d %s", diasRestantesContrato, diasRestantesContrato > 1 ? "dias" : "dia");
                                }
                            } else {
                                periodoRestante = "0 dias";
                            }
                            clienteSimplDTO.setPeriodoRestanteContrato(periodoRestante);
                            if (UteisValidacao.emptyNumber(rs.getInt("qtdContratos"))) {
                                clienteSimplDTO.setDescricaoQtdContratos("Nenhum contrato ativo");
                            } else if (rs.getInt("qtdContratos") == 1) {
                                clienteSimplDTO.setDescricaoQtdContratos("Um contrato ativo");
                            } else {
                                clienteSimplDTO.setDescricaoQtdContratos("Mais de um contrato ativo");
                            }
                            clientes.add(clienteSimplDTO);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return clientes;
    }

    public boolean clienteEstaInadimplente(Integer codigoMatricula, Integer codigoEmpresa) throws ServiceException {
        AtomicBoolean clienteInadimplente = new AtomicBoolean(false);
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try {
                    String sqlConfigTolerancia = "select\n" +
                            "\tcoalesce(e.toleranciapagamento,0) as toleranciapagamento_empresa,\n" +
                            "\tcoalesce(cfg.toleranciapagamento,0) as toleranciapagamento_cfg\n" +
                            "from configuracaosistema cfg\n" +
                            "\tleft join empresa e on e.codigo = " + codigoEmpresa;

                    int toleranciaPagamento = 0;
                    ResultSet rsTolerancia = con.prepareStatement(sqlConfigTolerancia).executeQuery();
                    if (rsTolerancia.next()) {
                        toleranciaPagamento = rsTolerancia.getInt("toleranciapagamento_empresa");
                        if (toleranciaPagamento == 0) {
                            toleranciaPagamento = rsTolerancia.getInt("toleranciapagamento_cfg");
                        }
                    } else {
                        throw new ServiceException("Não foi possível obter a tolerância de pagamento da empresa ou configuração do sistema.");
                    }

                    String sql = "SELECT cli.codigo FROM cliente cli \n" +
                            " INNER JOIN pessoa pes ON pes.codigo = cli.pessoa \n" +
                            "WHERE cli.codigoMatricula = " + codigoMatricula + " \n" +
                            "AND exists (SELECT mpar.codigo FROM movparcela mpar \n" +
                            "   WHERE mpar.pessoa = cli.pessoa \n" +
                            "   AND mpar.situacao = 'EA' \n" +
                            "   AND (mpar.datavencimento + (interval '1 day' * " + toleranciaPagamento + ")) < '" + Calendario.getData("yyyy-MM-dd") + "') \n";

                    try (PreparedStatement stm = con.prepareStatement(sql)) {
                        try (ResultSet rs = stm.executeQuery()) {
                            clienteInadimplente.set(rs.next());
                        }
                    }
                } catch (SQLException e) {
                    throw new RuntimeException("Erro ao executar o SQL", e);
                } catch (ServiceException e) {
                    throw new RuntimeException(e);
                } finally {
                    con.close();
                }
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return clienteInadimplente.get();
    }
}
