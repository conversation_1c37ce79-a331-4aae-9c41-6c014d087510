package com.pacto.adm.core.dao.interfaces;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroMovProdutoJSON;
import com.pacto.adm.core.entities.MovProduto;

import java.util.List;

public interface MovProdutoDao extends DaoGenerico<MovProduto, Integer> {

    List<MovProduto> findAllByPessoa(Integer codPessoa, FiltroMovProdutoJSON filtroMovProdutoJSON, PaginadorDTO paginadorDTO) throws Exception;

    List<MovProduto> consultarPorCodigoContrato(Integer codigoContrato) throws Exception;

    MovProduto consultarPorCodigoVendaAvulsaRetornaCodigo(Integer codigo) throws Exception;

    List<MovProduto> consultarProdutoComValidadePorCodigoPessoa(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception;

    List<MovProduto> consultarMovProdutosPorCodigoPessoa(Integer codPessoa, Integer codContrato,
                                                         FiltroMovProdutoJSON filtroMovProdutoJSON,
                                                         PaginadorDTO paginadorDTO) throws Exception;

    Integer consultarPorCodigoMovProdutoRetornaAulaAvulsaDiaria(Integer codigoMovProduto) throws Exception;
}
