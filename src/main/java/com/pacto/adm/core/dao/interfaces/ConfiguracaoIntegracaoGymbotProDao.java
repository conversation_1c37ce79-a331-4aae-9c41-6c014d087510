package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.ConfiguracaoIntegracaoGymbotPro;
import com.pacto.config.exceptions.ServiceException;

import java.util.List;

public interface ConfiguracaoIntegracaoGymbotProDao extends DaoGenerico<ConfiguracaoIntegracaoGymbotPro, Integer> {

    List<ConfiguracaoIntegracaoGymbotPro> findByEmpresaId(Integer empresaId) throws ServiceException;

    String findbyFluxoTelaAluno(String idEmpresa) throws ServiceException;
}
