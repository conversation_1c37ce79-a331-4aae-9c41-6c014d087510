package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ConfiguracaoEmpresaHubSpotDao;
import com.pacto.adm.core.entities.ConfiguracaoEmpresaHubSpot;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.Map;

@Repository
public class ConfiguracaoEmpresaHubSpotDaoImpl extends  DaoGenericoImpl<ConfiguracaoEmpresaHubSpot, Integer> implements ConfiguracaoEmpresaHubSpotDao {

    @Override
    public ConfiguracaoEmpresaHubSpot findByEmpresaId(Integer empresaId) {
        getCurrentSession().clear();

        Map<String, Object> params = new HashMap<>();
        StringBuilder s = new StringBuilder("SELECT obj FROM ConfiguracaoEmpresaHubSpot obj ");
        s.append(" WHERE obj.empresa.codigo = :empresa ");
        params.put("empresa", empresaId);
        Query query = getCurrentSession().createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        ConfiguracaoEmpresaHubSpot configEntity = new ConfiguracaoEmpresaHubSpot();
        try {
            configEntity = (ConfiguracaoEmpresaHubSpot) query.getResultList().get(0);
        } catch (Exception e) {
        }
        return configEntity;
    }
}
