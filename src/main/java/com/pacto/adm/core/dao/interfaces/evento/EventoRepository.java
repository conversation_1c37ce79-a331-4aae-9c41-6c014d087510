package com.pacto.adm.core.dao.interfaces.evento;

import com.pacto.adm.core.entities.Evento;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface EventoRepository extends JpaRepository<Evento, Integer> {

    @Query("SELECT e FROM Evento e" +
            " WHERE upper(e.descricao) like concat(upper(:descricao), '%')" +
            " AND ((:situacao = true and :today between e.vigenciaInicial AND e.vigenciaFinal) OR :situacao = false OR :situacao IS NULL)" +
            " ORDER BY e.descricao")
    List<Evento> findAllByDescricaoAndSituacao(
            @Param("situacao") Boolean situacao,
            @Param("descricao") String descricao,
            @Param("today") Date today
    );

    @Query("SELECT e FROM Evento e" +
            " WHERE (upper(e.descricao) like concat(upper(:descricao), '%') OR :descricao IS NULL OR :descricao != '')" +
            " AND :date between e.vigenciaInicial AND e.vigenciaFinal" +
            " ORDER BY e.descricao")
    List<Evento> findAllByDescricao(
            @Param("descricao") String descricao,
            @Param("date") Date date
    );

}
