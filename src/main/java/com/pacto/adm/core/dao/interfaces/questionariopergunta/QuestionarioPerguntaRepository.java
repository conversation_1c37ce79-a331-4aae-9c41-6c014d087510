package com.pacto.adm.core.dao.interfaces.questionariopergunta;

import com.pacto.adm.core.entities.QuestionarioPergunta;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QuestionarioPerguntaRepository extends JpaRepository<QuestionarioPergunta, Integer> {

    List<QuestionarioPergunta> findByQuestionario_Codigo(Integer questionarioCodigo);

}
