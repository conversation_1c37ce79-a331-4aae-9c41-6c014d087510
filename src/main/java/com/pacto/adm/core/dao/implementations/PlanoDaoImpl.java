package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.PlanoDao;
import com.pacto.adm.core.dto.ConfigConsultaTurmaDTO;
import com.pacto.adm.core.dto.PessoaDTO;
import com.pacto.adm.core.dto.filtros.FiltroNegociacaoPlanoJSON;
import com.pacto.adm.core.dto.negociacao.*;
import com.pacto.adm.core.dto.nivelturma.NivelTurmaDTO;
import com.pacto.adm.core.entities.contrato.Plano;
import com.pacto.adm.core.entities.contrato.SituacaoContratoEnum;
import com.pacto.adm.core.enumerador.DisponibilidadeTurmaEnum;
import com.pacto.adm.core.enumerador.TipoHorarioCreditoTreinoEnum;
import com.pacto.adm.core.enumerador.TipoProdutoEnum;
import com.pacto.adm.core.enumerador.TurnoEnum;
import com.pacto.config.enumerador.DiaSemana;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Ordenacao;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Repository;

import java.sql.*;
import java.util.*;
import java.util.Date;
import java.util.concurrent.atomic.AtomicReference;

@Repository
public class PlanoDaoImpl extends DaoGenericoImpl<Plano, Integer> implements PlanoDao {

    @Override
    public List<Plano> findVigentesByEmpresa(Integer empresa,
                                             Integer planoForcar,
                                             Boolean consultarEmPlanoEmpresaVendaPermitida,
                                             FiltroNegociacaoPlanoJSON filtros,
                                             Integer codigoCliente,
                                             Integer contratoRenovar) throws Exception {
        getCurrentSession().clear();
        List<Integer> codigos = codigosPlanoVendaPorEmpresa(empresa, null,
                false,
                consultarEmPlanoEmpresaVendaPermitida, codigoCliente);

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append(" WHERE obj.codigo in ( \n");

        codigos.add(planoForcar);
        for (int i = 0; i < codigos.size(); i++) {
            where.append(codigos.get(i));
            if (i < codigos.size() - 1) {
                where.append(", ");
            }
        }
        where.append(") AND obj.vigenciaDe <= :vigenciaDe \n");
        if(UteisValidacao.emptyNumber(contratoRenovar)){
            where.append(" AND obj.ingressoAte >= :ingressoAte \n");
        } else {
            where.append(" AND (obj.ingressoAte >= :ingressoAte \n");
            where.append(" OR (obj.vigenciaAte >= :ingressoAte AND obj.codigo = :planoRenovar))\n");
            params.put("planoRenovar", planoRenovar(contratoRenovar));
        }
        if (filtros != null) {
            if (filtros.getDataLancamentoContrato() != null) {
                params.put("vigenciaDe", Calendario.getDataComHoraZerada(filtros.getDataLancamentoContrato()));
                params.put("ingressoAte", Calendario.getDataComHoraZerada(filtros.getDataLancamentoContrato()));
            }
        } else {
            params.put("vigenciaDe", Calendario.getDataComHoraZerada(Calendario.hoje()));
            params.put("ingressoAte", Calendario.getDataComHoraZerada(Calendario.hoje()));
        }
        where.append(" order by obj.descricao");
        List<Plano> planos = findByParam(where, params);
        return Ordenacao.ordenarLista(planos, "descricao");
    }

    @Override
    public List<Plano> findByCodigos(List<Integer> codigos) throws Exception {

        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.codigo IN (:codigosPlano)\n");
        params.put("codigosPlano", codigos);

        return findByParam(where, params);
    }

    private Integer planoRenovar(Integer contrato) {
        AtomicReference<Integer> plano = new AtomicReference<>(0);
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(con -> {
                    StringBuilder query = new StringBuilder();
                    query.append(" select c.plano from contrato c ");
                    query.append(" WHERE c.codigo = ").append(contrato);
                    PreparedStatement pst = con.prepareStatement(query.toString());
                    ResultSet rs = pst.executeQuery();
                    plano.set(rs.next() ? rs.getInt("plano") : 0);
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return plano.get();
    }

    public List<DescontoDTO> descontosPorPlano(Integer empresa) throws Exception {
        List<DescontoDTO> descontosPorPlano = new ArrayList<>();
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(con -> {
                    try {
                        PreparedStatement pst = con.prepareStatement("SELECT codigo, descricao  FROM ConvenioDesconto\n" +
                                "WHERE (dataInicioVigencia <= '" +
                                Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd") +
                                "' and dataFinalVigencia >= '" +
                                Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd") +
                                "') " +
                                "AND empresa = " + empresa + " ORDER BY descricao");
                        ResultSet rs = pst.executeQuery();
                        while (rs.next()) {
                            DescontoDTO descontoDTO = new DescontoDTO();
                            descontoDTO.setCodigo(rs.getInt("codigo"));
                            descontoDTO.setDescricao(rs.getString("descricao"));
                            descontosPorPlano.add(descontoDTO);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return descontosPorPlano;
    }

    private List<Integer> codigosPlanoVendaPorEmpresa(Integer empresa,
                                                      Integer categoria,
                                                      Boolean planoPersonal,
                                                      Boolean consultarEmPlanoEmpresaVendaPermitida,
                                                      Integer codigoCliente) throws Exception {

        StringBuilder sbSqlConsulta = new StringBuilder();
        sbSqlConsulta.append("SELECT codigo FROM Plano\n");
        sbSqlConsulta.append("WHERE (NOT site OR (site AND permitirVendaPlanoSiteNoBalcao))\n");
        sbSqlConsulta.append("AND (NOT totem OR (totem AND permitirVendaPlanoTotemNoBalcao))\n");

        if (!planoPersonal) {
            sbSqlConsulta.append(" AND (planoPersonal IS FALSE OR planoPersonal IS NULL) ");
        }
        if (empresa != 0) {
            if (consultarEmPlanoEmpresaVendaPermitida) {
                sbSqlConsulta.append("  AND ((SELECT COUNT(*) ");
                sbSqlConsulta.append("         FROM planoempresa ");
                sbSqlConsulta.append("         WHERE planoempresa.plano = plano.codigo ");
                sbSqlConsulta.append("           AND planoempresa.empresa = ").append(empresa).append(" ");
                sbSqlConsulta.append("           AND venda IS TRUE) > 0 ");
                sbSqlConsulta.append("        OR (SELECT COUNT(*) ");
                sbSqlConsulta.append("            FROM planoempresa ");
                sbSqlConsulta.append("            WHERE planoempresa.plano = plano.codigo ");
                sbSqlConsulta.append("              AND venda IS TRUE) = 0) ");
            } else {
                sbSqlConsulta.append("  AND empresa = ").append(empresa).append(" \n");
            }
        }
        if (categoria != null && categoria > 0) {
            sbSqlConsulta.append("  AND ((restringevendaporcategoria IS TRUE AND codigo IN (SELECT plano FROM planocategoria WHERE categoria = ")
                    .append(categoria).append(")) ");
            sbSqlConsulta.append("       OR (restringevendaporcategoria IS FALSE)) ");
        }

        if (codigoCliente != null && codigoCliente > 0) {
            sbSqlConsulta.append("  AND ( (bloquearRecompra IS NULL OR bloquearRecompra = FALSE) ");
            sbSqlConsulta.append("        OR (bloquearRecompra = TRUE AND NOT EXISTS (");
            sbSqlConsulta.append("              SELECT 1 FROM contrato c ");
            sbSqlConsulta.append("              JOIN cliente cl ON cl.pessoa = c.pessoa ");
            sbSqlConsulta.append("              JOIN plano p ON p.codigo = c.plano ");
            sbSqlConsulta.append("              WHERE cl.codigo = ? ");
            sbSqlConsulta.append("                AND cl.situacao != 'VI' ");
            sbSqlConsulta.append("                AND p.bloquearRecompra = TRUE ");
            sbSqlConsulta.append("         )) ) ");
        }

        sbSqlConsulta.append(" ORDER BY descricao");

        List<Integer> codigos = new ArrayList<>();
        try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
            sessionImplementor.doWork(con -> {
                try (PreparedStatement pst = con.prepareStatement(sbSqlConsulta.toString())) {

                    int paramIndex = 1;

                    if (codigoCliente != null && codigoCliente > 0) {
                        pst.setInt(paramIndex++, codigoCliente);
                    }

                    try (ResultSet rs = pst.executeQuery()) {
                        while (rs.next()) {
                            codigos.add(rs.getInt("codigo"));
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return codigos;
    }

    public List<PlanoDuracaoDTO> duracoesPorPlano(Integer plano, boolean addObsOperacao) throws Exception {
        List<PlanoDuracaoDTO> duracoesPorPlano = new ArrayList<>();
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(con -> {
                    try {
                        PreparedStatement pst = con.prepareStatement("select numeromeses,codigo, " +
                                "p.tipoOperacao, p.tipoValor, p.valorEspecifico, p.percentualDesconto " +
                                "from planoduracao p where situacao is true and plano = " + plano +
                                " order by numeromeses");
                        ResultSet rs = pst.executeQuery();
                        while (rs.next()) {
                            PlanoDuracaoDTO planoDuracaoDTO = new PlanoDuracaoDTO();
                            int nrMeses = rs.getInt("numeromeses");
                            planoDuracaoDTO.setCodigo(rs.getInt("codigo"));
                            planoDuracaoDTO.setNrMeses(nrMeses);

                            try {
                                String desc = String.valueOf(nrMeses).concat(nrMeses > 1 ? " meses" : " mês");
                                if(addObsOperacao){
                                    String tipoOperacao = rs.getString("tipoOperacao");
                                    String tipoValor = rs.getString("tipoValor");
                                    if(!UteisValidacao.emptyString(tipoOperacao)
                                            && (!UteisValidacao.emptyNumber(rs.getDouble("valorEspecifico")) || !UteisValidacao.emptyNumber(rs.getDouble("percentualDesconto"))
                                    )){
                                        desc += " - " + (tipoOperacao.equals("AC") ? "Acréscimo " : "Redução ");
                                        if(tipoValor != null && tipoValor.equals("VE")){
                                            desc += "R$ " + Uteis.arrendondarForcando2CadasDecimaisComVirgula(rs.getDouble("valorEspecifico"));
                                        } else {
                                            desc += Uteis.arredondar(rs.getDouble("percentualDesconto"), 0, 1) + "%";
                                        }
                                    }
                                }
                                planoDuracaoDTO.setDescricao(desc);
                            }catch (Exception e){
                                planoDuracaoDTO.setDescricao(String.valueOf(nrMeses).concat(nrMeses > 1 ? " meses" : " mês"));
                            }
                            planoDuracaoDTO.setCondicoes(condicoesPorDuracao(con, planoDuracaoDTO.getCodigo()));
                            planoDuracaoDTO.setCreditos(creditosPorDuracao(con, planoDuracaoDTO.getCodigo()));
                            duracoesPorPlano.add(planoDuracaoDTO);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return duracoesPorPlano;
    }

    public List<DiaCartaoDTO> diasCartao(Integer plano, Integer diaAtual) throws Exception {
        List<DiaCartaoDTO> opcoes = new ArrayList<>();
        List<String> diasCartao = new ArrayList<>();
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(con -> {
                    try {
                        PreparedStatement pst = con.prepareStatement("select codigo, diasvencimentoprorata from plano p \n" +
                                "where p.recorrencia is true and p.codigo = " + plano);
                        ResultSet rs = pst.executeQuery();
                        if (rs.next()) {
                            String dias = rs.getString("diasvencimentoprorata");
                            if (UteisValidacao.emptyString(dias)) {
                                for (int i = 1; i <= 31; i++) {
                                    diasCartao.add(String.valueOf(i));
                                }
                            } else {
                                diasCartao.addAll(Arrays.asList(dias.split(",")));
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        int diaHoje = Calendario.getDia(Calendario.hoje());
        if (!diasCartao.isEmpty()) {
            opcoes.add(new DiaCartaoDTO(diaHoje, "Hoje"));
        }
        for (String obj : diasCartao) {
            if (!obj.isEmpty() && diaHoje != Integer.parseInt(obj)) {
                opcoes.add(new DiaCartaoDTO(Integer.valueOf(obj), obj));
            }
        }
        return opcoes;
    }

    public List<DiaCartaoDTO> diasProRata(Integer diaAtual, String dias) {
        List<DiaCartaoDTO> opcoes = new ArrayList<>();
        if(dias == null || dias.isEmpty()){
            for (int i = 1; i <= 31; i++) {
                opcoes.add(new DiaCartaoDTO(i, String.valueOf(i)));
            }
        } else {
            try {
                String[] split = dias.split(",");
                for (String s : split) {
                    opcoes.add(new DiaCartaoDTO(Integer.valueOf(s), s));
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        opcoes.add(0, new DiaCartaoDTO(diaAtual, "Hoje"));
        return opcoes;
    }

    private List<PlanoDuracaoCondicaoDTO> condicoesPorDuracao(Connection con, Integer duracao) throws Exception {
        List<PlanoDuracaoCondicaoDTO> lista = new ArrayList<>();
        PreparedStatement pst = con.prepareStatement("select p.codigo, c.descricao, c.nrparcelas," +
                " p.tipoOperacao, p.tipoValor, p.valorEspecifico, p.percentualDesconto, c.tipoconveniocobranca," +
                " c.geradoAutomaticoPlanoRecorrente" +
                " from planocondicaopagamento p \n" +
                " inner join condicaopagamento c on c.codigo = p.condicaopagamento\n" +
                " where p.planoduracao = " + duracao +
                " order by c.nrparcelas, c.tipoconveniocobranca  ");
        ResultSet rs = pst.executeQuery();
        while (rs.next()) {
            PlanoDuracaoCondicaoDTO obj = new PlanoDuracaoCondicaoDTO();
            obj.setCodigo(rs.getInt("codigo"));
            obj.setNrParcelas(rs.getInt("nrparcelas"));
            obj.setTipoConvenio(rs.getInt("tipoconveniocobranca"));
            obj.setGeradoAutomaticoPlanoRecorrente(rs.getBoolean("geradoautomaticoplanorecorrente"));
            try {
                String desc = rs.getString("descricao");
                String tipoOperacao = rs.getString("tipoOperacao");
                String tipoValor = rs.getString("tipoValor");
                if(!UteisValidacao.emptyString(tipoOperacao)
                        && (!UteisValidacao.emptyNumber(rs.getDouble("valorEspecifico")) || !UteisValidacao.emptyNumber(rs.getDouble("percentualDesconto"))
                )){
                    desc += " - " + (tipoOperacao.equals("AC") ? "Acréscimo " : "Redução ");
                    if(tipoValor != null && tipoValor.equals("VE")){
                        desc += "R$ " + Uteis.arrendondarForcando2CadasDecimaisComVirgula(rs.getDouble("valorEspecifico"));
                    } else {
                        desc += Uteis.arredondar(rs.getDouble("percentualDesconto"), 0, 1) + "%";
                    }
                }
                obj.setDescricao(desc);
            }catch (Exception e){
                obj.setDescricao(rs.getString("descricao"));
            }
            lista.add(obj);
        }
        return lista;
    }

    private List<PlanoDuracaoCreditoDTO> creditosPorDuracao(Connection con, Integer duracao) throws Exception {
        List<PlanoDuracaoCreditoDTO> lista = new ArrayList<>();
        PreparedStatement pst = con.prepareStatement("select planoduracao, quantidadecreditomensal," +
                " valorunitario, quantidadecreditocompra, numerovezessemana, tipohorariocreditotreino, codigo " +
                " from planoduracaocreditotreino  \n" +
                " where planoduracao = " + duracao +
                " order by quantidadecreditocompra");
        ResultSet rs = pst.executeQuery();
        while (rs.next()) {
            PlanoDuracaoCreditoDTO obj = new PlanoDuracaoCreditoDTO();
            obj.setCodigo(rs.getInt("codigo"));
            obj.setPlanoDuracao(rs.getInt("planoduracao"));
            obj.setNumeroVezesSemana(rs.getInt("numerovezessemana"));
            obj.setQuantidadeCreditoCompra(rs.getInt("quantidadecreditocompra"));
            obj.setQuantidadeCreditoMensal(rs.getInt("quantidadecreditomensal"));
            obj.setValorUnitario(rs.getDouble("valorunitario"));
            obj.setTipoHorarioCreditoTreino(rs.getInt("tipohorariocreditotreino"));
            TipoHorarioCreditoTreinoEnum tipo = TipoHorarioCreditoTreinoEnum.getTipo(obj.getTipoHorarioCreditoTreino());
            obj.setLivre(tipo != null && !tipo.equals(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA));
            lista.add(obj);
        }
        return lista;
    }

    public List<PlanoHorarioDTO> horariosPorPlano(Integer plano, boolean addObsOperacao) throws Exception {
        List<PlanoHorarioDTO> horariosPorPlano = new ArrayList<>();
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(con -> {
                    try {
                        PreparedStatement pst = con.prepareStatement("select h.livre, h.descricao, p.codigo," +
                                "p.tipoOperacao, p.tipoValor, p.valorEspecifico, p.percentualDesconto " +
                                " from planohorario p \n" +
                                " inner join horario h on p.horario = h.codigo \n" +
                                " where p.plano = " + plano +
                                " order by h.descricao ");
                        ResultSet rs = pst.executeQuery();
                        while (rs.next()) {
                            PlanoHorarioDTO planoHorarioDTO = new PlanoHorarioDTO();
                            planoHorarioDTO.setCodigo(rs.getInt("codigo"));
                            try {
                                String desc = rs.getString("descricao");
                                if(addObsOperacao){
                                    String tipoOperacao = rs.getString("tipoOperacao");
                                    String tipoValor = rs.getString("tipoValor");
                                    if(!UteisValidacao.emptyString(tipoOperacao)){
                                        desc += " - " + (tipoOperacao.equals("AC") ? "Acréscimo " : "Redução ");
                                        if(tipoValor != null && tipoValor.equals("VE")){
                                            desc += "R$ " + Uteis.arrendondarForcando2CadasDecimaisComVirgula(rs.getDouble("valorEspecifico"));
                                        } else {
                                            desc += Uteis.arredondar(rs.getDouble("percentualDesconto"), 0, 1) + "%";
                                        }
                                    }
                                }
                                planoHorarioDTO.setDescricao(desc);
                                planoHorarioDTO.setLivre(rs.getBoolean("livre"));
                            }catch (Exception e){
                                planoHorarioDTO.setDescricao(rs.getString("descricao"));
                            }
                            horariosPorPlano.add(planoHorarioDTO);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return horariosPorPlano;
    }

    public List<PlanoModalidadeDTO> modalidadesPorPlano(Integer plano, Boolean creditoLivreMarcarAula, Integer contratoBaseado) throws Exception {
        List<PlanoModalidadeDTO> lista = new ArrayList<>();
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(con -> {
                    try(PreparedStatement pst = con.prepareStatement("select p.codigo, m.nome, p.modalidade, m.valormensal, " +
                            " m.utilizarturma, m.nrvezes, m.modalidadedefault \n" +
                            " from planomodalidade p \n" +
                            " inner join modalidade m on m.codigo = p.modalidade \n" +
                            " where m.ativo is true and p.plano = " + plano +
                            " order by m.nome ");
                        ResultSet rs = pst.executeQuery()){
                        while (rs.next()) {
                            PlanoModalidadeDTO obj = new PlanoModalidadeDTO();
                            obj.setCodigo(rs.getInt("modalidade"));
                            obj.setDescricao(rs.getString("nome"));
                            obj.setNrvezes(rs.getInt("nrvezes"));
                            obj.setUtilizarTurma(!creditoLivreMarcarAula && rs.getBoolean("utilizarturma"));
                            if(!obj.isUtilizarTurma()){
                                obj.setUtilizarTurma(temAulaColetivaAlunoFixo(con, rs.getInt("modalidade")));
                                obj.setAulaColetivaFixa(obj.isUtilizarTurma());
                            }
                            obj.setValorModalidade(rs.getDouble("valormensal"));
                            obj.setConfigsVezes(modalidadesVezesSemana(con, rs.getInt("codigo")));
                            obj.setVezesSugeridas(vezesSemanaSugerir(con, obj, contratoBaseado));
                            lista.add(obj);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return lista;
    }

    private PlanoModalidadeVezesDTO vezesSemanaSugerir(Connection con, PlanoModalidadeDTO obj, Integer contratoBaseado) throws Exception {
        if (UteisValidacao.emptyNumber(contratoBaseado) || obj.getConfigsVezes() == null || obj.getConfigsVezes().isEmpty()) {
            return obj.getConfigsVezes() == null || obj.getConfigsVezes().isEmpty() ?
                    new PlanoModalidadeVezesDTO() : obj.getConfigsVezes().get(0);
        }

        PreparedStatement pst = con.prepareStatement("select vezessemana from contratomodalidade c \n" +
                " where contrato = " + contratoBaseado +
                " and modalidade = " + obj.getCodigo());
        ResultSet rs = pst.executeQuery();
        if(rs.next()){
            int vezessemana = rs.getInt("vezessemana");
            for(PlanoModalidadeVezesDTO pmv : obj.getConfigsVezes()){
                if(pmv.getVezes() == vezessemana){
                    return pmv;
                }
            }
        }
        if(!UteisValidacao.emptyList(obj.getConfigsVezes())){
            return obj.getConfigsVezes().get(0);
        }
        return new PlanoModalidadeVezesDTO();
    }

    private boolean temAulaColetivaAlunoFixo(Connection con, Integer modalidade) throws Exception {
        PreparedStatement pst = con.prepareStatement("select count(*) as total from turma " +
                "where permitefixar " +
                " and current_date between datainicialvigencia and datafinalvigencia " +
                " and modalidade = " + modalidade + " and aulacoletiva");
        ResultSet rs = pst.executeQuery();
        return rs.next() && rs.getInt("total") > 0;
    }

    public List<PacoteDTO> pacotesPorPlano(Integer plano) throws Exception {
        List<PacoteDTO> lista = new ArrayList<>();
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(con -> {
                    try {
                        PreparedStatement pst = con.prepareStatement(
                                "select c.descricao, c.codigo, c.modalidadesespecificas, " +
                                        "c.composicaodefault, c.composicaoadicional, c.precocomposicao, c.qtdemodalidades " +
                                        "from planocomposicao p \n" +
                                        "inner join composicao c on c.codigo = p.composicao \n" +
                                        "where plano = " + plano +
                                        " order by c.descricao ");
                        ResultSet rs = pst.executeQuery();
                        while (rs.next()) {
                            PacoteDTO obj = new PacoteDTO();
                            obj.setCodigo(rs.getInt("codigo"));
                            obj.setDescricao(rs.getString("descricao"));
                            obj.setPadrao(rs.getBoolean("composicaodefault"));
                            obj.setModalidadesEspecificas(rs.getBoolean("modalidadesespecificas"));
                            obj.setAdicional(rs.getBoolean("composicaoadicional"));
                            obj.setValor(rs.getDouble("precocomposicao"));
                            obj.setQtdeModalidades(rs.getInt("qtdemodalidades"));
                            PreparedStatement pstModalidades = con.prepareStatement("select modalidade from composicaomodalidade " +
                                    "where composicao = " + obj.getCodigo());
                            ResultSet rsMod = pstModalidades.executeQuery();
                            List<Integer> modalidades = new ArrayList<>();
                            while (rsMod.next()) {
                                modalidades.add(rsMod.getInt("modalidade"));
                            }
                            obj.setModalidades(modalidades);
                            lista.add(obj);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return lista;
    }

    public List<Integer> horariosFixar(Integer ... horarios) throws Exception {
        List<Integer> lista = new ArrayList<>();
        final StringBuilder sb = new StringBuilder();
        sb.append(horarios[0]);
        for (int i = 1; i < horarios.length; i++) {
            sb.append(", ").append(horarios[i]);
        }
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(con -> {
                    try {
                        PreparedStatement pst = con.prepareStatement("select h.codigo from horarioturma h \n" +
                                "inner join turma t on t.codigo = h.turma \n" +
                                "where t.aulacoletiva \n" +
                                "and h.codigo in (" + sb + ")");
                        ResultSet rs = pst.executeQuery();
                        while (rs.next()) {
                            lista.add(rs.getInt("codigo"));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return lista;
    }

    public List<PlanoModalidadeVezesDTO> modalidadesVezesSemana(Connection con, Integer planoModalidade) throws Exception {
        List<PlanoModalidadeVezesDTO> lista = new ArrayList<>();
        PreparedStatement pst = con.prepareStatement(
                " select  pl.creditosessao, p.duracao, p.horario, p.tipooperacao, p.tipovalor," +
                        " p.codigo, p.referencia, p.nrvezes, p.percentualDesconto, " +
                        " p.valormodalidade, p.valorespecifico, p.percentualdesconto " +
                        " from planomodalidadevezessemana p \n" +
                        " inner join planomodalidade pm on pm.codigo = p.planomodalidade " +
                        " inner join plano pl on pl.codigo = pm.plano" +
                        " where p.planomodalidade = " + planoModalidade);
        ResultSet rs = pst.executeQuery();
        List<Integer> vzs = new ArrayList<>();
        while (rs.next()) {
            if(vzs.contains(rs.getInt("nrvezes"))){
                continue;
            }
            vzs.add(rs.getInt("nrvezes"));
            PlanoModalidadeVezesDTO obj = new PlanoModalidadeVezesDTO();
            obj.setCodigo(rs.getInt("codigo"));
            obj.setVezes(rs.getInt("nrvezes"));
            obj.setDuracao(rs.getInt("duracao"));
            obj.setDuracao(rs.getInt("horario"));
            obj.setTipoOperacao(rs.getString("tipooperacao"));
            obj.setTipoValor(rs.getString("tipovalor"));
            obj.setValorModalidade(rs.getDouble("valormodalidade"));
            obj.setValorEspecifico(rs.getDouble("valorespecifico"));
            obj.setPercentualDesconto(rs.getDouble("percentualdesconto"));
            obj.setReferencia(rs.getBoolean("referencia"));
            obj.setCreditoSessao(rs.getBoolean("creditosessao"));
            lista.add(obj);
        }
        return lista;
    }

    private PlanoProdutoDTO montarPlanoProduto(ResultSet rs, String tipoproduto) throws SQLException {
        PlanoProdutoDTO obj = new PlanoProdutoDTO();
        obj.setCodigo(rs.getInt("codigoproduto"));
        obj.setDescricao(rs.getString("descricao"));
        obj.setTipoProduto(tipoproduto);
        obj.setProduto(rs.getInt("produto"));
        obj.setObrigatorio(rs.getBoolean("obrigatorio"));
        obj.setValor(rs.getDouble("valorproduto"));
        obj.setQuantidade(1);
        return obj;
    }

    public ConfigConsultaTurmaDTO configAgenda(Integer empresa, Integer modalidade) throws ServiceException{
        try {
            ConfigConsultaTurmaDTO cfg = new ConfigConsultaTurmaDTO();
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        StringBuilder sql = new StringBuilder();
                        sql.append(" select distinct c.codigo, p.nome from colaborador c ");
                        sql.append(" inner join pessoa p on p.codigo = c.pessoa ");
                        sql.append(" inner join horarioturma h on h.professor = c.codigo ");
                        sql.append(" inner join turma t on t.codigo = h.turma ");
                        sql.append(" where c.situacao = 'AT' ");
                        sql.append(" and h.situacao = 'AT' ");
                        sql.append(" and (not t.aulacoletiva or t.permitefixar) ");
                        sql.append(" and t.modalidade = ").append(modalidade);
                        sql.append(" and c.empresa = ").append(empresa);
                        sql.append(" order by nome ");

                        PreparedStatement pst = connection.prepareStatement(sql.toString());
                        ResultSet rs = pst.executeQuery();
                        while(rs.next()) {
                            cfg.getProfessores().add(new PessoaDTO(rs.getInt("codigo"),
                                    rs.getString("nome")));
                        }
                        sql = new StringBuilder();
                        sql.append(" SELECT DISTINCT n.codigo, n.descricao, n.codigomgb ");
                        sql.append(" FROM nivelturma n ");
                        sql.append(" INNER JOIN horarioturma h ON h.nivelturma = n.codigo ");
                        sql.append(" INNER JOIN turma t ON t.codigo = h.turma ");
                        sql.append(" WHERE t.modalidade = ").append(modalidade);
                        sql.append(" ORDER BY descricao");

                        pst = connection.prepareStatement(sql.toString());
                        rs = pst.executeQuery();
                        while(rs.next()) {
                            cfg.getNiveis().add(new NivelTurmaDTO(
                                    rs.getInt("codigo"),
                                    rs.getString("descricao"),
                                    rs.getString("codigomgb")
                            ));
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
            return cfg;
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    public List<AgendaTurmaDTO> montarAgendaTurma(Integer modalidade,
                                                  Integer nivel,
                                                  Integer professor,
                                                  String periodo,
                                                  String disponibilidade,
                                                  Integer cliente,
                                                  Integer contratoBaseado,
                                                  Date dataBase,
                                                  Integer empresa) throws ServiceException {
        Map<String, Map<DiaSemana, List<HorarioTurmaAgendaDTO>>> agenda = new HashMap<>();
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    try {
                        StringBuilder sql = new StringBuilder();
                        sql.append(" select t.niveis, t.aulacoletiva, t.idademaxima, t.idademinima, t.idademinimameses, t.idademaximameses, \n");
                        sql.append(" a.descricao as ambiente, h.horainicial, h.codigo, \n");
                        sql.append(" h.diasemana, t.modalidade, t.bloquearLotacaoFutura, nrmaximoaluno, t.bloquearmatriculasacimalimite,\n");
                        sql.append(" h.horafinal, t.descricao as turma, p.nome as professor, n.codigomgb as nivelcodigomgb, \n");
                        sql.append(" emp.horariocapacidadeporcategoria \n");
                        sql.append(" from horarioturma h \n");
                        sql.append(" inner join colaborador c on c.codigo = h.professor \n");
                        sql.append(" inner join pessoa p on p.codigo = c.pessoa \n");
                        sql.append(" inner join turma t on t.codigo = h.turma \n");
                        sql.append(" inner join ambiente a on h.ambiente = a.codigo \n");
                        sql.append(" inner join nivelturma n on n.codigo = h.nivelturma \n");
                        sql.append(" inner join empresa emp on emp.codigo = t.empresa \n");
                        sql.append(" where (t.aulacoletiva is false or t.permitefixar) \n");
                        sql.append(" and (h.ativo or h.ativo is null) and h.situacao = 'AT' AND t.empresa = ").append(empresa).append(" \n");
                        if(contratoBaseado == null) {
                            sql.append(" and '").append(Calendario.getDataAplicandoFormatacao(dataBase, "yyyy-MM-dd"));
                            sql.append("' between t.datainicialvigencia and t.datafinalvigencia\n");
                            sql.append(" and t.modalidade = ").append(modalidade);
                            if(!UteisValidacao.emptyNumber(professor)){
                                sql.append(" and h.professor = ").append(professor);
                            }
                            if(!UteisValidacao.emptyNumber(nivel)){
                                sql.append(" and h.nivelturma = ").append(nivel);
                            }
                        } else {
                            sql.append(" and h.situacao = 'AT' and h.codigo in ( \n");
                            sql.append(" select cmht.horarioturma from ContratoModalidadeHorarioTurma cmht \n");
                            sql.append(" inner join contratomodalidadeturma cmt on cmt.codigo = cmht.contratomodalidadeturma \n");
                            sql.append(" inner join contratomodalidade cm on cm.codigo = cmt.contratomodalidade \n");
                            sql.append(" where cm.contrato =").append(contratoBaseado).append(") \n");
                        }

                        sql.append(" order by h.horainicial ");
                        PreparedStatement pst = connection.prepareStatement(sql.toString());

                        ResultSet rs = pst.executeQuery();
                        TurnoEnum turno = TurnoEnum.turno(periodo);
                        DisponibilidadeTurmaEnum dispEnum = DisponibilidadeTurmaEnum.disponibilidade(disponibilidade);
                        while (rs.next()){
                            boolean capacidadePorCategoria = rs.getBoolean("horariocapacidadeporcategoria");
                            String tipoCategoriaCliente = "";
                            Integer nrMaximoAlunos = rs.getInt("nrmaximoaluno");
                            if (capacidadePorCategoria) {
                                tipoCategoriaCliente = obterTipoCategoriaCliente(cliente, connection);
                                nrMaximoAlunos = obterNrMaximoAlunosPorCategoriaCliente(connection, rs.getInt("codigo"), tipoCategoriaCliente, nrMaximoAlunos);
                            }
                            String inicio = rs.getString("horainicial");
                            if(turno != null && !TurnoEnum.estaNoTurno(inicio, turno)){
                                continue;
                            }
                            int ocupacao;
                            if(rs.getBoolean("bloquearLotacaoFutura")){
                                int ocupacaoHoje = ocupacaoTurma(connection, rs.getInt("codigo"), Calendario.hoje(), cliente, tipoCategoriaCliente, capacidadePorCategoria);
                                int ocupacaoFutura = ocupacaoTurmaFutura(connection, rs.getInt("codigo"), tipoCategoriaCliente, capacidadePorCategoria);
                                ocupacao = ocupacaoHoje + ocupacaoFutura;
                            } else {
                                ocupacao = ocupacaoTurma(connection, rs.getInt("codigo"), dataBase, cliente, tipoCategoriaCliente, capacidadePorCategoria);
                            }
                            boolean cheia = rs.getBoolean("bloquearmatriculasacimalimite")
                                    && nrMaximoAlunos <= ocupacao;
                            if (DisponibilidadeTurmaEnum.disponiveis.equals(dispEnum) && cheia){
                                continue;
                            }
                            if (DisponibilidadeTurmaEnum.indisponiveis.equals(dispEnum) && !cheia){
                                continue;
                            }
                            Map<DiaSemana, List<HorarioTurmaAgendaDTO>> aulasNoHorario = agenda.get(inicio);
                            if(aulasNoHorario == null){
                                aulasNoHorario = new HashMap<>();
                                agenda.put(inicio, aulasNoHorario);
                            }

                            DiaSemana diaSemana = DiaSemana.getDiaSemana(rs.getString("diasemana"));
                            List<HorarioTurmaAgendaDTO> aulas = aulasNoHorario.get(diaSemana);
                            if(aulas == null){
                                aulas = new ArrayList<>();
                                aulasNoHorario.put(diaSemana, aulas);
                            }
                            HorarioTurmaAgendaDTO horario = new HorarioTurmaAgendaDTO();
                            horario.setCodigo(rs.getInt("codigo"));
                            if(contratoBaseado != null){
                                horario.setDiaSemana(diaSemana.getDescricaoSimples());
                            }
                            horario.setModalidade(rs.getInt("modalidade"));
                            horario.setInicio(inicio);
                            horario.setBloquearMatriculasAcimaLimite(rs.getBoolean("bloquearmatriculasacimalimite"));
                            horario.setFim(rs.getString("horafinal"));
                            horario.setColetiva(rs.getBoolean("aulacoletiva"));
                            horario.setCapacidade(nrMaximoAlunos);
                            horario.setOcupacao(ocupacao);
                            horario.setIdadeMaximaMeses(rs.getInt("idademaximameses") + (rs.getInt("idademaxima") * 12));
                            horario.setIdadeMinimaMeses(rs.getInt("idademinimameses") + (rs.getInt("idademinima") * 12));
                            horario.setTurma(rs.getString("turma"));
                            horario.setAmbiente(rs.getString("ambiente"));
                            horario.setProfessor(rs.getString("professor"));
                            horario.setNivelCodigoMgb(rs.getString("nivelcodigomgb") == null || "0".equals(rs.getString("nivelcodigomgb")) ? "" : rs.getString("nivelcodigomgb"));
                            String niveis = rs.getString("niveis");
                            horario.setNiveis(new ArrayList<>());
                            if(!UteisValidacao.emptyString(niveis)){
                                Arrays.asList(niveis.split(",")).forEach(n -> {
                                    try {
                                        int i = Integer.parseInt(n);
                                        if(i > 0){
                                            horario.getNiveis().add(i);
                                        }
                                    }catch (Exception e){
                                        e.printStackTrace();
                                    }

                                });
                            }
                            aulas.add(horario);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        List<AgendaTurmaDTO> agendaList = new ArrayList<>();
        for(String horario : agenda.keySet()){
            AgendaTurmaDTO agendaTurmaDTO = new AgendaTurmaDTO();
            agendaTurmaDTO.setHorario(horario);
            agendaTurmaDTO.setAulasDiaSemana(agenda.get(horario));
            agendaList.add(agendaTurmaDTO);
        }
        return Ordenacao.ordenarLista(agendaList, "horario");
    }

    private Integer obterNrMaximoAlunosPorCategoriaCliente(Connection connection, Integer horarioTurma, String tipoCategoriaCliente, Integer nrMaximoAlunos) {
        Integer maxCapacidadePorCateg = nrMaximoAlunos;
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select capacidade from horariocapacidadecategoria \n");
            sql.append("where horarioturma = ").append(horarioTurma);
            sql.append(" and tipocategoria = '").append(tipoCategoriaCliente).append("'");
            PreparedStatement pst1 = connection.prepareStatement(sql.toString());
            ResultSet rs1 = pst1.executeQuery();
            if (rs1.next()) {
                maxCapacidadePorCateg = rs1.getInt("capacidade");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return maxCapacidadePorCateg;
    }

    private String obterTipoCategoriaCliente(Integer cliente, Connection connection) throws Exception {
        String tipoCategoriaCliente = "";
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select ct.tipoCategoria from cliente c \n");
            sql.append("inner join categoria ct on ct.codigo = c.categoria \n");
            sql.append("where c.codigo = ").append(cliente);
            PreparedStatement pst1 = connection.prepareStatement(sql.toString());
            ResultSet rs1 = pst1.executeQuery();
            if (rs1.next()) {
                tipoCategoriaCliente = rs1.getString("tipoCategoria");
            }
            if (UteisValidacao.emptyString(tipoCategoriaCliente)) {
                throw new Exception("O tipo de categoria não encontrado no cliente");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException("Erro ao obter tipo de categoria do cliente: " + ex.getMessage());
        }
        return tipoCategoriaCliente;
    }

    private Integer ocupacaoTurma(Connection connection, int horarioTurma, Date dataBase, Integer cliente, String tipoCategoriaCliente, boolean capacidadePorCategoria) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select count(m.codigo) as qtde from matriculaalunohorarioturma m ");
        sql.append("  inner join cliente clim on clim.pessoa = m.pessoa ");
        if (capacidadePorCategoria) {
            sql.append(" inner join categoria cat on cat.codigo = clim.categoria ");
        }
        sql.append("  where '").append(Calendario.getDataAplicandoFormatacao(dataBase, "yyyy-MM-dd"));
        sql.append("' between datainicio and datafim and clim.codigo <> ").append(cliente == null ? 0 : cliente);
        sql.append("  and horarioturma = ").append(horarioTurma);
        if (capacidadePorCategoria) {
            sql.append(" and cat.tipocategoria = '" + tipoCategoriaCliente + "'");
        }

        try (Statement stm = connection.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                return rs.next() ? rs.getInt("qtde") : 0;
            }
        }
    }

    private Integer ocupacaoTurmaFutura(Connection connection, int horarioTurma, String tipoCategoriaCliente, boolean capacidadePorCategoria) throws Exception{
        String dataConsulta = Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(mt.codigo) AS qtde FROM horarioturma ht \n");
        sql.append("LEFT OUTER JOIN matriculaalunohorarioturma mt ON ht.codigo = mt.horarioturma \n");
        if (capacidadePorCategoria) {
            sql.append("LEFT OUTER JOIN cliente clim on clim.pessoa = mt.pessoa ");
            sql.append("LEFT OUTER JOIN categoria cat on cat.codigo = clim.categoria ");
        }
        sql.append("WHERE mt.horarioturma = ").append(horarioTurma).append(" AND '");
        sql.append(dataConsulta);
        sql.append("' < mt.datainicio \n");
        if (capacidadePorCategoria) {
            sql.append(" AND cat.tipocategoria = 'CO' \n");
        }
        sql.append(" AND mt.pessoa not in (\n");
        sql.append(" SELECT mtp.pessoa FROM horarioturma htp \n");
        sql.append("inner JOIN matriculaalunohorarioturma mtp ON htp.codigo = mtp.horarioturma \n");
        if (capacidadePorCategoria) {
            sql.append(" inner join cliente clim on clim.pessoa = mtp.pessoa \n");
            sql.append(" inner join categoria cat on cat.codigo = clim.categoria \n");
        }
        sql.append("WHERE mtp.horarioturma = ").append(horarioTurma);
        if (capacidadePorCategoria) {
            sql.append(" AND cat.tipocategoria = 'CO' \n");
        }
        sql.append(" AND '");
        sql.append(dataConsulta);
        sql.append("' BETWEEN mtp.datainicio AND mtp.datafim )");
        try (Statement stm = connection.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getInt("qtde");
                } else {
                    return 0;
                }
            }
        }
    }

    public Integer contratoEPlano(Integer plano, Integer contratoBaseado) throws Exception {
        AtomicReference<Integer> contratoEPlano = new AtomicReference<>(0);
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(con -> {
                    try {
                        PreparedStatement pst = con.prepareStatement("select c.codigo from contrato c \n" +
                                " where c.plano = " + plano +
                                " and c.codigo = " + contratoBaseado );
                        ResultSet rs = pst.executeQuery();
                        if (rs.next()) {
                            contratoEPlano.set(rs.getInt("codigo"));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return contratoEPlano.get();
    }

    public List<PlanoProdutoDTO> produtodosPorPlano(Integer plano,
                                                    SituacaoContratoEnum tipoContrato) throws Exception {
        List<PlanoProdutoDTO> lista = new ArrayList<>();
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(con -> {
                    try {
                        PreparedStatement pst = con.prepareStatement("select p.codigo as codigoproduto, " +
                                "p.descricao, p.tipoproduto, p.valorfinal,\n" +
                                "pp.* from planoprodutosugerido pp \n" +
                                "inner join produto p on pp.produto = p.codigo\n" +
                                "where ativoplano and plano = " + plano +
                                " order by p.descricao");
                        ResultSet rs = pst.executeQuery();
                        while (rs.next()) {
                            String tipoproduto = rs.getString("tipoproduto");
                            PlanoProdutoDTO obj = montarPlanoProduto(rs, tipoproduto);
                            if (!TipoProdutoEnum.produtoAdesao(tipoproduto)) {
                                lista.add(obj);
                                continue;
                            }
                            TipoProdutoEnum tipoProduto = TipoProdutoEnum.getTipoProdutoCodigo(tipoproduto);
                            switch (tipoContrato) {
                                case MA:
                                    if (tipoProduto == TipoProdutoEnum.MATRICULA && obj.getTipoProduto().equals(TipoProdutoEnum.MATRICULA.getCodigo())) {
                                        obj.setObrigatorio(true);
                                        lista.add(obj);
                                    }
                                    if (tipoProduto == TipoProdutoEnum.TAXA_DE_ADESAO_PLANO_RECORRENCIA && obj.getTipoProduto().equals(TipoProdutoEnum.TAXA_DE_ADESAO_PLANO_RECORRENCIA.getCodigo())) {
                                        obj.setObrigatorio(true);
                                        lista.add(obj);
                                    }
                                    break;

                                case RE:
                                    if (tipoProduto == TipoProdutoEnum.REMATRICULA && obj.isObrigatorio()) {
                                        lista.add(obj);
                                    }
                                    if (tipoProduto == TipoProdutoEnum.TAXA_DE_ADESAO_PLANO_RECORRENCIA && obj.getTipoProduto().equals(TipoProdutoEnum.TAXA_DE_ADESAO_PLANO_RECORRENCIA.getCodigo())) {
                                        obj.setObrigatorio(true);
                                        lista.add(obj);
                                    }
                                    break;

                                case RN:
                                    if (tipoProduto == TipoProdutoEnum.RENOVACAO && obj.isObrigatorio()) {
                                        obj.setObrigatorio(true);
                                        lista.add(obj);
                                    }
                                    break;
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return lista;
    }
}