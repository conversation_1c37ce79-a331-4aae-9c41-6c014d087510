package com.pacto.adm.core.dao.interfaces.vinculo;

import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Vinculo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface VinculoRepository extends JpaRepository<Vinculo, Integer> {

    Vinculo findByTipoVinculoAndCliente_Codigo(String tipoVinculo, Integer codigoCliente);
    List<Vinculo> findByCliente_CodigoAndTipoVinculoNotLike(Integer codigoCliente, String tipoVinculo);

    List<Vinculo> findByCliente(Cliente cliente);

    boolean existsAllByCodigoIn(List<Integer> vinculos);

    /**
     * Remove os vínculos do cliente em que é o mesmo colaborador porém não é consultor
     *
     * @param codCliente
     * @param codColaboradorConsultor: Código do novo consultor
     */
    @Modifying
    @Query("DELETE FROM Vinculo v\n" +
            "WHERE v.cliente.codigo = :codCliente\n" +
            "AND v.tipoVinculo != 'CO'\n" +
            "AND v.colaborador.codigo = :codColaboradorConsultor")
    void removeVinculoNaoConsultor(
            @Param("codCliente") Integer codCliente,
            @Param("codColaboradorConsultor") Integer codColaboradorConsultor
    );

    /**
     * Remove os vínculos do cliente em que é o mesmo colaborador porém é consultor
     *
     * @param codCliente
     * @param codColaboradorConsultor: Código do novo consultor
     */
    @Modifying
    @Query("DELETE FROM Vinculo v\n" +
            "WHERE v.cliente.codigo = :codCliente\n" +
            "AND v.tipoVinculo = 'CO'\n" +
            "AND v.colaborador.codigo = :codColaboradorConsultor")
    void removeVinculoConsultor(
            @Param("codCliente") Integer codCliente,
            @Param("codColaboradorConsultor") Integer codColaboradorConsultor
    );
}
