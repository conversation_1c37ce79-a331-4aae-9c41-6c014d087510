package com.pacto.adm.core.dao.interfaces;

import com.pacto.adm.core.entities.ConfiguracaoIntegracaoAcessoPratique;
import com.pacto.config.exceptions.ServiceException;

public interface ConfiguracaoIntegracaoEnvioAcessoPratiqueDao extends DaoGenerico<ConfiguracaoIntegracaoAcessoPratique, Integer> {

    ConfiguracaoIntegracaoAcessoPratique findByEmpresaId(Integer empresaId) throws ServiceException;

}
