package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ConfigTotalPassDao;
import com.pacto.adm.core.entities.ConfigTotalPass;
import org.springframework.stereotype.Repository;
import javax.persistence.NoResultException;
import javax.persistence.Query;


@Repository
public class ConfigTotalPassDaoImpl extends  DaoGenericoImpl<ConfigTotalPass, Integer> implements ConfigTotalPassDao {

    @Override
    public ConfigTotalPass findByEmpresaId(Integer empresaId) {
        getCurrentSession().clear();
        StringBuilder sql = new StringBuilder("SELECT obj FROM ConfigTotalPass obj WHERE obj.empresa_codigo = :empresaId");
        Query query = getCurrentSession().createQuery(sql.toString());
        query.setParameter("empresaId", empresaId);

        ConfigTotalPass configTotalPass = null;
        try {
            configTotalPass = (ConfigTotalPass) query.getSingleResult();
        } catch (NoResultException e) {
            configTotalPass = new ConfigTotalPass();
            configTotalPass.setCodigoTotalPass("");
            configTotalPass.setEmpresa_codigo(empresaId);
            configTotalPass.setApikey("");
            configTotalPass.setLimiteDeAcessosPorDia(0);
            configTotalPass.setLimiteDeAulasPorDia(0);
            configTotalPass.setPermitirWod(false);
            configTotalPass.setInativo(true);
            return configTotalPass;
        }

        return configTotalPass;
    }
}
