package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ConfiguracaoEmpresaBitrix24Dao;
import com.pacto.adm.core.entities.ConfiguracaoEmpresaBitrix24;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.Map;

@Repository
public class ConfiguracaoEmpresaBitrix24DaoImpl extends DaoGenericoImpl<ConfiguracaoEmpresaBitrix24, Integer> implements ConfiguracaoEmpresaBitrix24Dao {

    @Override
    public ConfiguracaoEmpresaBitrix24 findByEmpresaChave(String chave) {
        getCurrentSession().clear();

        Map<String, Object> params = new HashMap<>();
        StringBuilder s = new StringBuilder("SELECT obj FROM ConfiguracaoEmpresaBitrix24 obj ");
        s.append(" WHERE obj.empresa = :empresa ");
        params.put("empresa", chave);
        Query query = getCurrentSession().createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        ConfiguracaoEmpresaBitrix24 configEntity = null;
        try {
            configEntity = (ConfiguracaoEmpresaBitrix24) query.getSingleResult();
        } catch (Exception e) {
            // Trate a exceção ou retorne null, dependendo dos requisitos do seu aplicativo.
        }
        return configEntity;
    }
}
