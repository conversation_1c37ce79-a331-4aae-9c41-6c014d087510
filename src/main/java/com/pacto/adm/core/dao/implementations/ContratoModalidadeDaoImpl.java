package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.ContratoModalidadeDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.contrato.ContratoModalidade;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ContratoModalidadeDaoImpl extends DaoGenericoImpl<ContratoModalidade, Integer> implements ContratoModalidadeDao {
    private static final int MAXIMO_RESULTADOS = 10;

    public List<ContratoModalidade> findAllByContrato(Integer codigoContrato, PaginadorDTO paginadorDTO) throws Exception {
        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append("WHERE obj.contrato.codigo = :codigoContrato");
        params.put("codigoContrato", codigoContrato);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + sortOrder);
                }
            } else {
                where.append(" order by obj.codigo desc");
            }
        } else {
            where.append(" order by obj.codigo desc");
        }

        return findByParam(where, params);
    }

}
