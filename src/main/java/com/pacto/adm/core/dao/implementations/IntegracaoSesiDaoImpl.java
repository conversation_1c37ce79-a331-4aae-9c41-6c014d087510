package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.IntegracaoSesiDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.filtros.FiltroIntegracaoSesiJSON;
import com.pacto.adm.core.entities.sesi.IntegracaoSesi;
import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class IntegracaoSesiDaoImpl extends DaoGenericoImpl<IntegracaoSesi, Integer> implements IntegracaoSesiDao {

    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<IntegracaoSesi> findAll(FiltroIntegracaoSesiJSON filtros, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder select = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        select.append("SELECT \n");
        select.append(" new IntegracaoSesi(obj.codigo, obj.dataRequisicao, obj.funcionalidade, obj.messageID, obj.status, obj.dataRetorno, obj.resultado, obj.xmlEnviado, obj.resposta, ");
        select.append(" coalesce(pes.nome, '-') as aluno , \n");
        select.append(" obj.codigoEmpresa, e.nome as nomeEmpresa)");
        select.append(" FROM IntegracaoSesi obj \n");
        where.append(" LEFT JOIN Pessoa pes ON pes.codigo = obj.codigoPessoa \n");
        where.append(" LEFT JOIN Empresa e ON e.codigo = obj.codigoEmpresa \n");
        where.append(" WHERE 1 = 1 \n");

        if (Uteis.notNullAndNotEmpty(filtros.getParametro())) {
            where.append(" AND upper(pes.nome) LIKE concat('%',:aluno,'%') \n");
            params.put("aluno", filtros.getParametro());
        }

        if(!UteisValidacao.emptyList(filtros.getListaEmpresa())){
            where.append(" AND obj.codigoEmpresa in(:codsEmpresa) ");
            params.put("codsEmpresa", filtros.getListaEmpresa());
        }

        if(!UteisValidacao.emptyList(filtros.getListaStatus())){
            where.append(" AND obj.status in(:status) ");
            params.put("status", filtros.getListaStatus());
        }

        if(!UteisValidacao.emptyList(filtros.getListaTipoSolicitacao())){
            where.append(" AND obj.funcionalidade in(:funcionalidades) ");
            params.put("funcionalidades", filtros.getListaTipoSolicitacao());
        }

        if (filtros.getDataInicial() != null && filtros.getDataFinal() != null)  {
            where.append(" AND obj.dataRequisicao BETWEEN :dataInicial AND :dataFinal ");
            params.put("dataInicial", filtros.getDataInicial().getTime());
            params.put("dataFinal", filtros.getDataFinal().getTime());
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("dtRequisicao")) {
                    where.append(" order by obj.dataRequisicao " + sortOrder);
                } else if (sortField.equalsIgnoreCase("aluno")) {
                    where.append(" order by aluno " + sortOrder);
                } else if (sortField.equalsIgnoreCase("tipoSolicitacao")) {
                    where.append(" order by obj.funcionalidade " + sortOrder);
                } else if (sortField.equalsIgnoreCase("id")) {
                    where.append(" order by obj.messageID " + sortOrder);
                } else if (sortField.equalsIgnoreCase("statusDescricao")) {
                    where.append(" order by obj.status " + sortOrder);
                } else if (sortField.equalsIgnoreCase("dtRetorno")) {
                    where.append(" order by obj.dataRetorno " + sortOrder);
                } else if (sortField.equalsIgnoreCase("resultado")) {
                    where.append(" order by obj.resultado " + sortOrder);
                }
            } else {
                where.append(" order by obj.codigo desc ");
            }
        } else {
            where.append(" order by obj.codigo desc ");
        }

        StringBuilder hqlFinal = new StringBuilder();
        hqlFinal.append(select);
        hqlFinal.append(where);

        Query q = getCurrentSession().createQuery(hqlFinal.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                q.setParameter(p, params.get(p));
            }
        }

        if (maxResults != 0)
            q.setMaxResults(maxResults);
        if (indiceInicial != 0)
            q.setFirstResult(indiceInicial);

        return q.getResultList();
    }
}
