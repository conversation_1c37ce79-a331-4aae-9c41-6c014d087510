package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.PeriodoAcessoClienteDao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.contrato.PeriodoAcessoCliente;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.Uteis;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.sql.ResultSet;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class PeriodoAcessoClienteDaoImpl extends DaoGenericoImpl<PeriodoAcessoCliente, Integer> implements PeriodoAcessoClienteDao {

    private static final int MAXIMO_RESULTADOS = 10;

    @Override
    public List<PeriodoAcessoCliente> consultarPorContrato(Integer codigoContrato) throws Exception {
        if (getCurrentSession() != null) {
            getCurrentSession().clear();
        }

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append(" WHERE obj.contrato = :codigoContrato");
        params.put("codigoContrato", codigoContrato);
        where.append(" ORDER BY obj.codigo DESC ");

        return findByParam(where, params);
    }

    @Override
    public PeriodoAcessoCliente obterUltimoDiaPeriodoAcessoContrato(Integer codigoContrato) throws Exception {
        StringBuilder hql = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        hql.append("SELECT obj FROM PeriodoAcessoCliente obj ");
        hql.append(" WHERE obj.dataFinalAcesso IN (SELECT MAX(dataFinalAcesso) FROM PeriodoAcessoCliente WHERE contrato.codigo = :codigoContrato ) ");
        hql.append(" AND obj.contrato.codigo = :codigoContrato ");
        params.put("codigoContrato", codigoContrato);

        Query query = getCurrentSession().createQuery(hql.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        if (query.getResultList().size() == 0) {
            return null;
        } else {
            return (PeriodoAcessoCliente) query.getSingleResult();
        }
    }

    @Override
    public List<PeriodoAcessoCliente> consultarPorVigenteOuFuturoContrato(Date dataInicio, Integer codigoContrato) throws Exception {
        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append(" WHERE obj.contrato.codigo = :codigoContrato");
        where.append(" AND (obj.dataInicioAcesso <= '").append(Uteis.getDataJDBC(dataInicio)).append("' AND obj.dataFinalAcesso >= '").append(Uteis.getDataJDBC(dataInicio)).append("')");
        where.append(" OR dataInicioAcesso > '").append(Uteis.getDataJDBC(dataInicio)).append("' ");
        where.append(" ORDER BY obj.dataInicioAcesso DESC ");

        params.put("codigoContrato", codigoContrato);

        return findByParam(where, params);
    }

    @Override
    public boolean existePeriodoAcessoTotalpassVigente(Integer codPessoa) throws Exception {
        getCurrentSession().clear();
        Map<String, Object> params = new HashMap<>();
        String data = Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd");
        StringBuilder where = new StringBuilder();

        where.append("WHERE  obj.pessoa = ").append(codPessoa).append(" and obj.dataInicioAcesso >= '").append(data).append("' and obj.dataFinalAcesso <= '").append(data).append("' and obj.tipototalpass = true ");

        List<PeriodoAcessoCliente> lista = findByParam(where, params);

        return !lista.isEmpty();

    }


    @Override
    public List<PeriodoAcessoCliente> consultarPorDataInicioContrato(Date dataInicio, Integer codigoContrato) throws Exception {
        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append(" WHERE (obj.dataInicioAcesso > '").append(Uteis.getDataJDBC(dataInicio)).append("') ");
        where.append(" AND obj.contrato.codigo = :codigoContrato");
        where.append(" ORDER BY obj.dataInicioAcesso ");

        params.put("codigoContrato", codigoContrato);

        return findByParam(where, params);
    }

    @Override
    public List<PeriodoAcessoCliente> findByPessoa(Integer codPessoa, boolean gymPass, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append(" WHERE obj.pessoa = :codPessoa");
        if (gymPass) {
            where.append(" AND obj.tokenGympass IS NOT NULL");
            where.append(" AND obj.tokenGympass <> ''");
        }

        params.put("codPessoa", codPessoa);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + sortOrder);
                } else if (sortField.equalsIgnoreCase("dataInicio")) {
                    where.append(" order by obj.dataInicioAcesso " + sortOrder);
                } else if (sortField.equalsIgnoreCase("tokenGympass")) {
                    where.append(" order by obj.tokenGympass " + sortOrder);
                } else if (sortField.equalsIgnoreCase("valorGympass")) {
                    where.append(" order by obj.valorGympass " + sortOrder);
                }
            } else {
                where.append(" order by obj.codigo desc ");
            }
        } else {
            where.append(" order by obj.codigo desc ");
        }

        return findByParam(where, params);
    }

    @Override
    public List<PeriodoAcessoCliente> findByPessoaGoGood(Integer codPessoa, PaginadorDTO paginadorDTO) throws Exception {
        getCurrentSession().clear();

        int maxResults = MAXIMO_RESULTADOS;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append(" WHERE obj.pessoa = :codPessoa");
        where.append(" AND obj.tokenGogood IS NOT NULL");
        where.append(" AND obj.tokenGogood <> ''");

        params.put("codPessoa", codPessoa);

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam("obj.codigo", where, params).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            if (paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String sortField = paginadorDTO.getSort().split(",")[0];
                String sortOrder = paginadorDTO.getSort().split(",")[1];
                if (sortField.equalsIgnoreCase("codigo")) {
                    where.append(" order by obj.codigo " + sortOrder);
                } else if (sortField.equalsIgnoreCase("dataInicio")) {
                    where.append(" order by obj.dataInicioAcesso " + sortOrder);
                } else if (sortField.equalsIgnoreCase("tokenGogood")) {
                    where.append(" order by obj.tokenGogood " + sortOrder);
                }
            } else {
                where.append(" order by obj.codigo desc ");
            }
        } else {
            where.append(" order by obj.codigo desc ");
        }

        return findByParam(where, params);
    }

    public List<PeriodoAcessoCliente> findByClienteAndDataGreaterThanAndTipoPeriodoAcesso(
            Cliente cliente, Date date, String tipoPeriodoAcesso) throws Exception {

        StringBuilder where = new StringBuilder();
        where.append("WHERE obj.pessoa = :codPessoa\n");
        where.append("AND obj.tipoAcesso = :tipoAcesso\n");
        where.append("AND (obj.dataInicioAcesso >= :data or obj.dataFinalAcesso >= :data)\n");

        Map<String, Object> params = new HashMap<>();
        params.put("codPessoa", cliente.getPessoa().getCodigo());
        params.put("tipoAcesso", tipoPeriodoAcesso);
        params.put("data", date);

        return findByParam(where, params);
    }

}
