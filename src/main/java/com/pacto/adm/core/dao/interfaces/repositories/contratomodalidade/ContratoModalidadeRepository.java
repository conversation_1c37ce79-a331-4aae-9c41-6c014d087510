package com.pacto.adm.core.dao.interfaces.repositories.contratomodalidade;

import com.pacto.adm.core.entities.contrato.ContratoModalidade;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ContratoModalidadeRepository extends JpaRepository<ContratoModalidade, Integer> {

    boolean existsByModalidade_Codigo(Integer modalidadeCodigo);

}
