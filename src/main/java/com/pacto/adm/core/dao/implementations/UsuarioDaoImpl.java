package com.pacto.adm.core.dao.implementations;

import com.pacto.adm.core.dao.interfaces.UsuarioDao;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.filtros.FiltroUsuarioJSON;
import com.pacto.adm.core.dto.negociacao.AgendaTurmaDTO;
import com.pacto.adm.core.dto.negociacao.HorarioTurmaAgendaDTO;
import com.pacto.adm.core.entities.Usuario;
import com.pacto.config.enumerador.DiaSemana;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Uteis;
import org.hibernate.engine.spi.SessionImplementor;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

@Repository
public class UsuarioDaoImpl extends DaoGenericoImpl<Usuario, Integer> implements UsuarioDao {
    @Override
    public List<Usuario> findAll(FiltroUsuarioJSON filtros) throws Exception {
        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append(" WHERE 1 = 1 ");
        if (filtros != null) {
            if (Uteis.notNullAndNotEmpty(filtros.getParametro())) {
                where.append("AND upper(obj.nome) like CONCAT('%', :nome, '%')\n");
                params.put("nome", filtros.getParametro());
            }
            if (!Uteis.intNullOrEmpty(filtros.getCodigo())) {
                where.append(" AND obj.codigo = :codigo ");
                params.put("codigo", filtros.getCodigo());
            }
        }
        return findByParam(where, params);
    }

    public List<UsuarioDTO> consultarSimples(Integer empresa) throws ServiceException {
        List<UsuarioDTO> usuarios = new ArrayList<>();
        try {
            try (SessionImplementor sessionImplementor = createSessionCurrentWork()) {
                sessionImplementor.doWork(connection -> {
                    String sql = "SELECT u.codigo, u.nome FROM usuario u " +
                            "INNER JOIN usuarioperfilacesso upa ON upa.usuario = u.codigo " +
                            "WHERE upa.empresa = ? ORDER BY u.nome";
                    try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                        stmt.setInt(1, empresa);
                        try (ResultSet rs = stmt.executeQuery()) {
                            while (rs.next()) {
                                UsuarioDTO dto = new UsuarioDTO();
                                dto.setCodigo(rs.getInt("codigo"));
                                dto.setNome(rs.getString("nome"));
                                usuarios.add(dto);
                            }
                        }
                    }
                });
            }
            return usuarios;
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    @Override
    public List<Usuario> consultarUsuariosMeta(FiltroUsuarioJSON filtros) throws Exception {
        getCurrentSession().clear();

        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        where.append(" INNER JOIN Colaborador col ON col.codigo = obj.colaborador.codigo");
        where.append(" INNER JOIN UsuarioPerfilAcesso upa ON upa.usuario.codigo = obj.codigo ");
        where.append(" INNER JOIN Colaborador colEmp ON colEmp.pessoa.codigo = col.pessoa.codigo AND colEmp.empresa.codigo = upa.empresa ");

        where.append(" WHERE upa.empresa = :empresa ");
        params.put("empresa", filtros.getCodigoEmpresa());
        where.append(" AND colEmp.situacao = 'AT' ");
        where.append(" AND (SELECT count(*) FROM Vinculo v WHERE v.colaborador.codigo = colEmp.codigo) > 0 ");
        if (filtros != null) {
            if (Uteis.notNullAndNotEmpty(filtros.getParametro())) {
                where.append("AND upper(obj.nome) like CONCAT('%', :nome, '%')\n");
                params.put("nome", filtros.getParametro());
            }
        }
        return findByParam(where, params);
    }
}
