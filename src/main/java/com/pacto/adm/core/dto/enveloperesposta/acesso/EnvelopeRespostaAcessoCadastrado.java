package com.pacto.adm.core.dto.enveloperesposta.acesso;

import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Atestado", description = "Representação das respostas contendo as informações do atestado")
public class EnvelopeRespostaAcessoCadastrado {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas", example = "Registro efetuado com sucesso!")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public static final String resposta = "{\"content\": \"Registro efetuado com sucesso!\"}";

}
