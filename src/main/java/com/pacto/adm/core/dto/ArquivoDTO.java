package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Arquivo", description = "Informações do arquivo")
public class ArquivoDTO {

    @Schema(description = "Código único identificador do arquivo", example = "193")
    private Integer codigo;

    @Schema(description = "Tipo do arquivo", example = "IMAGEM")
    private String tipo;

    @Schema(description = "Nome do arquivo", example = "atestado")
    private String nome;
    @Schema(description = "Extensão do arquivo", example = "png")
    private String extensao;

    @Schema(description = "Código da pessoa vinculada ao arquivo", example = "1029")
    private Integer pessoa;

    @Schema(description = "Dados do arquivo", example = "Dados da imagem")
    private String dados;
    @Schema(description = "Observação dos arquivo", example = "Atestado médico")
    private String observacao;
    @Schema(description = "Chave da foto do arquivo", example = "fotokey_1239084")
    private String fotoKey;
    @Schema(description = "URL completa do arquivo", example = "www.pactosolucoes.com.br/arquivos/atestadomedico.png")
    private String urlFull;

    @Schema(description = "Data do registro do arquivo no sistema", example = "1744122600000")
    private Long dataRegistro;

    @Schema(description = "Código da solicitação de compra", example = "1")
    private Integer solicitacaoCompra;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getExtensao() {
        return extensao;
    }

    public void setExtensao(String extensao) {
        this.extensao = extensao;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getDados() {
        return dados;
    }

    public void setDados(String dados) {
        this.dados = dados;
    }

    public void splitNomeExtensao() {
        if (this.nome != null && this.nome.contains(".")) {
            String[] parts = this.nome.split("\\.");
            this.nome = parts[0];
            this.extensao = parts[1];
        }
    }

    public Long getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Long dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getUrlFull() {
        return urlFull;
    }

    public void setUrlFull(String urlFull) {
        this.urlFull = urlFull;
    }

    public Integer getSolicitacaoCompra() {
        return solicitacaoCompra;
    }

    public void setSolicitacaoCompra(Integer solicitacaoCompra) {
        this.solicitacaoCompra = solicitacaoCompra;
    }
}
