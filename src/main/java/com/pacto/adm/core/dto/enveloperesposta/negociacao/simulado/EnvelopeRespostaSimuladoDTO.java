package com.pacto.adm.core.dto.enveloperesposta.negociacao.simulado;

import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaGrupoDescontoDTO;
import com.pacto.adm.core.dto.enveloperesposta.desconto.EnvelopeRespostaDescontoDTO;
import com.pacto.adm.core.dto.enveloperesposta.modalidade.contrato.EnvelopeRespostaContratoModalidadeWS;
import com.pacto.adm.core.dto.enveloperesposta.modalidade.valor.EnvelopeRespostaValorModalidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.parcelas.arredondamento.EnvelopeRespostaArredondamentoParcelaDTO;
import com.pacto.adm.core.dto.enveloperesposta.parcelas.editarnegociacao.EnvelopeRespostaParcelasEditarNegociacaoNovoDTO;
import com.pacto.adm.core.dto.enveloperesposta.produto.ws.EnvelopeRespostaProdutoWS;
import com.pacto.adm.core.dto.negociacao.SimuladoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaSimuladoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private SimuladoDTO content;

    public SimuladoDTO getContent() {
        return content;
    }

    public void setContent(SimuladoDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 1001, "
                    + "\"codigoCliente\": 123, "
                    + "\"inicio\": 1622505600000, "
                    + "\"fim\": 1654041600000, "
                    + "\"lancamento\": 1622505600000, "
                    + "\"valorFinal\": 1200.00, "
                    + "\"valorBase\": 1000.00, "
                    + "\"valorPlano\": 950.00, "
                    + "\"valorAcrescimos\": 50.00, "
                    + "\"descontosConvenio\": 30.00, "
                    + "\"descontosExtra\": 20.00, "
                    + "\"descontos\": 50.00, "
                    + "\"contratoEnviado\": true, "
                    + "\"reciboEnviado\": false, "
                    + "\"situacaoContrato\": \"EM ANDAMENTO\", "
                    + "\"dataLancamento\": \"2025-05-01\", "
                    + "\"vigenciaDe\": \"2025-05-01\", "
                    + "\"vigenciaAteAjustada\": \"2026-05-01\", "
                    + "\"situacao\": \"SIMULADO\", "
                    + "\"nomePlano\": \"PLANO PREMIUM\", "
                    + "\"modalidades\": [{" + EnvelopeRespostaContratoModalidadeWS.atributos + "}], "
                    + "\"valoresModalidades\": [{ " + EnvelopeRespostaValorModalidadeDTO.atributos + "}], "
                    + "\"produtos\": [{" + EnvelopeRespostaProdutoWS.atributos + "}], "
                    + "\"valoresArredondados\": [{" + EnvelopeRespostaArredondamentoParcelaDTO.atributos + "}], "
                    + "\"parcelas\": [{" + EnvelopeRespostaParcelasEditarNegociacaoNovoDTO.atributos + "}], "
                    + "\"valorMensal\": 100.00, "
                    + "\"valorAnuidade\": 1200.00, "
                    + "\"diaVencimentoAnuidade\": 10, "
                    + "\"mesVencimentoAnuidade\": \"Dezembro\", "
                    + "\"valorAdesao\": 100.00, "
                    + "\"valorContrato\": 1350.00, "
                    + "\"valorMatricula\": 200.00, "
                    + "\"valorProRata\": 33.33, "
                    + "\"valorPrimeiraParcela\": 150.00, "
                    + "\"codigoPlano\": 5, "
                    + "\"maxVezesParcelarAdesao\": 3, "
                    + "\"msgValidacao\": \"Simulação válida.\", "
                    + "\"numeroMeses\": 12, "
                    + "\"crossfit\": false, "
                    + "\"situacaoSubordinada\": \"RENOVACAO\", "
                    + "\"permiteMarcarAula\": true, "
                    + "\"vendaCreditoTreino\": false, "
                    + "\"nrParcelas\": 6, "
                    + "\"horarioDescricao\": \"SEG-SEX 06:00 - 22:00\", "
                    + "\"permiteRenovar\": true, "
                    + "\"simulacao\": true, "
                    + "\"bolsa\": false, "
                    + "\"maxVezesParcelarProduto\": 2, "
                    + "\"descricaoCobrancaPrimeiraParcela\": \"Cobrança integral no ato.\", "
                    + "\"informacaoRenovacaoCreditoTreino\": \"Renovação automática ativada.\", "
                    + "\"anoCobrancaAnuidade\": 2025, "
                    + "\"permiterenovacaoautomatica\": true, "
                    + "\"renovacaoautomaticasimnao\": true, "
                    + "\"diasCartao\": 5, "
                    + "\"grupo\": {" + EnvelopeRespostaGrupoDescontoDTO.atributos + "}, "
                    + "\"descontoRenovacaoAntecipada\": {" + EnvelopeRespostaDescontoDTO.atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
