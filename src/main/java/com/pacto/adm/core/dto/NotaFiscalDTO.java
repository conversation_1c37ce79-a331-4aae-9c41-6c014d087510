package com.pacto.adm.core.dto;

import com.pacto.adm.core.enumerador.StatusNotaFiscalEnotasEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(name = "Nota Fiscal", description = "Dados da nota fiscal emitida.")
public class NotaFiscalDTO {

    @Schema(description = "Código identificador da nota fiscal.", example = "1001")
    private Integer codigo;

    @Schema(description = "Data de registro da nota fiscal.", example = "2024-05-10T14:30:00Z")
    private Date dataRegistro;

    @Schema(description = "Data de emissão da nota fiscal.", example = "2024-05-10T14:31:00Z")
    private Date dataEmissao;

    @Schema(description = "Data de autorização da nota fiscal.", example = "2024-05-10T14:32:00Z")
    private Date dataAutorizacao;

    @Schema(description = "Status da nota fiscal." +
            "<strong>Valores disponíveis</strong>" +
            "<ul>" +
            "<li>1 - NENHUM (Nenhum)</li>" +
            "<li>2 - GERADA (Gerada)</li>" +
            "<li>3 - AGUARDANDOAUTORIZACAO (Aguardando Autorizacao)</li>" +
            "<li>4 - SOLICITANDOAUTORIZACAO (Solicitando Autorizacao)</li>" +
            "<li>5 - AUTORIZACAOSOLICITADA (Autorizacao Solicitada)</li>" +
            "<li>6 - EMPROCESSODEAUTORIZACAO (Processo De Autorizacao)</li>" +
            "<li>7 - AUTORIZADAAGUARDANDOGERACAOPDF (Aguardando Geracao PDF)</li>" +
            "<li>8 - AUTORIZADA (Autorizado)</li>" +
            "<li>9 - NEGADA (Negada)</li>" +
            "<li>10 - SOLICITANDOCANCELAMENTO (Solicitando Cancelamento)</li>" +
            "<li>11 - CANCELAMENTOSOLICITADO (Cancelamento Solicitado)</li>" +
            "<li>12 - EMPROCESSODECANCELAMENTO (Processo De Cancelamento)</li>" +
            "<li>13 - CANCELADAAGUARDANDOATUALIZACAOPDF (Cancelada Atualizando PDF)</li>" +
            "<li>14 - CANCELADA (Cancelada)</li>" +
            "<li>15 - CANCELAMENTONEGADO (Cancelamento Negado)</li>" +
            "<li>16 - ERRO (Erro na Geração)</li>" +
            "<li>17 - INUTILIZACAOSOLICITADO (Inutilização Solicitada)</li>" +
            "<li>18 - INUTILIZADA (Inutilizada)</li>" +
            "<li>19 - INUTILIZACAONEGADA (Inutilizacao Negada)</li>" +
            "<li>20 - REENVIADA (Reenviada)</li>" +
            "</ul>", example = "AUTORIZADA", implementation = StatusNotaFiscalEnotasEnum.class)
    private String statusNota;

    @Schema(description = "Razão social da empresa emissora.", example = "Academia Pacto Ltda.")
    private String razaoSocial;

    @Schema(description = "CPF ou CNPJ do cliente.", example = "12345678909")
    private String cpfCnpj;

    @Schema(description = "Nome do cliente.", example = "João Silva")
    private String nomeCliente;

    @Schema(description = "Número da nota fiscal.", example = "NF-20240510")
    private String numeroNota;

    @Schema(description = "Chave de acesso da nota fiscal.", example = "35140812345678900123550010000000011000000012")
    private String chaveAcesso;

    @Schema(description = "Número de série da nota fiscal.", example = "1")
    private String serie;

    @Schema(description = "Número do RPS (Recibo Provisório de Serviços).", example = "RPS123456")
    private String rps;

    @Schema(description = "Link para download do PDF da nota fiscal.", example = "https://pactosolucoes.com/notas/1001/pdf")
    private String linkPDF;

    @Schema(description = "Link para download do XML da nota fiscal.", example = "https://pactosolucoes.com/notas/1001/xml")
    private String linkXML;

    @Schema(description = "Tipo de nota fiscal.", example = "1")
    private Integer tipo;

    @Schema(description = "Valor total da nota fiscal.", example = "199.99")
    private Double valor;

    @Schema(description = "Dados da pessoa vinculada à nota fiscal.")
    private PessoaDTO pessoa;

    @Schema(description = "Dados do usuário responsável pela emissão da nota.")
    private UsuarioDTO usuario;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public Date getDataAutorizacao() {
        return dataAutorizacao;
    }

    public void setDataAutorizacao(Date dataAutorizacao) {
        this.dataAutorizacao = dataAutorizacao;
    }

    public String getStatusNota() {
        return statusNota;
    }

    public void setStatusNota(String statusNota) {
        this.statusNota = statusNota;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getCpfCnpj() {
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getNumeroNota() {
        return numeroNota;
    }

    public void setNumeroNota(String numeroNota) {
        this.numeroNota = numeroNota;
    }

    public String getChaveAcesso() {
        return chaveAcesso;
    }

    public void setChaveAcesso(String chaveAcesso) {
        this.chaveAcesso = chaveAcesso;
    }

    public String getSerie() {
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public String getRps() {
        return rps;
    }

    public void setRps(String rps) {
        this.rps = rps;
    }

    public String getLinkPDF() {
        return linkPDF;
    }

    public void setLinkPDF(String linkPDF) {
        this.linkPDF = linkPDF;
    }

    public String getLinkXML() {
        return linkXML;
    }

    public void setLinkXML(String linkXML) {
        this.linkXML = linkXML;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }

    public UsuarioDTO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioDTO usuario) {
        this.usuario = usuario;
    }
}
