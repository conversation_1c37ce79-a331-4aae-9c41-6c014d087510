package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Configuração de Integração com Nuvemshop", description = "Configurações necessárias para integração com a plataforma Nuvemshop.")
public class ConfiguracaoIntegracaoNuvemshopDTO {

    @Schema(description = "Código da empresa associada à configuração.", example = "123")
    private int empresa;

    @Schema(description = "Indica se a integração com a Nuvemshop está habilitada.", example = "true")
    private boolean habilitada;

    @Schema(description = "Indica se a empresa é uma franqueadora na Nuvemshop.", example = "false")
    @JsonProperty("isEmpresaFranqueadora")
    private boolean empresaFranqueadora;

    @Schema(description = "Token de acesso utilizado para autenticação com a Nuvemshop.", example = "abc123tokenXYZ")
    private String accessToken;

    @Schema(description = "Nome do aplicativo configurado para a integração com a Nuvemshop.", example = "meu-app-nuvem")
    private String appName;

    @Schema(description = "E-mail associado à conta da Nuvemshop para autenticação.", example = "<EMAIL>")
    private String email;

    @Schema(description = "Identificador da loja na Nuvemshop.", example = "loja-456")
    private String storeId;

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public boolean isHabilitada() {
        return habilitada;
    }

    public void setHabilitada(boolean habilitada) {
        this.habilitada = habilitada;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public boolean isEmpresaFranqueadora() {
        return empresaFranqueadora;
    }

    public void setEmpresaFranqueadora(boolean empresaFranqueadora) {
        this.empresaFranqueadora = empresaFranqueadora;
    }
}
