package com.pacto.adm.core.dto.enveloperesposta.atestado;

import com.pacto.adm.core.dto.AtestadoMedicoDTO;
import com.pacto.adm.core.dto.enveloperesposta.aulas.EnvelopeRespostaArquivoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaContratoOperacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.EnvelopeRespostaJustificativaOperacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.produto.EnvelopeRespostaMovProdutoDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Atestado", description = "Representação das respostas contendo as informações do atestado")
public class EnvelopeRespostaAtestadoMedicoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private AtestadoMedicoDTO content;

    public AtestadoMedicoDTO getContent() {
        return content;
    }

    public void setContent(AtestadoMedicoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"tipoAtestado\": 291, "
                    + "\"dataLancamento\": \"2025-04-08T14:30:00Z\", "
                    + "\"codigoContrato\": 1, "
                    + "\"vigenciaPorExtenso\": \"do dia oito de abril de dois mil e vinte e cinco até o dia dez de abril de dois mil e vinte e cinco\", "
                    + "\"urlArquivo\": \"www.pactosolucoes.com.br/arquivos/atestado-medico.png\", "
                    + "\"nomeArquivo\": \"atestado-medico\", "
                    + "\"formatoArquivo\": \"IMAGEM\", "
                    + "\"atestadoAptidaoFisica\": {" +
                            "\"codigo\": 1209, "
                            + "\"parqPositivo\": false, "
                            + "\"observacao\": \"22\", "
                            + "\"avaliacaoFisicaTW\": 123, "
                            + "\"arquivo\": {" + EnvelopeRespostaArquivoDTO.atributos + "}, "
                            + "\"movProduto\":  \"INFORMAÇÕES DA MovProduto\", "
                            + "\"data\": \"2025-04-08T14:30:00Z\", "
                            + "\"urlArquivo\": \"www.sistemapacto.com.br/arquivos/atestado.png\""
                    + "}, "
                    + "\"atestadoContrato\": {" +
                            "\"contrato\": {" + EnvelopeRespostaContratoDTO.atributos + "}, "
                            + "\"empresa\": 1234, "
                            + "\"dataInicio\": \"2025-04-08T14:30:00Z\", "
                            + "\"dataTermino\": \"2025-04-08T14:30:00Z\", "
                            + "\"dataInicioRetorno\": \"2025-04-08T14:30:00Z\", "
                            + "\"dataTerminoRetorno\": \"2025-04-08T14:30:00Z\", "
                            + "\"dataRegistro\": \"2025-04-08T14:30:00Z\", "
                            + "\"justificativaOperacao\": {" + EnvelopeRespostaJustificativaOperacaoDTO.atributos + "}, "
                            + "\"nrDias\": 2, "
                            + "\"nrDiasAtestado\": 2, "
                            + "\"nrDiasASomar\": 0, "
                            + "\"observacao\": \"ATESTADO VINCULADO AO CONTRATO\", "
                            + "\"responsavelOperacao\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}, "
                            + "\"apresentarPeriodoAtestado\": true, "
                            + "\"qtdDiasAtestadoMaiorQueContrato\": true, "
                            + "\"contratoVencido\": false, "
                            + "\"contratoOperacao\": {" + EnvelopeRespostaContratoOperacaoDTO.atributos + "}, "
                            + "\"chaveArquivo\": \"1234\", "
                            + "\"nomeArquivo\": \"contrato\", "
                            + "\"formatoArquivo\": \"PDF\", "
                            + "\"dadosArquivo\": \"Contrato\""
                            + "}";

    public final static String requestBody = "{" + atributos + "}";
    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
