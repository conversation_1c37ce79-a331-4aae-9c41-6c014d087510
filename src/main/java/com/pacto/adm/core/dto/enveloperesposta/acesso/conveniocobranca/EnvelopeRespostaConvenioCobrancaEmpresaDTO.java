package com.pacto.adm.core.dto.enveloperesposta.acesso.conveniocobranca;

import com.pacto.adm.core.dto.conveniocobranca.ConvenioCobrancaDTO;
import com.pacto.adm.core.dto.conveniocobranca.ConvenioCobrancaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaConvenioCobrancaEmpresaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConvenioCobrancaEmpresaDTO content;

    public ConvenioCobrancaEmpresaDTO getContent() {
        return content;
    }

    public void setContent(ConvenioCobrancaEmpresaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 3, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}, ";

    public final static String requestBody = "{" + atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
