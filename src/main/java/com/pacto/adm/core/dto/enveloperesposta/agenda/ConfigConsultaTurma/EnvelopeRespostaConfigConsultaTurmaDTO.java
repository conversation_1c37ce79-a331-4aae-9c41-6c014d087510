package com.pacto.adm.core.dto.enveloperesposta.agenda.ConfigConsultaTurma;

import com.pacto.adm.core.dto.ConfigConsultaTurmaDTO;
import com.pacto.adm.core.dto.enveloperesposta.turma.EnvelopeRespostaNivelTurmaDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaConfigConsultaTurmaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfigConsultaTurmaDTO content;

    public ConfigConsultaTurmaDTO getContent() {
        return content;
    }

    public void setContent(ConfigConsultaTurmaDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"professores\": [{" + EnvelopeRespostaPessoaDTO.atributos + "}], " +
                    "\"niveis\": [{" + EnvelopeRespostaNivelTurmaDTO.atributos + "}]";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";

}
