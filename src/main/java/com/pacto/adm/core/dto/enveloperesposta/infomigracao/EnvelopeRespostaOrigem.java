package com.pacto.adm.core.dto.enveloperesposta.infomigracao;

import com.pacto.adm.core.dto.InfoMigracaoDTO;
import com.pacto.adm.core.enumerador.OrigemSistemaEnum;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Respostas Aluguel de Armários", description = "Representação das respostas contendo uma lista de aluguel de armários")
public class EnvelopeRespostaOrigem {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas", example = "ZW", implementation = OrigemSistemaEnum.class)
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
    public final static String resposta = "{\"content\":\"ZW\"}";


}
