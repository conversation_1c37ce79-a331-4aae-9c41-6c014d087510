package com.pacto.adm.core.dto.filtros;

import org.json.JSONException;
import org.json.JSONObject;

public class FiltroClienteRestricaoJSON {
    private String quicksearchValue;

    public FiltroClienteRestricaoJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.quicksearchValue = filters.optString("quicksearchValue").toUpperCase();
        }
    }

    public String getQuicksearchValue() {
        return quicksearchValue;
    }

    public void setQuicksearchValue(String quicksearchValue) {
        this.quicksearchValue = quicksearchValue;
    }

}

