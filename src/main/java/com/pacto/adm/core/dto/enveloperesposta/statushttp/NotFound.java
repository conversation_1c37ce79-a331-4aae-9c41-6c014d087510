package com.pacto.adm.core.dto.enveloperesposta.statushttp;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;


@Schema(
        name = "NotFound", description = "Representação de uma resposta 404 (Não encontrado)",
        example = "{ \"timestamp\": \"1742393075634\", \"status\": 404, \"error\": \"Not Found\", \"path\": \"/caminho-da-requisicao/\" }"
)
public class NotFound {

    @Schema(description = "Horário que o erro foi gerado", example = "1742393075634")
    private Date timestamp;

    @Schema(description = "Informa o código do erro gerado", example ="404" )
    private Integer status;

    @Schema(description = "Descrição do erro", example = "Not Found")
    private String error;

    @Schema(description = "Descreve o endpoint que gerou o erro", example = "/pagamentos/5")
    private String path;

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}
