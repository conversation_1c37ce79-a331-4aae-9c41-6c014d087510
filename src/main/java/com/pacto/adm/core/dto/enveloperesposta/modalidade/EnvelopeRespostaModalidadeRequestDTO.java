package com.pacto.adm.core.dto.enveloperesposta.modalidade;

import com.pacto.adm.core.dto.enveloperesposta.aulas.EnvelopeRespostaArquivoDTO;
import com.pacto.adm.core.dto.enveloperesposta.aulas.EnvelopeRespostaProdutoSugeridoDTO;
import com.pacto.adm.core.dto.modalidade.ModalidadeDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaModalidadeRequestDTO {


    public static final String atributos =
            "\"codigo\": 90, "
                    + "\"nome\": \"Musculação\", "
                    + "\"nrVezes\": 3, "
                    + "\"tipoModalidade\": 1, "
                    + "\"valorMensal\": 150.00, "
                    + "\"ativo\": true, "
                    + "\"utilizarturma\": true, "
                    + "\"fotoKey\": \"modalidade_001.jpg\", "
                    + "\"fotoKeyUrlFull\": \"https://exemplo.com/imagens/modalidade_001.jpg\", "
                    + "\"arquivo\": {" + EnvelopeRespostaArquivoDTO.atributos + "}, "
                    + "\"empresasModalidade\": ["
                    + "{" + EnvelopeRespostaModalidadeEmpresaDTO.atributos + "}"
                    + "], "
                    + "\"produtosSugeridos\": ["
                    + "{" + EnvelopeRespostaProdutoSugeridoDTO.atributos + "}"
                    + "], "
                    + "\"crossfit\": false";

    public final static String requestBody = "{" + atributos + "}";

}
