package com.pacto.adm.core.dto.empresa;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Cidade")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CidadeDTO {
    @Schema(description = "Código único identificador da cidade", example = "1")
    private Integer codigo;

    @Schema(description = "Nome completo da cidade", example = "Goiânia")
    private String nome;

    @Schema(description = "Detalhes do estado ao qual a cidade pertence")
    private EstadoDTO estado;

    public CidadeDTO() {
    }

    public CidadeDTO(Integer codigo, String nome) {
        this.codigo = codigo;
        this.nome = nome;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public EstadoDTO getEstado() {
        return estado;
    }

    public void setEstado(EstadoDTO estado) {
        this.estado = estado;
    }
}
