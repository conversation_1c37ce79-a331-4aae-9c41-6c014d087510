package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.empresa.EmpresaLocalDeAcessoDTO;
import com.pacto.adm.core.entities.Cliente;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor

@Schema(name = "Iniciar Dados de Acesso", description = "Informações para iniciar o cadastro de acesso de forma manual.")
public class InicioDadosAcessoDTO {

    @Schema(description = "Listagem de academias que pode ser feito o acesso")
    private List<EmpresaLocalDeAcessoDTO> listaEmpresas;
    @Schema(description = "Listagem de locais que pode ser feito o acesso")
    private List<LocalDeAcessoDTO> listaLocalDeAcesso;
    @Schema(description = "Informações do cliente necessárias para realizar o acesso.")
    private ClienteLocalDeAcessoDTO cliente;

    @Schema(description = "Data de acesso à academia", example = "2025-04-28")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT-3")
    private Date dataDeAcesso;

    @Schema(description = "Data de saída da academia", example = "2025-04-28")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT-3")
    private Date dataSaida;

    @Schema(description = "Horário de entrada à academia", example = "06:33:23")
    private String horaEntradaRegistroAcesso;

    @Schema(description = "Horário de saída da academia", example = "07:21:43")
    private String horaSaidaRegistroAcesso;

    @Schema(description = "Código identificador do meio utilizado para a entrada", example = "1")
    private Integer meioIdentificacaoEntrada;

    @Schema(description = "Código identificador do meio utilizado para a saída", example = "1")
    private Integer meioIdentificacaoSaida;

    @Schema(description = "Data do registro", example = "2025-04-28")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT-3")
    private Date dataRegistro;

    @Schema(description = "Senha de acesso", example = "abc1234")
    protected String senhaAcesso ;

    @Schema(description = "Confirmação da senha de acesso", example = "abc1234")
    protected String confirmaSenhaAcesso;

    @Schema(description = "Indica se a senha de acesso deve ser habilitada", example = "false")
    protected Boolean habilitaSenhaAcesso = false;

    @Schema(description = "Indica se deve registrar a saída", example = "false")
    private Boolean registrarSaida = false;

}
