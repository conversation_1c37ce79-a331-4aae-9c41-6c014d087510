package com.pacto.adm.core.dto.enveloperesposta.usuario;

import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.UsuarioMovelDTO;
import io.swagger.v3.oas.annotations.media.Schema;


@Schema(description = "Representação das respostas")
public class EnvelopeRespostaUsuarioDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private UsuarioDTO content;

    public UsuarioDTO getContent() {
        return content;
    }

    public void setContent(UsuarioDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"nome\": \"<PERSON> Silva\"";


}
