package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(name = "Cliente Rede Empresa", description = "Informações do cliente e da empresa que ele está vinculado")
public class ClienteRedeEmpresaDTO {

    @Schema(description = "Código identificador do Cliente Rede Empresa",  example = "123")
    private Integer codigo;

    @Schema(description = "Código de matrícula do cliente", example = "12303")
    private Integer codigoMatricula;

    @Schema(description = "Nome do cliente", example = "Felícia Stallone da Silva")
    private String nome;

    @Schema(description = "CPF do cliente", example = "234.567.891-10")
    private String cpf;

    @Schema(description = "Chave da empresa que o cliente está vinculado", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9")
    private String chaveEmpresa;

    @Schema(description = "Código da empresa que o cliente está vinculado", example = "1")
    private Integer codigoEmpresa;

    @Schema(description = "Nome da empresa que o cliente está vinculado", example = "ACADEMIA PACTO")
    private String nomeEmpresa;

    @Schema(description = "Data da sincronização do cliente", example = "2025-04-28T00:00:00.000Z")
    private Date dataSincronizacao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getChaveEmpresa() {
        return chaveEmpresa;
    }

    public void setChaveEmpresa(String chaveEmpresa) {
        this.chaveEmpresa = chaveEmpresa;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Date getDataSincronizacao() {
        return dataSincronizacao;
    }

    public void setDataSincronizacao(Date dataSincronizacao) {
        this.dataSincronizacao = dataSincronizacao;
    }
}
