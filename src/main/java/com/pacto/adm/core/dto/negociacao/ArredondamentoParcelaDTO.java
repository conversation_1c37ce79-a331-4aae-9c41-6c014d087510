package com.pacto.adm.core.dto.negociacao;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Arredondamento Parcela", description = "Informações sobre valores de entrada e parcelas com arredondamento aplicado.")
public class ArredondamentoParcelaDTO {

    @Schema(description = "Valor de entrada sugerido após o arredondamento.", example = "100.00")
    private Double valorEntrada;

    @Schema(description = "Valor das parcelas após o arredondamento.", example = "150.00")
    private Double valorParcelas;

    @Schema(description = "Indica se essa opção de arredondamento foi selecionada.", example = "false")
    private Boolean selecionado = false;

    public ArredondamentoParcelaDTO() {
    }

    public Double getValorEntrada() {
        return valorEntrada;
    }

    public void setValorEntrada(Double valorEntrada) {
        this.valorEntrada = valorEntrada;
    }

    public Double getValorParcelas() {
        return valorParcelas;
    }

    public void setValorParcelas(Double valorParcelas) {
        this.valorParcelas = valorParcelas;
    }


    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }
    
    
}