package com.pacto.adm.core.dto.enveloperesposta.parcelas;

import com.pacto.adm.core.dto.negociacao.ParcelasDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaParcelasDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ParcelasDTO content;

    public ParcelasDTO getContent() {
        return content;
    }

    public void setContent(ParcelasDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 341455, "
                    + "\"valor\": 120.00, "
                    + "\"dataLancamento\": \"2025-05-13\", "
                    + "\"descricao\": \"Parcela 02 - Contrato\"";


    public final static String requestBody = "{" + atributos + "}";

    public final static String resposta = "{"
            + "\"content\": {"
            + atributos
            + "}}";
}
