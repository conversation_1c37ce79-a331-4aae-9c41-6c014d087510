package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração Integração My Wellness", description = "Configurações para integração com a plataforma MyWellness.")
public class ConfiguracaoIntegracaoMyWellnessDTO {

    @Schema(description = "Indica se a integração está habilitada.", example = "true")
    private boolean habilitada;

    @Schema(description = "Indica se os vínculos devem ser enviados.", example = "true")
    private boolean enviarVinculos;

    @Schema(description = "Indica se os grupos devem ser enviados.", example = "false")
    private boolean enviarGrupos;

    @Schema(description = "URL da instalação (facility) do MyWellness.", example = "https://api.mywellness.com/facility/123")
    private String facilityUrl;

    @Schema(description = "Cha<PERSON> de acesso (API Key) da integração.", example = "abc123def456")
    private String apiKey;

    @Schema(description = "Usuário para autenticação na plataforma MyWellness.", example = "usuario_integracao")
    private String user;

    @Schema(description = "Senha para autenticação na plataforma MyWellness.", example = "senhaSegura123")
    private String password;

    @Schema(description = "Número de dias de vigência do vínculo com o GymPass via MyWellness.", example = "30")
    private Integer nrDiasVigenciaMyWellnessGymPass;

    @Schema(description = "Tipo de vigência para o GymPass via MyWellness.", example = "1")
    private Integer tipoVigenciaMyWellnessGympass;

    @Schema(description = "Dados da empresa relacionados à integração.")
    private EmpresaDTO empresa;

    public ConfiguracaoIntegracaoMyWellnessDTO() {
        inicializarDados();
    }

    private void inicializarDados() {
        this.user = "<EMAIL>";
        this.password = "Pac03092020@";
    }

    public boolean isHabilitada() {
        return habilitada;
    }

    public void setHabilitada(boolean habilitada) {
        this.habilitada = habilitada;
    }

    public boolean isEnviarVinculos() {
        return enviarVinculos;
    }

    public void setEnviarVinculos(boolean enviarVinculos) {
        this.enviarVinculos = enviarVinculos;
    }

    public boolean isEnviarGrupos() {
        return enviarGrupos;
    }

    public void setEnviarGrupos(boolean enviarGrupos) {
        this.enviarGrupos = enviarGrupos;
    }

    public String getFacilityUrl() {
        return facilityUrl;
    }

    public void setFacilityUrl(String facilityUrl) {
        this.facilityUrl = facilityUrl;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getNrDiasVigenciaMyWellnessGymPass() {
        return nrDiasVigenciaMyWellnessGymPass;
    }

    public void setNrDiasVigenciaMyWellnessGymPass(Integer nrDiasVigenciaMyWellnessGymPass) {
        this.nrDiasVigenciaMyWellnessGymPass = nrDiasVigenciaMyWellnessGymPass;
    }

    public Integer getTipoVigenciaMyWellnessGympass() {
        return tipoVigenciaMyWellnessGympass;
    }

    public void setTipoVigenciaMyWellnessGympass(Integer tipoVigenciaMyWellnessGympass) {
        this.tipoVigenciaMyWellnessGympass = tipoVigenciaMyWellnessGympass;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
