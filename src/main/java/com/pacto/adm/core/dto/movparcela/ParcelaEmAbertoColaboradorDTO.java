package com.pacto.adm.core.dto.movparcela;

import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Parcela em Aberto Colaborador", description = "Informações sobre parcelas em aberto de colaboradores")
public class ParcelaEmAbertoColaboradorDTO {

    @Schema(description = "Informações do colaborador que possui a parcela em aberto")
    private ColaboradorDTO colaborador;

    public ParcelaEmAbertoColaboradorDTO(
            ColaboradorDTO colaborador
    ) {
        this.colaborador = colaborador;
    }

    public ColaboradorDTO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorDTO colaborador) {
        this.colaborador = colaborador;
    }
}
