package com.pacto.adm.core.dto.enveloperesposta.evento;

import com.pacto.adm.core.entities.Evento;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListEvento {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<Evento> content;

    public List<Evento> getContent() {
        return content;
    }

    public void setContent(List<Evento> content) {
        this.content = content;
    }


    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaEvento.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

}
