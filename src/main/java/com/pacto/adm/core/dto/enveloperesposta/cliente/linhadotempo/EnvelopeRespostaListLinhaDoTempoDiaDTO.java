package com.pacto.adm.core.dto.enveloperesposta.cliente.linhadotempo;

import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.linhaTempo.LinhaDoTempoDiaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListLinhaDoTempoDiaDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<LinhaDoTempoDiaDTO> content;

    public List<LinhaDoTempoDiaDTO> getContent() {
        return content;
    }

    public void setContent(List<LinhaDoTempoDiaDTO> content) {
        this.content = content;
    }


    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaLinhaDoTempoDiaDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

}
