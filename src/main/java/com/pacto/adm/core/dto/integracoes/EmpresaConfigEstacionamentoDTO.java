package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(description = "Informações da configuração do estacionamento")
public class EmpresaConfigEstacionamentoDTO {

    @Schema(description = "Código único identificador da configuração de estacionamento", example = "1")
    private Integer codigo;

    @Schema(description = "Host FTP utilizado para a integração com o sistema de estacionamento", example = "ftp.estacionamento.example.com")
    private String ftpHost;

    @Schema(description = "Usuário FTP para autenticação na integração", example = "usuario-ftp")
    private String ftpUser;

    @Schema(description = "Senha FTP para autenticação na integração", example = "senha-ftp")
    private String ftpPass;

    @Schema(description = "Porta FTP utilizada para a conexão", example = "21")
    private Integer ftpPort;

    @Schema(description = "Indica se o valor deve ser enviado ao sistema de estacionamento", example = "true")
    private boolean enviaValor = true;

    @Schema(description = "Indica se o horário deve ser enviado ao sistema de estacionamento", example = "true")
    private boolean enviaHorario = true;

    @Schema(description = "Indica se o telefone e e-mail devem ser enviados ao sistema de estacionamento", example = "false")
    private boolean enviaTelefoneEmail;

    @Schema(description = "Nome do arquivo gerado para integração com o sistema de estacionamento", example = "alunoEstacionamento.txt")
    private String nomeArquivo = "alunoEstacionamento.txt";

    @Schema(description = "Lista de produtos que devem ser adicionados à integração", example = "PRODUTO1, PRODUTO2")
    private String produtosAdicionar;

    @Schema(description = "Código da empresa associada à configuração de estacionamento", example = "1")
    private Integer empresa;




    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getFtpHost() {
        return ftpHost;
    }

    public void setFtpHost(String ftpHost) {
        this.ftpHost = ftpHost;
    }

    public String getFtpUser() {
        return ftpUser;
    }

    public void setFtpUser(String ftpUser) {
        this.ftpUser = ftpUser;
    }

    public String getFtpPass() {
        return ftpPass;
    }

    public void setFtpPass(String ftpPass) {
        this.ftpPass = ftpPass;
    }

    public Integer getFtpPort() {
        return ftpPort;
    }

    public void setFtpPort(Integer ftpPort) {
        this.ftpPort = ftpPort;
    }

    public boolean isEnviaValor() {
        return enviaValor;
    }

    public void setEnviaValor(boolean enviaValor) {
        this.enviaValor = enviaValor;
    }

    public boolean isEnviaHorario() {
        return enviaHorario;
    }

    public void setEnviaHorario(boolean enviaHorario) {
        this.enviaHorario = enviaHorario;
    }

    public boolean isEnviaTelefoneEmail() {
        return enviaTelefoneEmail;
    }

    public void setEnviaTelefoneEmail(boolean enviaTelefoneEmail) {
        this.enviaTelefoneEmail = enviaTelefoneEmail;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getProdutosAdicionar() {
        return produtosAdicionar;
    }

    public void setProdutosAdicionar(String produtosAdicionar) {
        this.produtosAdicionar = produtosAdicionar;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
}
