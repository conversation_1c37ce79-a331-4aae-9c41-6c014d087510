package com.pacto.adm.core.dto.enveloperesposta.vendaavulsa;

import com.pacto.adm.core.dto.VendaAvulsaDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaItemVendaAvulsaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaVendaAvulsaDTO {


    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private VendaAvulsaDTO content;

    public VendaAvulsaDTO getContent() {
        return content;
    }

    public void setContent(VendaAvulsaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"pessoa\": 5, "
                    + "\"codigo\": 1113, "
                    + "\"colaborador\": true, "
                    + "\"nomeComprador\": \"<PERSON>\", "
                    + "\"descontoGeral\": 10, "
                    + "\"tipo\": 1, "
                    + "\"lancamento\": 100.00, "
                    + "\"parcelas\": 1, "
                    + "\"primeiraParcela\": 100.00, "
                    + "\"descricaoParcela\": \"Compra de produtos\", "
                    + "\"itens\": [{" + EnvelopeRespostaItemVendaAvulsaDTO.atributos + "}]";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
