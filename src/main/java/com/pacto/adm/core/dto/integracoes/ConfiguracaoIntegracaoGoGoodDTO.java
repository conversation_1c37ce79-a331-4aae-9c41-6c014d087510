package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração de Integração GoGood", description = "Configurações para integração com o sistema GoGood.")
public class ConfiguracaoIntegracaoGoGoodDTO {

    @Schema(description = "Token de autenticação utilizado na integração com o GoGood.", example = "token-academy-gogood-abc123")
    private String tokenAcademyGoGood;

    @Schema(description = "Detalhes da empresa associada à configuração de integração GoGood.")
    private EmpresaDTO empresa;

    public String getTokenAcademyGoGood() {
        return tokenAcademyGoGood;
    }

    public void setTokenAcademyGoGood(String tokenAcademyGoGood) {
        this.tokenAcademyGoGood = tokenAcademyGoGood;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
