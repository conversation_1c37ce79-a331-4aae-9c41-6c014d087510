package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoDelsoftDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoDelsoftDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoDelsoftDTO content;

    public ConfiguracaoIntegracaoDelsoftDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoDelsoftDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"utilizaIntegracaoDelsoft\": true, "
                    + "\"hostIntegracaoDelsoft\": \"delsoft.example.com\", "
                    + "\"portaIntegracaoDelsoft\": 8080, "
                    + "\"tokenIntegracaoDelsoft\": \"abcd1234token\", "
                    + "\"nomeAplicacaoDelsoft\": \"AplicacaoDelsoft1\", "
                    + "\"usuarioAplicacaoDelsoft\": \"adminDelsoft\", "
                    + "\"senhaAplicacaoDelsoft\": \"senhaDelsoft\", "
                    + "\"planoAplicacaoDelsoft\": 1, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public static final String atributosSemEmpresa =
            "\"utilizaIntegracaoDelsoft\": true, "
                    + "\"hostIntegracaoDelsoft\": \"delsoft.example.com\", "
                    + "\"portaIntegracaoDelsoft\": 8080, "
                    + "\"tokenIntegracaoDelsoft\": \"abcd1234token\", "
                    + "\"nomeAplicacaoDelsoft\": \"AplicacaoDelsoft1\", "
                    + "\"usuarioAplicacaoDelsoft\": \"adminDelsoft\", "
                    + "\"senhaAplicacaoDelsoft\": \"senhaDelsoft\", "
                    + "\"planoAplicacaoDelsoft\": 1, "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
