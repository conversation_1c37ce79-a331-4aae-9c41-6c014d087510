package com.pacto.adm.core.dto.recibodevolucao;

import com.pacto.adm.core.dto.*;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
@Schema (description = "Informações do Recibo de Devolução")
public class ReciboDevolucaoDTO {

    @Schema(description = "Código único identificador do recibo de devolução", example = "1")
    private Integer codigo;

    @Schema(description = "Data da devolução (em milissegundos desde 1970-01-01)", example = "1698765432000")
    private Long dataDevolucao;

    @Schema(description = "Indica se a devolução foi realizada manualmente", example = "true")
    private Boolean devolucaoManual;

    @Schema(description = "Indica se a liberação foi realizada", example = "false")
    private Boolean liberacao;

    @Schema(description = "Indica se a liberação da devolução foi realizada", example = "true")
    private Boolean liberacaoDevolucao;

    @Schema(description = "Indica se a quitação foi realizada", example = "false")
    private Boolean quitacao;

    @Schema(description = "Indica se a quitação foi realizada manualmente", example = "true")
    private Boolean quitacaoManual;

    @Schema(description = "Valor base do contrato", example = "500.00")
    private BigDecimal valorBaseContrato;

    @Schema(description = "Valor disponível na conta corrente", example = "300.00")
    private BigDecimal valorContaCorrente;

    @Schema(description = "Valor total do contrato", example = "1000.00")
    private BigDecimal valorContrato;

    @Schema(description = "Valor da devolução", example = "200.00")
    private BigDecimal valorDevolucao;

    @Schema(description = "Valor devolvido em dinheiro", example = "150.00")
    private BigDecimal valorDevolvidoEmDinheiro;

    @Schema(description = "Valor da multa por cancelamento", example = "50.00")
    private BigDecimal valorMultaCancelamento;

    @Schema(description = "Valor original do contrato", example = "1000.00")
    private BigDecimal valorOriginal;

    @Schema(description = "Valor real da devolução", example = "250.00")
    private BigDecimal valorRealDevolucao;

    @Schema(description = "Valor da taxa de cancelamento", example = "30.00")
    private BigDecimal valorTaxaCancelamento;

    @Schema(description = "Valor total pago pelo cliente", example = "800.00")
    private BigDecimal valorTotalPagoPeloCliente;

    @Schema(description = "Valor total da soma dos produtos dos contratos", example = "1200.00")
    private BigDecimal valorTotalSomaProdutoContratos;

    @Schema(description = "Valor utilizado pelo cliente", example = "600.00")
    private BigDecimal valorUtilizadoPeloCliente;

    @Schema(description = "Detalhes do contrato associado à devolução")
    private ContratoDTO contrato;

    @Schema(description = "Detalhes do movimento do produto associado à devolução")
    private MovProdutoDTO movProduto;

    @Schema(description = "Detalhes da pessoa associada à devolução")
    private PessoaDTO pessoa;

    @Schema(description = "Detalhes do produto de devolução")
    private MovProdutoDTO prodDevolucao;

    @Schema(description = "Detalhes dos recebíveis associados à devolução")
    private MovProdutoDTO prodRecebiveis;

    @Schema(description = "Detalhes do recibo editado")
    private ReciboPagamentoDTO reciboEditado;

    @Schema(description = "Detalhes do usuário responsável pela devolução")
    private UsuarioDTO usuario;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Long getDataDevolucao() {
        return dataDevolucao;
    }

    public void setDataDevolucao(Long dataDevolucao) {
        this.dataDevolucao = dataDevolucao;
    }

    public Boolean getDevolucaoManual() {
        return devolucaoManual;
    }

    public void setDevolucaoManual(Boolean devolucaoManual) {
        this.devolucaoManual = devolucaoManual;
    }

    public Boolean getLiberacao() {
        return liberacao;
    }

    public void setLiberacao(Boolean liberacao) {
        this.liberacao = liberacao;
    }

    public Boolean getLiberacaoDevolucao() {
        return liberacaoDevolucao;
    }

    public void setLiberacaoDevolucao(Boolean liberacaoDevolucao) {
        this.liberacaoDevolucao = liberacaoDevolucao;
    }

    public Boolean getQuitacao() {
        return quitacao;
    }

    public void setQuitacao(Boolean quitacao) {
        this.quitacao = quitacao;
    }

    public Boolean getQuitacaoManual() {
        return quitacaoManual;
    }

    public void setQuitacaoManual(Boolean quitacaoManual) {
        this.quitacaoManual = quitacaoManual;
    }

    public BigDecimal getValorBaseContrato() {
        return valorBaseContrato;
    }

    public void setValorBaseContrato(BigDecimal valorBaseContrato) {
        this.valorBaseContrato = valorBaseContrato;
    }

    public BigDecimal getValorContaCorrente() {
        return valorContaCorrente;
    }

    public void setValorContaCorrente(BigDecimal valorContaCorrente) {
        this.valorContaCorrente = valorContaCorrente;
    }

    public BigDecimal getValorContrato() {
        return valorContrato;
    }

    public void setValorContrato(BigDecimal valorContrato) {
        this.valorContrato = valorContrato;
    }

    public BigDecimal getValorDevolucao() {
        return valorDevolucao;
    }

    public void setValorDevolucao(BigDecimal valorDevolucao) {
        this.valorDevolucao = valorDevolucao;
    }

    public BigDecimal getValorDevolvidoEmDinheiro() {
        return valorDevolvidoEmDinheiro;
    }

    public void setValorDevolvidoEmDinheiro(BigDecimal valorDevolvidoEmDinheiro) {
        this.valorDevolvidoEmDinheiro = valorDevolvidoEmDinheiro;
    }

    public BigDecimal getValorMultaCancelamento() {
        return valorMultaCancelamento;
    }

    public void setValorMultaCancelamento(BigDecimal valorMultaCancelamento) {
        this.valorMultaCancelamento = valorMultaCancelamento;
    }

    public BigDecimal getValorOriginal() {
        return valorOriginal;
    }

    public void setValorOriginal(BigDecimal valorOriginal) {
        this.valorOriginal = valorOriginal;
    }

    public BigDecimal getValorRealDevolucao() {
        return valorRealDevolucao;
    }

    public void setValorRealDevolucao(BigDecimal valorRealDevolucao) {
        this.valorRealDevolucao = valorRealDevolucao;
    }

    public BigDecimal getValorTaxaCancelamento() {
        return valorTaxaCancelamento;
    }

    public void setValorTaxaCancelamento(BigDecimal valorTaxaCancelamento) {
        this.valorTaxaCancelamento = valorTaxaCancelamento;
    }

    public BigDecimal getValorTotalPagoPeloCliente() {
        return valorTotalPagoPeloCliente;
    }

    public void setValorTotalPagoPeloCliente(BigDecimal valorTotalPagoPeloCliente) {
        this.valorTotalPagoPeloCliente = valorTotalPagoPeloCliente;
    }

    public BigDecimal getValorTotalSomaProdutoContratos() {
        return valorTotalSomaProdutoContratos;
    }

    public void setValorTotalSomaProdutoContratos(BigDecimal valorTotalSomaProdutoContratos) {
        this.valorTotalSomaProdutoContratos = valorTotalSomaProdutoContratos;
    }

    public BigDecimal getValorUtilizadoPeloCliente() {
        return valorUtilizadoPeloCliente;
    }

    public void setValorUtilizadoPeloCliente(BigDecimal valorUtilizadoPeloCliente) {
        this.valorUtilizadoPeloCliente = valorUtilizadoPeloCliente;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoDTO contrato) {
        this.contrato = contrato;
    }

    public MovProdutoDTO getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProdutoDTO movProduto) {
        this.movProduto = movProduto;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }

    public MovProdutoDTO getProdDevolucao() {
        return prodDevolucao;
    }

    public void setProdDevolucao(MovProdutoDTO prodDevolucao) {
        this.prodDevolucao = prodDevolucao;
    }

    public MovProdutoDTO getProdRecebiveis() {
        return prodRecebiveis;
    }

    public void setProdRecebiveis(MovProdutoDTO prodRecebiveis) {
        this.prodRecebiveis = prodRecebiveis;
    }

    public ReciboPagamentoDTO getReciboEditado() {
        return reciboEditado;
    }

    public void setReciboEditado(ReciboPagamentoDTO reciboEditado) {
        this.reciboEditado = reciboEditado;
    }

    public UsuarioDTO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioDTO usuario) {
        this.usuario = usuario;
    }
}
