package com.pacto.adm.core.dto.enveloperesposta;

import com.pacto.adm.core.dto.BiometriaDTO;
import io.swagger.v3.oas.annotations.media.Schema;


@Schema(description = "Representação das respostas")
public class EnvelopeRespostaRegistroAcessoManualDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private BiometriaDTO content;

    public BiometriaDTO getContent() {
        return content;
    }

    public void setContent(BiometriaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"digital\": true, "
                    + "\"facial\": false";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
