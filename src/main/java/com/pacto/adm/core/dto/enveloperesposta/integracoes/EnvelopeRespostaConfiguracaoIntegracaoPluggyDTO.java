package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaParceiroFidelidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.alterdata.EnvelopeRespostaConfiguracaoContabilAlterData;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.botconversa.EnvelopeRespostaConfiguracaoIntegracaoBotConversaDTO;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.facilitepay.EnvelopeRespostaConfiguracaoIntegracaoFacilitePayDTO;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.spivi.EnvelopeRespostaConfiguracaoIntegracaoSpiviDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoPluggyDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracoesIntegracoesEmpresasDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoPluggyDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoPluggyDTO content;

    public ConfiguracaoIntegracaoPluggyDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoPluggyDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 12345, "
                    + "\"empresa\": 6789, "
                    + "\"id\": \"abc123xyz\", "
                    + "\"dadosRetorno\": {\"status\":\"sucesso\"}, "
                    + "\"ativo\": true";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
