package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGoGoodDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoTotalPassDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoTotalPassDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoTotalPassDTO content;

    public ConfiguracaoIntegracaoTotalPassDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoTotalPassDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigoTotalpass\": \"codigo-totalpass-456\", "
                    + "\"apiKey\": \"api-key-totalpass-xyz789\", "
                    + "\"limiteDeAcessosPorDia\": 10, "
                    + "\"limiteDeAulasPorDia\": 5, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}, "
                    + "\"inativo\": false, "
                    + "\"permitirWod\": true";
    public static final String atributosSemResposta =
            "\"codigoTotalpass\": \"codigo-totalpass-456\", "
                    + "\"apiKey\": \"api-key-totalpass-xyz789\", "
                    + "\"limiteDeAcessosPorDia\": 10, "
                    + "\"limiteDeAulasPorDia\": 5, "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\", "
                    + "\"inativo\": false, "
                    + "\"permitirWod\": true";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
