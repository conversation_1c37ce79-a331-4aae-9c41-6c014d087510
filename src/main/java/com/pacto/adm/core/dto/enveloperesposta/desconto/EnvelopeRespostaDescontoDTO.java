package com.pacto.adm.core.dto.enveloperesposta.desconto;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaConfigEstacionamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaParceiroFidelidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaCidade;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaEstado;
import com.pacto.adm.core.dto.negociacao.DescontoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaDescontoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private DescontoDTO content;

    public DescontoDTO getContent() {
        return content;
    }

    public void setContent(DescontoDTO content) {
        this.content = content;
    }




    public static final String atributos =
            "\"codigo\": 101, "
                    + "\"descricao\": \"Desconto para plano da academia\", "
                    + "\"tipo\": \"PE\", "
                    + "\"valor\": 10.0, "
                    + "\"valorReal\": 50.00, "
                    + "\"nrDiasAntecipado\": 5, "
                    + "\"apresentarDiasAtrasoRenovacao\": true, "
                    + "\"selecionado\": false";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
