package com.pacto.adm.core.dto.enveloperesposta.modalidade;

import com.pacto.adm.core.dto.enveloperesposta.aulas.EnvelopeRespostaArquivoDTO;
import com.pacto.adm.core.dto.enveloperesposta.aulas.EnvelopeRespostaProdutoSugeridoDTO;
import com.pacto.adm.core.dto.enveloperesposta.modalidade.tipo.EnvelopeRespostaTipoModalidadeDTO;
import com.pacto.adm.core.dto.modalidade.ModalidadeDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaModalidadeDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ModalidadeDTO content;

    public ModalidadeDTO getContent() {
        return content;
    }

    public void setContent(ModalidadeDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 90, "
                    + "\"nome\": \"Musculação\", "
                    + "\"nrVezes\": 3, "
                    + "\"tipoModalidade\": {" + EnvelopeRespostaTipoModalidadeDTO.atributos + "}, "
                    + "\"valorMensal\": 150.00, "
                    + "\"ativo\": true, "
                    + "\"utilizarturma\": true, "
                    + "\"fotoKey\": \"modalidade_001.jpg\", "
                    + "\"fotoKeyUrlFull\": \"https://exemplo.com/imagens/modalidade_001.jpg\", "
                    + "\"arquivo\": {" + EnvelopeRespostaArquivoDTO.atributos + "}, "
                    + "\"empresasModalidade\": ["
                    + "{" + EnvelopeRespostaModalidadeEmpresaDTO.atributos + "}"
                    + "], "
                    + "\"produtosSugeridos\": ["
                    + "{" + EnvelopeRespostaProdutoSugeridoDTO.atributos + "}"
                    + "], "
                    + "\"temContratoVendido\": true, "
                    + "\"crossfit\": false";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
