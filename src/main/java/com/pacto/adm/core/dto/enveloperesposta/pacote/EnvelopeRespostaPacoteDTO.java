package com.pacto.adm.core.dto.enveloperesposta.pacote;

import com.pacto.adm.core.dto.enveloperesposta.desconto.EnvelopeRespostaDescontoDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.duracao.EnvelopeRespostaDuracaoPlanoDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.horario.EnvelopeRespostaPlanoHorarioDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.modalidade.EnvelopeRespostaPlanoModalidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.plano.produto.EnvelopeRespostaPlanoProdutoDTO;
import com.pacto.adm.core.dto.negociacao.NegociacaoDTO;
import com.pacto.adm.core.dto.negociacao.PacoteDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaPacoteDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private PacoteDTO content;

    public PacoteDTO getContent() {
        return content;
    }

    public void setContent(PacoteDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"qtdeModalidades\": 3, "
                    + "\"descricao\": \"Pacote Fitness Avançado\", "
                    + "\"valor\": 199.90, "
                    + "\"adicional\": true, "
                    + "\"padrao\": false, "
                    + "\"modalidadesEspecificas\": true, "
                    + "\"modalidades\": [1, 2, 3]";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
