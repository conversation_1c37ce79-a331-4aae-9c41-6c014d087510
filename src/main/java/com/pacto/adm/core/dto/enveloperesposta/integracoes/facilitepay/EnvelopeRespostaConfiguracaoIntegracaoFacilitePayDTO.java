package com.pacto.adm.core.dto.enveloperesposta.integracoes.facilitepay;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoRecursosFacilitePayDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoFacilitePayDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoRecursosFacilitePayDTO content;

    public ConfiguracaoIntegracaoRecursosFacilitePayDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoRecursosFacilitePayDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"empresa\":  {" + EnvelopeRespostaEmpresaDTO.atributos + "}, " +
                    "\"concContasPagarFacilitePay\": true, " +
                    "\"concContasReceberFacilitePay\": true, " +
                    "\"facilitePayConciliacaoCartao\": false, " +
                    "\"facilitePayReguaCobranca\": true, " +
                    "\"facilitePayCDLSPC\": false, " +
                    "\"valorMetaFacilitePay\": 5000.00, " +
                    "\"facilitePayReguaCobrancaEmail\": true, " +
                    "\"facilitePayReguaCobrancaSms\": false, " +
                    "\"facilitePayReguaCobrancaApp\": true, " +
                    "\"facilitePayReguaCobrancaWhatsApp\": true, " +
                    "\"qtdLmtContasConcFacilitePay\": 100, " +
                    "\"facilitePayReguaCobrancaGymbotPro\": false";

    public static final String atributosSemEmpresa =
            "\"empresa\": \"INFORMAÇÕES DA EMPRESA\", " +
                    "\"concContasPagarFacilitePay\": true, " +
                    "\"concContasReceberFacilitePay\": true, " +
                    "\"facilitePayConciliacaoCartao\": false, " +
                    "\"facilitePayReguaCobranca\": true, " +
                    "\"facilitePayCDLSPC\": false, " +
                    "\"valorMetaFacilitePay\": 5000.00, " +
                    "\"facilitePayReguaCobrancaEmail\": true, " +
                    "\"facilitePayReguaCobrancaSms\": false, " +
                    "\"facilitePayReguaCobrancaApp\": true, " +
                    "\"facilitePayReguaCobrancaWhatsApp\": true, " +
                    "\"qtdLmtContasConcFacilitePay\": 100, " +
                    "\"facilitePayReguaCobrancaGymbotPro\": false";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";


}
