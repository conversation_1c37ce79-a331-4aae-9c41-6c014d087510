package com.pacto.adm.core.dto.filtros;

import org.json.JSONObject;

import java.util.Date;

public class FiltroPlanoContaDespesasJSON {
    private String parametro;
    private Integer codigoEmpresa;
    private Date dataInicio;
    private Date dataFim;

    public FiltroPlanoContaDespesasJSON(String filters) {
        if (filters != null) {
            JSONObject filtersJson = new JSONObject(filters);
            this.parametro = filtersJson.optString("quicksearchValue");
            this.codigoEmpresa = filtersJson.optInt("codigoEmpresa");
            this.dataInicio = new Date(filtersJson.optLong("dataInicio"));
            this.dataFim = new Date(filtersJson.optLong("dataFim"));
        }
    }

    public String getParametro() {
        return parametro;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }
}
