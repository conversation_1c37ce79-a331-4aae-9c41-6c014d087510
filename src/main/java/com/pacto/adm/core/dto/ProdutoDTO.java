package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.enumerador.TipoProdutoEnum;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Produto", description = "Informações do produto")
public class ProdutoDTO {

    @Schema(description = "Código único identificador do produto", example = "1")
    private Integer codigo;

    @Schema(description = "Descrição detalhada do produto", example = "Plano Mensal Básico")
    private String descricao;

    @Schema(description = "Valor final do produto", example = "99.99")
    private Double valorFinal;

    @Schema(description = "Indica se o produto está desativado", example = "false")
    private Boolean desativado;

    @Schema(description = "Tipo do produto (código)", example = "PM")
    private String tipoProduto;

    @Schema(description = "Descrição do tipo do produto", example = "Mês de Referência Plano")
    private String tipoProdutoDescricao;

    @Schema(description = "Indica se a vigência do contrato deve prevalecer sobre a do produto", example = "true")
    private Boolean prevalecerVigenciaContrato;

    @Schema(description = "Indica se o produto bloqueia pela vigência", example = "false")
    private Boolean bloqueiaPelaVigencia;

    @Schema(description = "Número de dias de vigência do produto", example = "30")
    private Integer nrDiasVigencia;

    @Schema(description = "Quantidade de pontos associados ao produto", example = "100")
    private Integer pontos;

    @Schema(description = "Tipo de vigência do produto", example = "Mensal")
    private String tipoVigencia;

    @Schema(description = "Indica se o produto é renovável automaticamente", example = "true")
    private Boolean renovavelAutomaticamente;
    private Integer contratoTextoPadrao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Boolean getDesativado() {
        return desativado;
    }

    public void setDesativado(Boolean desativado) {
        this.desativado = desativado;
    }

    public String getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }

    public Boolean getPrevalecerVigenciaContrato() {
        return prevalecerVigenciaContrato;
    }

    public void setPrevalecerVigenciaContrato(Boolean prevalecerVigenciaContrato) {
        this.prevalecerVigenciaContrato = prevalecerVigenciaContrato;
    }

    public Boolean getBloqueiaPelaVigencia() {
        return bloqueiaPelaVigencia;
    }

    public void setBloqueiaPelaVigencia(Boolean bloqueiaPelaVigencia) {
        this.bloqueiaPelaVigencia = bloqueiaPelaVigencia;
    }

    public Integer getNrDiasVigencia() {
        return nrDiasVigencia;
    }

    public void setNrDiasVigencia(Integer nrDiasVigencia) {
        this.nrDiasVigencia = nrDiasVigencia;
    }

    public String getTipoProdutoDescricao() {
        return tipoProdutoDescricao;
    }

    public void setTipoProdutoDescricao(String tipoProdutoDescricao) {
        this.tipoProdutoDescricao = tipoProdutoDescricao;
    }

    public String getTipoVigencia() {
        return tipoVigencia;
    }

    public void setTipoVigencia(String tipoVigencia) {
        this.tipoVigencia = tipoVigencia;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public Boolean getRenovavelAutomaticamente() {
        return renovavelAutomaticamente;
    }

    public void setRenovavelAutomaticamente(Boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public Integer getContratoTextoPadrao() {
        return contratoTextoPadrao;
    }

    public void setContratoTextoPadrao(Integer contratoTextoPadrao) {
        this.contratoTextoPadrao = contratoTextoPadrao;
    }
}
