package com.pacto.adm.core.dto.auladesmarcada;

import com.pacto.adm.core.dto.ClienteDTO;
import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.dto.HorarioTurmaDTO;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.operacaocoletiva.OperacaoColetivaDTO;
import com.pacto.adm.core.dto.reposicao.ReposicaoDTO;
import com.pacto.adm.core.dto.turma.TurmaDTO;
import com.pacto.adm.core.enumerador.OrigemSistemaEnum;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

@Schema(name = "Aula Desmarcada", description = "Informações detalhadas sobre a aula desmarcada, incluindo reposições e justificativas.")
public class AulaDesmarcadaDTO {

    @Schema(description = "Código único identificador da aula desmarcada.", example = "1501")
    private Integer codigo;

    @Schema(description = "Indica se é permitido repor a aula desmarcada.", example = "true")
    private Boolean permiteReporAulaDesmarcada;

    @Schema(description = "Detalhes da reposição associada à aula desmarcada.")
    private ReposicaoDTO reposicao;

    @Schema(description = "Data de lançamento da aula desmarcada.", example = "2023-07-10T00:00:00Z")
    private Date dataLancamento;

    @Schema(description = "Data original da aula antes de ser desmarcada.", example = "2023-07-10T08:00:00Z")
    private Date dataOrigem;

    @Schema(description = "Data da reposição da aula, caso tenha ocorrido.", example = "2023-07-15T08:00:00Z")
    private Date dataReposicao;

    @Schema(description = "Origem do sistema que gerou a aula desmarcada." +
            "Tipos de origem: 1 - ZillyonWeb, 2 - Agenda Web, 3 - Pacto Treino, 4 - App Treino, 5 - App Professor, 6 - Autoatendimento, 7 - Site Vendas, 8 - Buzz Lead, 9 - Vendas 2.0, 10 - App do consultor, 11 - Booking Gympass, 12 - Fila de espera, 13 - Importação API, 14 - Hubspot Lead, 15 - CRM Meta Diaria, 16 - Pacto Flow, 17 - Nova Tela de Negociação",
            example = "1", implementation = OrigemSistemaEnum.class)
    private OrigemSistemaEnum origemSistema;

    @Schema(description = "Indica se a aula foi desmarcada devido a afastamento do aluno.", example = "false")
    private Boolean desmarcadaPorAfastamento;

    @Schema(description = "Detalhes da operação coletiva associada à aula desmarcada.")
    private OperacaoColetivaDTO operacaoColetiva;

    @Schema(description = "Justificativa para a desmarcação da aula.", example = "Problema de saúde do aluno.")
    private String justificativa;

    @Schema(description = "Turma para a qual a aula desmarcada será realocada, se aplicável.")
    private TurmaDTO turmaDestino;

    @Schema(description = "Contrato anterior associado à aula desmarcada, caso haja histórico relacionado.")
    private ContratoDTO contratoAnterior;

    @Schema(description = "Contrato atual associado à aula desmarcada.")
    private ContratoDTO contrato;

    @Schema(description = "Informações do cliente associado à aula desmarcada.")
    private ClienteDTO cliente;

    @Schema(description = "Turma da aula desmarcada.")
    private TurmaDTO turma;

    @Schema(description = "Horário da turma relacionada à aula desmarcada.")
    private HorarioTurmaDTO horarioTurma;

    @Schema(description = "Informações do usuário que registrou a desmarcação da aula.")
    private UsuarioDTO usuario;

    @Schema(description = "Empresa associada à aula desmarcada.")
    private EmpresaDTO empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getPermiteReporAulaDesmarcada() {
        return permiteReporAulaDesmarcada;
    }

    public void setPermiteReporAulaDesmarcada(Boolean permiteReporAulaDesmarcada) {
        this.permiteReporAulaDesmarcada = permiteReporAulaDesmarcada;
    }

    public ReposicaoDTO getReposicao() {
        return reposicao;
    }

    public void setReposicao(ReposicaoDTO reposicao) {
        this.reposicao = reposicao;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataOrigem() {
        return dataOrigem;
    }

    public void setDataOrigem(Date dataOrigem) {
        this.dataOrigem = dataOrigem;
    }

    public Date getDataReposicao() {
        return dataReposicao;
    }

    public void setDataReposicao(Date dataReposicao) {
        this.dataReposicao = dataReposicao;
    }

    public OrigemSistemaEnum getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }

    public Boolean getDesmarcadaPorAfastamento() {
        return desmarcadaPorAfastamento;
    }

    public void setDesmarcadaPorAfastamento(Boolean desmarcadaPorAfastamento) {
        this.desmarcadaPorAfastamento = desmarcadaPorAfastamento;
    }

    public OperacaoColetivaDTO getOperacaoColetiva() {
        return operacaoColetiva;
    }

    public void setOperacaoColetiva(OperacaoColetivaDTO operacaoColetiva) {
        this.operacaoColetiva = operacaoColetiva;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public TurmaDTO getTurmaDestino() {
        return turmaDestino;
    }

    public void setTurmaDestino(TurmaDTO turmaDestino) {
        this.turmaDestino = turmaDestino;
    }

    public ContratoDTO getContratoAnterior() {
        return contratoAnterior;
    }

    public void setContratoAnterior(ContratoDTO contratoAnterior) {
        this.contratoAnterior = contratoAnterior;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoDTO contrato) {
        this.contrato = contrato;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public TurmaDTO getTurma() {
        return turma;
    }

    public void setTurma(TurmaDTO turma) {
        this.turma = turma;
    }

    public HorarioTurmaDTO getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaDTO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public UsuarioDTO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioDTO usuario) {
        this.usuario = usuario;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
