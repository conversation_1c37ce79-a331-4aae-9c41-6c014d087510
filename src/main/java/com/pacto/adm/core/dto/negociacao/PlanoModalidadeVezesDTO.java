package com.pacto.adm.core.dto.negociacao;

import com.pacto.config.utils.Uteis;
import com.pacto.config.utils.UteisValidacao;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Plano Modalidade Vezes", description = "Informações sobre as configurações de vezes que a modalidade pode ser utilizada no plano, incluindo valores e descontos.")
public class PlanoModalidadeVezesDTO {

    @Schema(description = "Código identificador da configuração de vezes para a modalidade no plano.", example = "1")
    private Integer codigo;

    @Schema(description = "Número de vezes que a modalidade pode ser utilizada.", example = "10")
    private Integer vezes;

    @Schema(description = "Duração de cada utilização da modalidade, em minutos.", example = "60")
    private Integer duracao;

    @Schema(description = "Código do horário associado à utilização da modalidade.", example = "2")
    private Integer horario;

    @Schema(description = "Tipo de operação para o valor da modalidade, como adição ou subtração.", example = "ADICAO")
    private String tipoOperacao;

    @Schema(description = "Tipo de valor associado à modalidade, como valor fixo ou percentual.", example = "FIXO")
    private String tipoValor;

    @Schema(description = "Valor unitário da modalidade por vez utilizada.", example = "50.00")
    private Double valorModalidade;

    @Schema(description = "Valor específico para a modalidade, caso haja variação de preço.", example = "40.00")
    private Double valorEspecifico;

    @Schema(description = "Percentual de desconto aplicado à modalidade.", example = "10.00")
    private Double percentualDesconto;

    @Schema(description = "Indica se a configuração de vezes é uma referência para outras modalidades.", example = "false")
    private boolean referencia;

    @Schema(description = "Indica se a modalidade está associada a crédito de sessão.", example = "true")
    private boolean creditoSessao;

    public String getDescricao(){
        String desc = vezes + "x";
        try {
            if(creditoSessao){
                desc += " - R$ " + Uteis.arrendondarForcando2CadasDecimaisComVirgula(valorEspecifico);
                desc += " por crédito ";
            } else if(!UteisValidacao.emptyString(tipoOperacao)){
                desc += " - " + (tipoOperacao.equals("AC") ? "Acréscimo " : "Redução ");
                if(tipoValor != null && tipoValor.equals("VE")){
                    desc += "R$ " + Uteis.arrendondarForcando2CadasDecimaisComVirgula(valorEspecifico);
                } else {
                    desc += Uteis.arrendondarForcando2CadasDecimaisComVirgula(percentualDesconto) + "%";
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return desc;
    }

    public boolean isCreditoSessao() {
        return creditoSessao;
    }

    public void setCreditoSessao(boolean creditoSessao) {
        this.creditoSessao = creditoSessao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getVezes() {
        return vezes;
    }

    public void setVezes(Integer vezes) {
        this.vezes = vezes;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getHorario() {
        return horario;
    }

    public void setHorario(Integer horario) {
        this.horario = horario;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public String getTipoValor() {
        return tipoValor;
    }

    public void setTipoValor(String tipoValor) {
        this.tipoValor = tipoValor;
    }

    public Double getValorModalidade() {
        return valorModalidade;
    }

    public void setValorModalidade(Double valorModalidade) {
        this.valorModalidade = valorModalidade;
    }

    public Double getValorEspecifico() {
        return valorEspecifico;
    }

    public void setValorEspecifico(Double valorEspecifico) {
        this.valorEspecifico = valorEspecifico;
    }

    public Double getPercentualDesconto() {
        return percentualDesconto;
    }

    public void setPercentualDesconto(Double percentualDesconto) {
        this.percentualDesconto = percentualDesconto;
    }

    public boolean isReferencia() {
        return referencia;
    }

    public void setReferencia(boolean referencia) {
        this.referencia = referencia;
    }
}
