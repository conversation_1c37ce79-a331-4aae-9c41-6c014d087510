package com.pacto.adm.core.dto.enveloperesposta.cliente.listagem;

import com.pacto.adm.core.dto.ClienteListagemDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

//@Schema(name = "Resposta Listagem de Clientes", description = "Representação das respostas envolvendo uma lista de clientes")
public class EnvelopeRespostaListClienteListagemDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ClienteListagemDTO> content;

    public List<ClienteListagemDTO> getContent() {
        return content;
    }

    public void setContent(List<ClienteListagemDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaClienteListagemDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

}
