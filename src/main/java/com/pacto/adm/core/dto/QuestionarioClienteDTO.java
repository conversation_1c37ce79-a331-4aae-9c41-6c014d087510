package com.pacto.adm.core.dto;

import com.pacto.adm.core.dto.auth.AuthDTO;
import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import com.pacto.adm.core.dto.indicacao.evento.EventoDTO;
import com.pacto.adm.core.dto.questionario.QuestionarioDTO;
import com.pacto.adm.core.entities.Colaborador;
import com.pacto.adm.core.entities.Evento;
import com.pacto.adm.core.entities.Questionario;
import com.pacto.adm.core.enumerador.OrigemSistemaEnum;
import com.pacto.adm.core.enumerador.TipoBV;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Schema(name = "Questionário Cliente", description = "Informações do questionário do cliente")
public class QuestionarioClienteDTO {

    @Schema(description = "Código único identificador do questionário", example = "123")
    private Integer codigo;

    @Schema(description = "Código identificador do cliente", example = "2")
    private Integer cliente;

    @Schema(description = "Observação do questionário", example = "Respondido antes do prazo")
    private String observacao;

    @Schema(description = "Tipo do BV. \n\n" +
            "**Valores disponíveis**\n" +
            "- MA (Matrícula)\n" +
            "- RT (Retorno)\n" +
            "- RE (Rematrícula)\n" +
            "- SS-P (Sessão - Primeira Compra)\n" +
            "- SS-R (Sessão - Retorno)", example = "MA")
    private TipoBV tipoBV;

    @Schema(description = "Data que foi respondido o questionário", example = "2025-04-30T00:00:00.000Z")
    private Date data;

    @Schema(description = "Lista de perguntas do questionário")
    private List<QuestionarioPerguntaClienteDTO> questionarioPerguntaCliente;

    @Schema(description = "Última atualização do questionário", example = "2025-04-30T00:00:00.000Z")
    private Date ultimaAtualizacao;

    @Schema(description = "Evento do questionário")
    private EventoDTO evento;

    @Schema(description = "Questionário")
    private QuestionarioDTO questionario;

    @Schema(description = "Colaborar consultor do questionário")
    private ColaboradorDTO consultor;

    @Schema(description = "Informações do FreePass")
    private ProdutoDTO freepass;

    @Schema(description = "Lista de vínculos do questionário")
    private List<VinculoDTO> vinculos;

    @Schema(description = "Responsável pelo FreePass")
    private AuthDTO responsavelFreepass;

    @Schema(description = "Responsável pela alteração de data do BV")
    private AuthDTO responsavelAlteracaoDataBv;

    @Schema(description = "Indica se deve preencher o questionário", example = "false")
    private boolean preencherQuestionario;

    @Schema(description = "Indica se possui agendamentos com o consultor atual", example = "true")
    private boolean possuiAgendamentosConsultorAtual;

    @Schema(description = "Código do consultar antes da alteração", example = "23")
    private Integer codigoConsultorAntesAlteracao;

    @Schema(description = "Indica se deve alterar o agendamento com o consultor atual", example = "false")
    private Boolean alterarAgendamentoConsultorAtual;

    @Schema(description = "Origem do sistema onde foi criado o questionário. \n\n" +
            "**Valores disponíveis**\n" +
            "- 1 (ZillyonWeb)\n" +
            "- 2 (Agenda Web)\n" +
            "- 3 (Pacto Treino)\n" +
            "- 4 (App Treino)\n" +
            "- 5 (App Professor)\n" +
            "- 6 (Autoatendimento)\n" +
            "- 7 (Site Vendas)\n" +
            "- 8 (Buzz Lead)\n" +
            "- 9 (Vendas 2.0)\n" +
            "- 10 (App do consultor)\n" +
            "- 11 (Booking Gympass)\n" +
            "- 12 (Fila de espera)\n" +
            "- 13 (Importação API)\n" +
            "- 14 (Hubspot Lead)\n" +
            "- 15 (CRM Meta Diaria)\n" +
            "- 16 (Pacto Flow)\n" +
            "- 17 (Nova Tela de Negociação)\n" +
            "- 18 (Nova Tela de Caixa Aberto)\n" +
            "- 19 (Nova Tela de Cadastro Cliente)\n" +
            "- 20 (API Sistema Pacto)\n" +
            "- 21 (Conversas IA)\n" +
            "- 22 (ZW Boot)", example = "1")
    private Integer origemSistema;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Date getUltimaAtualizacao() {
        return ultimaAtualizacao;
    }

    public void setUltimaAtualizacao(Date ultimaAtualizacao) {
        this.ultimaAtualizacao = ultimaAtualizacao;
    }

    public EventoDTO getEvento() {
        return evento;
    }

    public void setEvento(EventoDTO evento) {
        this.evento = evento;
    }

    public QuestionarioDTO getQuestionario() {
        return questionario;
    }

    public void setQuestionario(QuestionarioDTO questionario) {
        this.questionario = questionario;
    }

    public ColaboradorDTO getConsultor() {
        return consultor;
    }

    public void setConsultor(ColaboradorDTO consultor) {
        this.consultor = consultor;
    }

    public TipoBV getTipoBV() {
        return tipoBV;
    }

    public void setTipoBV(TipoBV tipoBV) {
        this.tipoBV = tipoBV;
    }

    public List<QuestionarioPerguntaClienteDTO> getQuestionarioPerguntaCliente() {
        return questionarioPerguntaCliente;
    }

    public void setQuestionarioPerguntaCliente(List<QuestionarioPerguntaClienteDTO> questionarioPerguntaCliente) {
        this.questionarioPerguntaCliente = questionarioPerguntaCliente;
    }

    public ProdutoDTO getFreepass() {
        return freepass;
    }

    public void setFreepass(ProdutoDTO freepass) {
        this.freepass = freepass;
    }

    public List<VinculoDTO> getVinculos() {
        return vinculos;
    }

    public void setVinculos(List<VinculoDTO> vinculos) {
        this.vinculos = vinculos;
    }

    public AuthDTO getResponsavelFreepass() {
        return responsavelFreepass;
    }

    public void setResponsavelFreepass(AuthDTO responsavelFreepass) {
        this.responsavelFreepass = responsavelFreepass;
    }

    public AuthDTO getResponsavelAlteracaoDataBv() {
        return responsavelAlteracaoDataBv;
    }

    public void setResponsavelAlteracaoDataBv(AuthDTO responsavelAlteracaoDataBv) {
        this.responsavelAlteracaoDataBv = responsavelAlteracaoDataBv;
    }

    public boolean isPreencherQuestionario() {
        return preencherQuestionario;
    }

    public void setPreencherQuestionario(boolean preencherQuestionario) {
        this.preencherQuestionario = preencherQuestionario;
    }

    public boolean isPossuiAgendamentosConsultorAtual() {
        return possuiAgendamentosConsultorAtual;
    }

    public void setPossuiAgendamentosConsultorAtual(boolean possuiAgendamentosConsultorAtual) {
        this.possuiAgendamentosConsultorAtual = possuiAgendamentosConsultorAtual;
    }

    public Integer getCodigoConsultorAntesAlteracao() {
        return codigoConsultorAntesAlteracao;
    }

    public void setCodigoConsultorAntesAlteracao(Integer codigoConsultorAntesAlteracao) {
        this.codigoConsultorAntesAlteracao = codigoConsultorAntesAlteracao;
    }

    public Boolean getAlterarAgendamentoConsultorAtual() {
        if (alterarAgendamentoConsultorAtual == null) {
            alterarAgendamentoConsultorAtual = false;
        }
        return alterarAgendamentoConsultorAtual;
    }

    public void setAlterarAgendamentoConsultorAtual(Boolean alterarAgendamentoConsultorAtual) {
        this.alterarAgendamentoConsultorAtual = alterarAgendamentoConsultorAtual;
    }

    public Integer getOrigemSistema() {
        if (origemSistema == null) {
            origemSistema = OrigemSistemaEnum.ZW.getCodigo();
        }
        return origemSistema;
    }

    public void setOrigemSistema(Integer origemSistema) {
        this.origemSistema = origemSistema;
    }
}
