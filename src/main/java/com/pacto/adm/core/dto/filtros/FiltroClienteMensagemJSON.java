package com.pacto.adm.core.dto.filtros;

import org.json.JSONException;
import org.json.JSONObject;

public class FiltroClienteMensagemJSON {

    private String parametro;
    private String tipoMensagem;
    private Boolean desabilitado;

    public FiltroClienteMensagemJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue").toUpperCase();
            this.desabilitado = filters.has("desabilitado") ? filters.getBoolean("desabilitado") : null;
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public String getTipoMensagem() {
        return tipoMensagem;
    }

    public void setTipoMensagem(String tipoMensagem) {
        this.tipoMensagem = tipoMensagem;
    }

    public Boolean getDesabilitado() {
        return desabilitado;
    }

    public void setDesabilitado(Boolean desabilitado) {
        this.desabilitado = desabilitado;
    }
}
