package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

@Schema(name = "Dados do Plano de um Cliente", description = "Informações de um plano de um cliente")
public class ClienteDadosPlanoDTO {

    @Schema(description = "Situação do plano do cliente", example = "ATIVO")
    private String situacao;

    @Schema(description = "Nome do plano do cliente", example = "Mensalidade de Academia")
    private String nomePlano;

    @Schema(description = "Avisos do plano", example = "RENOVAR DE FORMA AUTOMÁTICA")
    private Integer avisos;

    @Schema(description = "Código de observações do plano", example = "58")
    private Integer observacoes;

    @Schema(description = "Data de cadastro do plano (Timestamp)", example = "")
    private Long dataCadastro;

    @Schema(description = "Data de matrícula (Timestamp)", example = "")
    private Long dataMatricula;

    @Schema(description = "Data de remetrícula (Timestamp)", example = "")
    private Long dataRematricula;

    @Schema(description = "Data de início do vínculo atual (Timestamp)", example = "")
    private Date inicioVinculoAtual;

    @Schema(description = "Lista de colaboradores vinculados ao contrato do cliente")
    private List<VinculoDTO> vinculos;

    @Schema(description = "Indica se o contrato é Gym Pass", example = "false")
    private boolean gympass;

    @Schema(description = "Indica se o contrato é Total Pass", example = "false")
    private boolean totalpass;

    @Schema(description = "Nome do titular do plano", example = "Renato Alves Cariri")
    private String titularContratoNome;

    @Schema(description = "Código do titular do plano", example = "554")
    private String titularContratoMatricula;

    @Schema(description = "Valor da receita do cliente", example = "1200.00")
    private Double valorReceitaCliente;

    @Schema(description = "Valor de receita média do cliente", example = "1200.00")
    private Double valorReceitaMediaCliente;

    @Schema(description = "Indica se deve autalizar o Bv", example = "false")
    private boolean atualizarBv;

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public Long getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Long dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Long getDataMatricula() {
        return dataMatricula;
    }

    public void setDataMatricula(Long dataMatricula) {
        this.dataMatricula = dataMatricula;
    }

    public Long getDataRematricula() {
        return dataRematricula;
    }

    public void setDataRematricula(Long dataRematricula) {
        this.dataRematricula = dataRematricula;
    }

    public List<VinculoDTO> getVinculos() {
        return vinculos;
    }

    public void setVinculos(List<VinculoDTO> vinculos) {
        this.vinculos = vinculos;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getAvisos() {
        return avisos;
    }

    public void setAvisos(Integer avisos) {
        this.avisos = avisos;
    }

    public Integer getObservacoes() {
        return observacoes;
    }

    public void setObservacoes(Integer observacoes) {
        this.observacoes = observacoes;
    }

    public boolean isGympass() {
        return gympass;
    }

    public void setGympass(boolean gympass) {
        this.gympass = gympass;
    }

    public boolean isTotalpass() {
        return totalpass;
    }

    public void setTotalpass(boolean totalpass) {
        this.totalpass = totalpass;
    }

    public Date getInicioVinculoAtual() {
        return inicioVinculoAtual;
    }

    public void setInicioVinculoAtual(Date inicioVinculoAtual) {
        this.inicioVinculoAtual = inicioVinculoAtual;
    }

    public String getTitularContratoNome() {
        return titularContratoNome;
    }

    public void setTitularContratoNome(String titularContratoNome) {
        this.titularContratoNome = titularContratoNome;
    }

    public String getTitularContratoMatricula() {
        return titularContratoMatricula;
    }

    public void setTitularContratoMatricula(String titularContratoMatricula) {
        this.titularContratoMatricula = titularContratoMatricula;
    }

    public Double getValorReceitaCliente() {
        return valorReceitaCliente;
    }

    public void setValorReceitaCliente(Double valorReceitaCliente) {
        this.valorReceitaCliente = valorReceitaCliente;
    }

    public Double getValorReceitaMediaCliente() {
        return valorReceitaMediaCliente;
    }

    public void setValorReceitaMediaCliente(Double valorReceitaMediaCliente) {
        this.valorReceitaMediaCliente = valorReceitaMediaCliente;
    }

    public boolean isAtualizarBv() {
        return atualizarBv;
    }

    public void setAtualizarBv(boolean atualizarBv) {
        this.atualizarBv = atualizarBv;
    }
}
