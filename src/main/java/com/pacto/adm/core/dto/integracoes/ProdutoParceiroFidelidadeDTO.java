package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
public class ProdutoParceiroFidelidadeDTO {


    @Schema(description = "Código único identificador do produto no parceiro de fidelidade", example = "1")
    private Integer codigo;

    @Schema(description = "Descrição detalhada do produto", example = "Assinatura Premium de 1 Mês")
    private String descricao;

    @Schema(description = "Quantidade de pontos necessários para resgatar o produto", example = "500")
    private Integer pontos;

    @Schema(description = "Valor monetário associado ao produto", example = "99.99")
    private Double valor;

    @Schema(description = "Código externo do produto no sistema do parceiro de fidelidade", example = "PROD-EXT-001")
    private String codigoExterno;

    @Schema(description = "Código do parceiro de fidelidade associado ao produto", example = "10")
    private Integer parceiroFidelidade;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public Integer getParceiroFidelidade() {
        return parceiroFidelidade;
    }

    public void setParceiroFidelidade(Integer parceiroFidelidade) {
        this.parceiroFidelidade = parceiroFidelidade;
    }
}
