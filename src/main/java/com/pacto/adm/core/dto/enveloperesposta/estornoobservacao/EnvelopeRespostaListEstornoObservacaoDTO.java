package com.pacto.adm.core.dto.enveloperesposta.estornoobservacao;

import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.estornoobservacao.EstornoObservacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas contendo uma lista de estornos de observação")
public class EnvelopeRespostaListEstornoObservacaoDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações dos estornos de observação encontrados")
    private List<EstornoObservacaoDTO> content;

    public List<EstornoObservacaoDTO> getContent() {
        return content;
    }

    public void setContent(List<EstornoObservacaoDTO> content) {
        this.content = content;
    }
}
