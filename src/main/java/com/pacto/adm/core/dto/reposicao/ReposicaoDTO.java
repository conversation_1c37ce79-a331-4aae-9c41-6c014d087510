package com.pacto.adm.core.dto.reposicao;

import com.pacto.adm.core.dto.ClienteDTO;
import com.pacto.adm.core.dto.ContratoDTO;
import com.pacto.adm.core.dto.HorarioTurmaDTO;
import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.conviteaulaexperimental.ConviteAulaExperimentalDTO;
import com.pacto.adm.core.dto.turma.TurmaDTO;
import com.pacto.adm.core.enumerador.OrigemSistemaEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(name = "Reposicao", description = "Informações detalhadas sobre a reposição de aula, incluindo dados do cliente, contrato e horários.")
public class ReposicaoDTO {

    @Schema(description = "Código único identificador da reposição.", example = "2001")
    private Integer codigo;

    @Schema(description = "Data de lançamento da reposição.", example = "2023-07-12T00:00:00Z")
    private Date dataLancamento;

    @Schema(description = "Data original da aula antes de ser desmarcada.", example = "2023-07-10T08:00:00Z")
    private Date dataOrigem;

    @Schema(description = "Data da presença do aluno na reposição.", example = "2023-07-15T08:00:00Z")
    private Date dataPresenca;

    @Schema(description = "Data da reposição da aula.", example = "2023-07-15T08:00:00Z")
    private Date dataReposicao;

    @Schema(description = "Indica se a reposição é marcada para a aula.", example = "true")
    private Boolean marcacaoAula;

    @Schema(description = "Origem do sistema que gerou a reposição." +
            "Tipos de origem: 1 - ZillyonWeb, 2 - Agenda Web, 3 - Pacto Treino, 4 - App Treino, 5 - App Professor, 6 - Autoatendimento, 7 - Site Vendas, 8 - Buzz Lead, 9 - Vendas 2.0, 10 - App do consultor, 11 - Booking Gympass, 12 - Fila de espera, 13 - Importação API, 14 - Hubspot Lead, 15 - CRM Meta Diaria, 16 - Pacto Flow, 17 - Nova Tela de Negociação",
            example = "1",implementation = OrigemSistemaEnum.class)
    private OrigemSistemaEnum origemSistema;

    @Schema(description = "ID do evento Spivi associado à reposição.", example = "3001")
    private Integer spiviEventId;

    @Schema(description = "ID do SEAR Spivi associado à reposição.", example = "4001")
    private Integer spiviSearId;

    @Schema(description = "Informações do cliente associado à reposição.")
    private ClienteDTO cliente;

    @Schema(description = "Contrato associado à reposição de aula.")
    private ContratoDTO contrato;

    @Schema(description = "Detalhes do convite para aula experimental, se aplicável.")
    private ConviteAulaExperimentalDTO conviteAulaExperimental;

    @Schema(description = "Horário da turma relacionado à reposição.")
    private HorarioTurmaDTO horarioTurma;

    @Schema(description = "Horário da turma original antes da reposição.")
    private HorarioTurmaDTO horarioTurmaOrigem;

    @Schema(description = "Turma de destino da reposição.")
    private TurmaDTO turmaDestino;

    @Schema(description = "Turma original antes da reposição.")
    private TurmaDTO turmaOrigem;

    @Schema(description = "Informações do usuário que registrou a reposição.")
    private UsuarioDTO usuario;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataOrigem() {
        return dataOrigem;
    }

    public void setDataOrigem(Date dataOrigem) {
        this.dataOrigem = dataOrigem;
    }

    public Date getDataPresenca() {
        return dataPresenca;
    }

    public void setDataPresenca(Date dataPresenca) {
        this.dataPresenca = dataPresenca;
    }

    public Date getDataReposicao() {
        return dataReposicao;
    }

    public void setDataReposicao(Date dataReposicao) {
        this.dataReposicao = dataReposicao;
    }

    public Boolean getMarcacaoAula() {
        return marcacaoAula;
    }

    public void setMarcacaoAula(Boolean marcacaoAula) {
        this.marcacaoAula = marcacaoAula;
    }

    public OrigemSistemaEnum getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }

    public Integer getSpiviEventId() {
        return spiviEventId;
    }

    public void setSpiviEventId(Integer spiviEventId) {
        this.spiviEventId = spiviEventId;
    }

    public Integer getSpiviSearId() {
        return spiviSearId;
    }

    public void setSpiviSearId(Integer spiviSearId) {
        this.spiviSearId = spiviSearId;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoDTO contrato) {
        this.contrato = contrato;
    }

    public ConviteAulaExperimentalDTO getConviteAulaExperimental() {
        return conviteAulaExperimental;
    }

    public void setConviteAulaExperimental(ConviteAulaExperimentalDTO conviteAulaExperimental) {
        this.conviteAulaExperimental = conviteAulaExperimental;
    }

    public HorarioTurmaDTO getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaDTO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public HorarioTurmaDTO getHorarioTurmaOrigem() {
        return horarioTurmaOrigem;
    }

    public void setHorarioTurmaOrigem(HorarioTurmaDTO horarioTurmaOrigem) {
        this.horarioTurmaOrigem = horarioTurmaOrigem;
    }

    public TurmaDTO getTurmaDestino() {
        return turmaDestino;
    }

    public void setTurmaDestino(TurmaDTO turmaDestino) {
        this.turmaDestino = turmaDestino;
    }

    public TurmaDTO getTurmaOrigem() {
        return turmaOrigem;
    }

    public void setTurmaOrigem(TurmaDTO turmaOrigem) {
        this.turmaOrigem = turmaOrigem;
    }

    public UsuarioDTO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioDTO usuario) {
        this.usuario = usuario;
    }
}
