package com.pacto.adm.core.dto.enveloperesposta.modalidade.contrato;

import com.pacto.adm.core.dto.negociacao.ContratoModalidadeWS;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaContratoModalidadeWS {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ContratoModalidadeWS content;

    public ContratoModalidadeWS getContent() {
        return content;
    }

    public void setContent(ContratoModalidadeWS content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 5, "
                    + "\"modalidade\": \"Musculação\", "
                    + "\"nrVezesSemana\": 3";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";



}
