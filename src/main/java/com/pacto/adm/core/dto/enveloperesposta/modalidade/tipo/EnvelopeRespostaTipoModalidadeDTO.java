package com.pacto.adm.core.dto.enveloperesposta.modalidade.tipo;

import com.pacto.adm.core.dto.TipoModalidadeDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaTipoModalidadeDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private TipoModalidadeDTO content;

    public TipoModalidadeDTO getContent() {
        return content;
    }

    public void setContent(TipoModalidadeDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 100, "
                    + "\"nome\": \"Individual\", "
                    + "\"identificador\": 1";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
