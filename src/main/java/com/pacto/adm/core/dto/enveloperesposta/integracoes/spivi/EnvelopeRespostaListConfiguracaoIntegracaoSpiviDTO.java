package com.pacto.adm.core.dto.enveloperesposta.integracoes.spivi;

import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoSpiviDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public class EnvelopeRespostaListConfiguracaoIntegracaoSpiviDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ConfiguracaoIntegracaoSpiviDTO> content;

    public List<ConfiguracaoIntegracaoSpiviDTO> getContent() {
        return content;
    }

    public void setContent(List<ConfiguracaoIntegracaoSpiviDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaConfiguracaoIntegracaoSpiviDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";


}
