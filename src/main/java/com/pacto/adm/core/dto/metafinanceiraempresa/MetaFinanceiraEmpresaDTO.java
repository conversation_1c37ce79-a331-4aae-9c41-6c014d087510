package com.pacto.adm.core.dto.metafinanceiraempresa;

import io.swagger.v3.oas.annotations.media.Schema;

public class MetaFinanceiraEmpresaDTO {

    @Schema(description = "Código identificador único da meta financeira da empresa", example = "1")
    private Integer codigo;

    @Schema(description = "Descrição detalhada da meta financeira estabelecida para a empresa", example = "Meta de faturamento mensal para expansão")
    private String descricao;

    @Schema(description = "Mês de referência da meta financeira (1-12)", example = "10")
    private Integer mes;

    @Schema(description = "Ano de referência da meta financeira", example = "2024")
    private Integer ano;

    public MetaFinanceiraEmpresaDTO(Integer codigo, String descricao, Integer mes, Integer ano) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.mes = mes;
        this.ano = ano;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }
}
