package com.pacto.adm.core.dto.enveloperesposta.modalidade;

import com.pacto.adm.core.dto.modalidade.ModalidadeEmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaModalidadeEmpresaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ModalidadeEmpresaDTO content;

    public ModalidadeEmpresaDTO getContent() {
        return content;
    }

    public void setContent(ModalidadeEmpresaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 110, "
                    + "\"empresa\": 2001, "
                    + "\"nomeEmpresa\": \"Academia Top Fit\", "
                    + "\"modalidade\": 90";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
