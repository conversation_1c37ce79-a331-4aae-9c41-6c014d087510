package com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;


//@Schema(name = "Resposta Lista de Inteiros", description = "Representação das respostas das requisições que devolvem uma lista de números")
public class EnvelopeRespostaListInteger {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas", example = "[1, 2, 3, 4]")
    private List<Integer> content;

    public List<Integer> getContent() {
        return content;
    }

    public void setContent(List<Integer> content) {
        this.content = content;
    }

    public static final String resposta = "{\n  \"content\": [1, 2, 3, 4]}";

}
