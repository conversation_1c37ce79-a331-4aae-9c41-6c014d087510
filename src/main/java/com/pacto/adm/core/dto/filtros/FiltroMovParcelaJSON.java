package com.pacto.adm.core.dto.filtros;

import org.json.JSONObject;

import java.util.Objects;

public class FiltroMovParcelaJSON {
    private boolean ignorarEmRemessa;
    private String situacao;
    private boolean ignorarParcelasMultaJuros;


    public FiltroMovParcelaJSON(JSONObject filters) {
        if (filters != null) {
            ignorarEmRemessa = filters.optBoolean("ignorarEmRemessa");
            ignorarParcelasMultaJuros = filters.optBoolean("ignorarParcelasMultaJuros");
            situacao = filters.optString("situacao");
        }
    }

    public boolean isIgnorarEmRemessa() {
        return ignorarEmRemessa;
    }

    public void setIgnorarEmRemessa(boolean ignorarEmRemessa) {
        this.ignorarEmRemessa = ignorarEmRemessa;
    }

    public boolean isIgnorarParcelasMultaJuros() {
        return ignorarParcelasMultaJuros;
    }

    public void setIgnorarParcelasMultaJuros(boolean ignorarParcelasMultaJuros) {
        this.ignorarParcelasMultaJuros = ignorarParcelasMultaJuros;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        FiltroMovParcelaJSON that = (FiltroMovParcelaJSON) o;
        return isIgnorarEmRemessa() == that.isIgnorarEmRemessa() && isIgnorarParcelasMultaJuros() == that.isIgnorarParcelasMultaJuros() && Objects.equals(getSituacao(), that.getSituacao());
    }

    @Override
    public int hashCode() {
        return 0;
    }
}
