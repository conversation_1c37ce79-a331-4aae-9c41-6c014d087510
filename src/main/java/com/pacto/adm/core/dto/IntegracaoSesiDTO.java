package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.enumerador.sesi.FuncionalidadeSesiEnum;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Integração SESI", description = "Informações da Integração SESI")
public class IntegracaoSesiDTO {

    @Schema(description = "Código único identificador da integração", example = "1")
    private Integer codigo;

    @Schema(description = "message ID", example = "id01293018")
    private String messageID;

    @Schema(description = "Data da requisição (timestamp)", example = "1746796800000")
    private Long dataRequisicao;

    @Schema(description = "Data do retorno (timestamp)", example = "1746796800000")
    private Long dataRetorno;

    @Schema(description = "XML enviado", example = "<xml><integracao>INFORMACOES DA INTEGRACAO</integracao> </xml>")
    private String xmlEnviado;

    @Schema(description = "Código identificador da entidade", example = "2")
    private String codigoEntidade;

    @Schema(description = "Chave da emrpesa", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9")
    private String key_empresa;

    @Schema(description = "Funcionalidade SESI que está sendo integrada", example = "INTEGRACAO_PIX", implementation = FuncionalidadeSesiEnum.class)
    private FuncionalidadeSesiEnum funcionalidade;

    @Schema(description = "Data da requisição", example = "2025-05-09")
    private String dtRequisicao;

    @Schema(description = "Aluno", example = "Renato Alves Cariri")
    private String aluno;

    @Schema(description = "Tipo da solicitação de integração (nome da funcionalidade)" +
            "<strong>Valores disponíveis</strong>" +
            "<ul>" +
            "<li>1 - PESSOA (Pessoa)</li>" +
            "<li>2 - NOTA_FISCAL (Nota Fiscal)</li>" +
            "<li>3 - TESOURARIA_INTEGRACAO_DINHEIRO (Tesouraria Integracao Dinheiro)</li>" +
            "<li>4 - TESOURARIA_INTEGRACAO_CONSULTA (Tesouraria Integracao Consulta)</li>" +
            "<li>5 - INTEGRACAO_PIX (Integracao PIX)</li>" +
            "<li>6 - INTEGRACAO_CARTAO (Integracao Cartão)</li>" +
            "<li>7 - INTEGRACAO_DEPOSITO (Integracao Deposito)</li>" +
            "</ul>", example = "Integração SESI")
    private String tipoSolicitacao;

    @Schema(description = "Código identificador", example = "1")
    private String id;

    @Schema(description = "Status da Integração", example = "EN")
    private String status;

    @Schema(description = "Descrição do status", example = "Enviado")
    private String statusDescricao;

    @Schema(description = "Data de retorno da solicitação", example = "2025-05-09")
    private String dtRetorno;

    @Schema(description = "Resultado", example = "INTEGRADO")
    private String resultado;

    @Schema(description = "Solicitação", example = "solicitação de integração PIX")
    private String solicitacao;

    @Schema(description = "Resposta da integração", example = "Integrado")
    private String resposta;

    @Schema(description = "Código da empresa que está sendo integrada", example = "1")
    private Integer codigoEmpresa;

    @Schema(description = "Nome da empresa que está sendo integrada", example = "ACADEMIA PACTO")
    private String nomeEmpresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getMessageID() {
        return messageID;
    }

    public void setMessageID(String messageID) {
        this.messageID = messageID;
    }

    public Long getDataRequisicao() {
        return dataRequisicao;
    }

    public void setDataRequisicao(Long dataRequisicao) {
        this.dataRequisicao = dataRequisicao;
    }

    public Long getDataRetorno() {
        return dataRetorno;
    }

    public void setDataRetorno(Long dataRetorno) {
        this.dataRetorno = dataRetorno;
    }

    public String getXmlEnviado() {
        return xmlEnviado;
    }

    public void setXmlEnviado(String xmlEnviado) {
        this.xmlEnviado = xmlEnviado;
    }

    public String getCodigoEntidade() {
        return codigoEntidade;
    }

    public void setCodigoEntidade(String codigoEntidade) {
        this.codigoEntidade = codigoEntidade;
    }

    public String getKey_empresa() {
        return key_empresa;
    }

    public void setKey_empresa(String key_empresa) {
        this.key_empresa = key_empresa;
    }

    public FuncionalidadeSesiEnum getFuncionalidade() {
        return funcionalidade;
    }

    public void setFuncionalidade(FuncionalidadeSesiEnum funcionalidade) {
        this.funcionalidade = funcionalidade;
    }

    public String getDtRequisicao() {
        return dtRequisicao;
    }

    public void setDtRequisicao(String dtRequisicao) {
        this.dtRequisicao = dtRequisicao;
    }

    public String getAluno() {
        return aluno;
    }

    public void setAluno(String aluno) {
        this.aluno = aluno;
    }

    public String getTipoSolicitacao() {
        return tipoSolicitacao;
    }

    public void setTipoSolicitacao(String tipoSolicitacao) {
        this.tipoSolicitacao = tipoSolicitacao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDtRetorno() {
        return dtRetorno;
    }

    public void setDtRetorno(String dtRetorno) {
        this.dtRetorno = dtRetorno;
    }

    public String getResultado() {
        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }

    public String getSolicitacao() {
        return solicitacao;
    }

    public void setSolicitacao(String solicitacao) {
        this.solicitacao = solicitacao;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getStatusDescricao() {
        return statusDescricao;
    }

    public void setStatusDescricao(String statusDescricao) {
        this.statusDescricao = statusDescricao;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }
}
