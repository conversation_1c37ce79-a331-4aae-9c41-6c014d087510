package com.pacto.adm.core.dto;

import java.util.Date;

public class ClientesCanceladosDTO {

    private Integer codigoCliente;
    private String matriculaCliente;
    private String nomeCliente;
    private Integer codigoContrato;
    private String descricaoJustificativa;
    private String responsavelOperacao;
    private Date dataOperacao;

    public ClientesCanceladosDTO(
            Integer codigoCliente, String matriculaCliente, String nomeCliente, Integer codigoContrato,
            String descricaoJustificativa, String responsavelOperacao, Date dataOperacao
    ) {
        this.codigoCliente = codigoCliente;
        this.matriculaCliente = matriculaCliente;
        this.nomeCliente = nomeCliente;
        this.codigoContrato = codigoContrato;
        this.descricaoJustificativa = descricaoJustificativa;
        this.responsavelOperacao = responsavelOperacao;
        this.dataOperacao = dataOperacao;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    public void setMatriculaCliente(String matriculaCliente) {
        this.matriculaCliente = matriculaCliente;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getDescricaoJustificativa() {
        return descricaoJustificativa;
    }

    public void setDescricaoJustificativa(String descricaoJustificativa) {
        this.descricaoJustificativa = descricaoJustificativa;
    }

    public String getResponsavelOperacao() {
        return responsavelOperacao;
    }

    public void setResponsavelOperacao(String responsavelOperacao) {
        this.responsavelOperacao = responsavelOperacao;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }
}
