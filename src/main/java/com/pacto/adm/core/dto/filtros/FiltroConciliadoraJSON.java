package com.pacto.adm.core.dto.filtros;

import org.json.JSONObject;

import java.util.Date;

public class FiltroConciliadoraJSON {
    private String parametro;
    private Date periodoDe;
    private Date periodoAte;
    private Integer recibo;

    public FiltroConciliadoraJSON(JSONObject filters) {
        if (filters != null) {
            this.parametro = filters.optString("quickSearchValue").toUpperCase();
            if(filters.optLong("periodoDe") != 0) {
                this.periodoDe = new Date(filters.optLong("periodoDe"));
            }
            if(filters.optLong("periodoAte") != 0) {
                this.periodoAte = new Date(filters.optLong("periodoAte"));
            }
            this.recibo = filters.optInt("recibo");
        }
    }

    public String getParametro() { return parametro; }

    public void setParametro(String parametro) { this.parametro = parametro; }

    public Date getPeriodoDe() {
        return periodoDe;
    }

    public void setPeriodoDe(Date periodoDe) {
        this.periodoDe = periodoDe;
    }

    public Date getPeriodoAte() {
        return periodoAte;
    }

    public void setPeriodoAte(Date periodoAte) {
        this.periodoAte = periodoAte;
    }

    public Integer getRecibo() {
        return recibo;
    }

    public void setRecibo(Integer recibo) {
        this.recibo = recibo;
    }
}
