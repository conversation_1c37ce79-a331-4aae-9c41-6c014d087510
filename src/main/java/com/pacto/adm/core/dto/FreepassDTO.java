package com.pacto.adm.core.dto;

import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Produto;
import com.pacto.config.utils.Calendario;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;
import java.util.Date;
@Schema(name = "Free Pass", description = "Informações do Free Pass")
public class FreepassDTO {
    @Schema(description = "Código identificador do cliente que realizou o acesso com Free Pass", example = "23")
    private Integer cliente;
    @Schema(description = "Código identificador do produto que o cliente acessou por Free Pass", example = "2")
    private Integer produto;
    @Schema(description = "Data de início do acesso via FreePass", example = "2025-05-09")
    private LocalDate dataInicio;
    @Schema(description = "Número de dias do acesso", example = "1")
    private Integer nrDias;
    @Schema(description = "Código único identificador do responsável pelo cadastro via Free Pass", example = "1")
    private Integer responsavel;
    @Schema(description = "Indica se existe o Free Pass", example = "true")
    private boolean validarExisteFreepass = true;

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public LocalDate getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(LocalDate dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Integer getNrDias() {
        return nrDias;
    }

    public void setNrDias(Integer nrDias) {
        this.nrDias = nrDias;
    }

    public Integer getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(Integer responsavel) {
        this.responsavel = responsavel;
    }

    public boolean isValidarExisteFreepass() {
        return validarExisteFreepass;
    }

    public void setValidarExisteFreepass(boolean validarExisteFreepass) {
        this.validarExisteFreepass = validarExisteFreepass;
    }

    public PeriodoAcessoClienteDTO toPeriodoAcessoClienteDTO(Cliente cliente, Produto produto) {
        PeriodoAcessoClienteDTO dto = new PeriodoAcessoClienteDTO();

        dto.setDataInicioAcesso(Calendario.toDate(dataInicio));
        dto.setDataFinalAcesso(Calendario.somarDias(Calendario.toDate(getDataInicio()), cliente.getFreepass().getNrDiasVigencia() - 1));
        dto.setPessoa(cliente.getPessoa().getCodigo());
        dto.setTipoAcesso("PL");
        dto.setResponsavel(getResponsavel());
        dto.setDataLancamento(new Date());
        dto.setProduto(produto.getCodigo());

        return dto;
    }
}
