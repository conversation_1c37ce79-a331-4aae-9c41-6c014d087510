package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Cliente Dados GymPass", description = "Informações dos dados do GymPass de um cliente")
public class ClienteDadosGymPassDTO {

    @Schema(description = "Tipo do número do GymPass do cliente", example = "1231934")
    private String gymPassTypeNumber;

    @Schema(description = "Token único do GymPass do cliente", example = "Token-1239123091")
    private String gymPassUniqueToken;

    public String getGymPassTypeNumber() {
        return gymPassTypeNumber;
    }

    public void setGymPassTypeNumber(String gymPassTypeNumber) {
        this.gymPassTypeNumber = gymPassTypeNumber;
    }

    public String getGymPassUniqueToken() {
        return gymPassUniqueToken;
    }

    public void setGymPassUniqueToken(String gymPassUniqueToken) {
        this.gymPassUniqueToken = gymPassUniqueToken;
    }
}
