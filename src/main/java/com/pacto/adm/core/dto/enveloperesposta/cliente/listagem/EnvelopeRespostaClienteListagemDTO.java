package com.pacto.adm.core.dto.enveloperesposta.cliente.listagem;

import com.pacto.adm.core.dto.ClienteListagemDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Listagem de Clientes", description = "Representação das respostas envolvendo uma lista de clientes")
public class EnvelopeRespostaClienteListagemDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ClienteListagemDTO content;

    public ClienteListagemDTO getContent() {
        return content;
    }

    public void setContent(ClienteListagemDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"nome\": \"Renato Alves Cariani\", "
                    + "\"matricula\": \"001023\", "
                    + "\"urlFoto\": \"www.sistemapacto.com.br/arquivos/renato-alves-cariani.png\", "
                    + "\"categoria\": \"Experiência Pacto\", "
                    + "\"inicioContrato\": 1745971200000, "
                    + "\"fimContrato\": 1756512000000, "
                    + "\"codigoCliente\": 97, "
                    + "\"empresa\": \"1\", "
                    + "\"situacao\": \"ATIVO\", "
                    + "\"situacaoContrato\": \"ATIVO\", "
                    + "\"telefone\": \"(99)1234500000\", "
                    + "\"email\": \"<EMAIL>\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";



}
