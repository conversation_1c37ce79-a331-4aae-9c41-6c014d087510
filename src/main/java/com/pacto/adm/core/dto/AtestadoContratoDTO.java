package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(name = "Atestado Contrato", description = "Informações do Atestado e do Contrato vinculados")
public class AtestadoContratoDTO {

    @Schema(description = "Contrato vinculado ao atestado contrato")
    private ContratoDTO contrato;

    @Schema(description = "Código da empresa", example = "true")
    private Integer empresa;

    @Schema(description = "Data de início", example = "2025-04-08T14:30:00Z")
    private Date dataInicio;
    @Schema(description = "Data de termíno", example = "2025-04-08T14:30:00Z")
    private Date dataTermino;
    @Schema(description = "Data de início do retorno", example = "2025-04-08T14:30:00Z")
    private Date dataInicioRetorno;
    @Schema(description = "Data de termíno do retorno", example = "2025-04-08T14:30:00Z")
    private Date dataTerminoRetorno;
    @Schema(description = "Data de registro", example = "2025-04-08T14:30:00Z")
    private Date dataRegistro;
    @Schema(description = "Justificativa da operação")
    private JustificativaOperacaoDTO justificativaOperacao;

    @Schema(description = "Número de dias", example = "2")
    private Integer nrDias;

    @Schema(description = "Número de dias do atestado", example = "2")
    private Integer nrDiasAtestado;

    @Schema(description = "Número de dias para somar", example = "0")
    private Integer nrDiasASomar = 0;

    @Schema(description = "Observações", example = "ATESTADO VINCULADO AO CONTRATO")
    private String observacao;

    @Schema(description = "Responsável pela operação")
    private UsuarioDTO responsavelOperacao;

    @Schema(description = "Indica se é necessário apresentar o período do atestado", example = "true")
    private Boolean apresentarPeriodoAtestado;

    @Schema(description = "Indica se a quantidade de dias do atestado é maior do que a do contrato", example = "true")
    private Boolean qtdDiasAtestadoMaiorQueContrato;

    @Schema(description = "Indica se o contrato venceu", example = "false")
    private Boolean contratoVencido;

    @Schema(description = "Operação do contrato")
    private ContratoOperacaoDTO contratoOperacao;

    @Schema(description = "Chave do Arquivo", example = "1234")
    private String chaveArquivo;

    @Schema(description = "Nome do arquivo", example = "contrato")
    private String nomeArquivo;

    @Schema(description = "Formato do arquivo", example = "PDF")
    private String formatoArquivo;

    @Schema(description = "Dados do arquivo", example = "Contrato")
    private String dadosArquivo;

    public AtestadoContratoDTO() {
        inicializarDados();
    }

    private void inicializarDados() {
        this.apresentarPeriodoAtestado = false;
        this.qtdDiasAtestadoMaiorQueContrato = false;
        this.contratoVencido = false;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoDTO contrato) {
        this.contrato = contrato;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public Date getDataInicioRetorno() {
        return dataInicioRetorno;
    }

    public void setDataInicioRetorno(Date dataInicioRetorno) {
        this.dataInicioRetorno = dataInicioRetorno;
    }

    public Date getDataTerminoRetorno() {
        return dataTerminoRetorno;
    }

    public void setDataTerminoRetorno(Date dataTerminoRetorno) {
        this.dataTerminoRetorno = dataTerminoRetorno;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public JustificativaOperacaoDTO getJustificativaOperacao() {
        return justificativaOperacao;
    }

    public void setJustificativaOperacao(JustificativaOperacaoDTO justificativaOperacao) {
        this.justificativaOperacao = justificativaOperacao;
    }

    public Integer getNrDias() {
        return nrDias;
    }

    public void setNrDias(Integer nrDias) {
        this.nrDias = nrDias;
    }

    public Integer getNrDiasAtestado() {
        return nrDiasAtestado;
    }

    public void setNrDiasAtestado(Integer nrDiasAtestado) {
        this.nrDiasAtestado = nrDiasAtestado;
    }

    public Integer getNrDiasASomar() {
        return nrDiasASomar;
    }

    public void setNrDiasASomar(Integer nrDiasASomar) {
        this.nrDiasASomar = nrDiasASomar;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public UsuarioDTO getResponsavelOperacao() {
        return responsavelOperacao;
    }

    public void setResponsavelOperacao(UsuarioDTO responsavelOperacao) {
        this.responsavelOperacao = responsavelOperacao;
    }

    public Boolean getApresentarPeriodoAtestado() {
        return apresentarPeriodoAtestado;
    }

    public void setApresentarPeriodoAtestado(Boolean apresentarPeriodoAtestado) {
        this.apresentarPeriodoAtestado = apresentarPeriodoAtestado;
    }

    public Boolean getQtdDiasAtestadoMaiorQueContrato() {
        return qtdDiasAtestadoMaiorQueContrato;
    }

    public void setQtdDiasAtestadoMaiorQueContrato(Boolean qtdDiasAtestadoMaiorQueContrato) {
        this.qtdDiasAtestadoMaiorQueContrato = qtdDiasAtestadoMaiorQueContrato;
    }

    public boolean isContratoVencido() {
        return contratoVencido;
    }

    public void setContratoVencido(boolean contratoVencido) {
        this.contratoVencido = contratoVencido;
    }

    public ContratoOperacaoDTO getContratoOperacao() {
        return contratoOperacao;
    }

    public void setContratoOperacao(ContratoOperacaoDTO contratoOperacao) {
        this.contratoOperacao = contratoOperacao;
    }

    public String getChaveArquivo() {
        return chaveArquivo;
    }

    public void setChaveArquivo(String chaveArquivo) {
        this.chaveArquivo = chaveArquivo;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getFormatoArquivo() {
        return formatoArquivo;
    }

    public void setFormatoArquivo(String formatoArquivo) {
        this.formatoArquivo = formatoArquivo;
    }

    public String getDadosArquivo() {
        return dadosArquivo;
    }

    public void setDadosArquivo(String dadosArquivo) {
        this.dadosArquivo = dadosArquivo;
    }
}
