package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoPjBankDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoPjBankDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoPjBankDTO content;

    public ConfiguracaoIntegracaoPjBankDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoPjBankDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"ddPjbank\": 11, "
                    + "\"fonePjbank\": *********, "
                    + "\"valueConta\": 123456, "
                    + "\"emailPjbank\": \"<EMAIL>\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public static final String atributosSemEmpresa =
            "\"ddPjbank\": 11, "
                    + "\"fonePjbank\": *********, "
                    + "\"valueConta\": 123456, "
                    + "\"emailPjbank\": \"<EMAIL>\", "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
