package com.pacto.adm.core.dto.filtros;

import com.pacto.adm.core.enumerador.TipoComprovanteVacinaEnum;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class FiltroComprovanteVacinaJSON {
    private String parametro;
    private List<TipoComprovanteVacinaEnum> tiposComprovantes;

    public FiltroComprovanteVacinaJSON(JSONObject filters) {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue").toUpperCase();

            tiposComprovantes = new ArrayList<>();
            if (filters.optJSONArray("tipos") != null) {
                filters.optJSONArray("tipos").forEach(
                        (situacao) -> {
                            TipoComprovanteVacinaEnum tipo = TipoComprovanteVacinaEnum.getInstance(situacao.toString());
                            this.tiposComprovantes.add(tipo);
                        }
                );
            }
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public List<TipoComprovanteVacinaEnum> getTiposComprovantes() {
        return tiposComprovantes;
    }

    public void setTiposComprovantes(List<TipoComprovanteVacinaEnum> tiposComprovantes) {
        this.tiposComprovantes = tiposComprovantes;
    }
}