package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(description = "Informações de usuário", name = "Usuario")
public class UsuarioDTO {

    @Schema(description = "Código único identificador do usuário", example = "1")
    private Integer codigo;

    @Schema(description = "Nome completo do usuário", example = "João Silva")
    private String nome;
    private ColaboradorDTO colaborador;

    public UsuarioDTO() {}

    public UsuarioDTO(String nome) {
        this.nome = nome;
    }

    public UsuarioDTO(Integer codigo, ColaboradorDTO colaborador) {
        this.codigo = codigo;
        this.colaborador = colaborador;
    }


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public ColaboradorDTO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorDTO colaborador) {
        this.colaborador = colaborador;
    }
}
