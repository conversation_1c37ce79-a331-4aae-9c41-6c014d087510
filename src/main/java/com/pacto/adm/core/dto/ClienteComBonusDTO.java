package com.pacto.adm.core.dto;

public class ClienteComBonusDTO {

    private ClienteDTO cliente;
    private ContratoOperacaoDTO contratoOperacao;
    private ContratoDTO contrato;

    public ClienteComBonusDTO(ClienteDTO cliente, ContratoDTO contrato, ContratoOperacaoDTO contratoOperacao) {
        this.cliente = cliente;
        this.contrato = contrato;
        this.contratoOperacao = contratoOperacao;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public ContratoOperacaoDTO getContratoOperacao() {
        return contratoOperacao;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }
}
