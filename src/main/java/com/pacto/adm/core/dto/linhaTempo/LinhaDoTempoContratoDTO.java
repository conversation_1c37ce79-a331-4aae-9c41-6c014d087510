package com.pacto.adm.core.dto.linhaTempo;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Linha do Tempo de Contrato", description = "Informações da linha do tempo de um contrato")
public class LinhaDoTempoContratoDTO {

    @Schema(description = "Código do contrato do cliente", example = "123")
    private Integer contrato;

    @Schema(description = "Plano do contrato", example = "ACADEMIA")
    private String plano;

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public String getPlano() {
        return plano;
    }

    public void setPlano(String plano) {
        this.plano = plano;
    }

    public String getContratoPlano() {
        if (this.getPlano() != null && this.getContrato() != null) {
            return this.getContrato() + " - " + this.getPlano();
        } else {
            return "";
        }
    }
}
