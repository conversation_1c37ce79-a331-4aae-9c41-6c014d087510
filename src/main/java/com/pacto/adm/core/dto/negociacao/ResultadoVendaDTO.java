package com.pacto.adm.core.dto.negociacao;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Resultado Venda", description = "Informações de retorno relacionadas à venda realizada.")
public class ResultadoVendaDTO {

    @Schema(description = "Código identificador da venda.", example = "1001")
    private Integer venda;

    @Schema(description = "Link de acesso à venda.", example = "https://pactosolucoes.com.br/venda/1001")
    private String link;

    @Schema(description = "Link direto para envio via WhatsApp.", example = "https://wa.me/5511999999999")
    private String whatsapp;

    public Integer getVenda() {
        return venda;
    }

    public void setVenda(Integer venda) {
        this.venda = venda;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getWhatsapp() {
        return whatsapp;
    }

    public void setWhatsapp(String whatsapp) {
        this.whatsapp = whatsapp;
    }
}
