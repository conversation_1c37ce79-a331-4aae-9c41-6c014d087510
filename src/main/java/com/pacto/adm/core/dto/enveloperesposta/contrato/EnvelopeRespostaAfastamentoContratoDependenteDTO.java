package com.pacto.adm.core.dto.enveloperesposta.contrato;

import com.pacto.adm.core.dto.AfastamentoContratoDependenteDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaAfastamentoContratoDependenteDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private AfastamentoContratoDependenteDTO content;

    public AfastamentoContratoDependenteDTO getContent() {
        return content;
    }

    public void setContent(AfastamentoContratoDependenteDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 150, "
                    + "\"inicioAfastamento\": \"2023-05-01T00:00:00Z\", "
                    + "\"finalAfastamento\": \"2023-06-01T00:00:00Z\", "
                    + "\"dataRegistro\": \"2023-04-25T00:00:00Z\", "
                    + "\"nrDiasSomar\": 30, "
                    + "\"observacao\": \"Afastamento por motivo de saúde.\", "
                    + "\"tipoAfastamento\": \"Saúde\", "
                    + "\"contratoDependente\": {" + EnvelopeRespostaContratoDependenteDTO.atributos + "}, "
                    + "\"justificativaOperacao\": {" + EnvelopeRespostaJustificativaOperacaoDTO.atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
}
