package com.pacto.adm.core.dto.enveloperesposta.fornecedor;

import com.pacto.adm.core.dto.FornecedorDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListFornecedorDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<FornecedorDTO> content;

    public List<FornecedorDTO> getContent() {
        return content;
    }

    public void setContent(List<FornecedorDTO> content) {
        this.content = content;
    }

    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaFornecedorDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

}
