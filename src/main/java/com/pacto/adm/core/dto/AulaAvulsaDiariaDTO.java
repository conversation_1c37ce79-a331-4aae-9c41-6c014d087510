package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Aula Avulsa Diaria", description = "Informações das vendas de produtos diaria")
public class AulaAvulsaDiariaDTO {

    @Schema(description = "Código único identificador da aula avulsa diaria", example = "1113")
    private Integer codigo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
}
