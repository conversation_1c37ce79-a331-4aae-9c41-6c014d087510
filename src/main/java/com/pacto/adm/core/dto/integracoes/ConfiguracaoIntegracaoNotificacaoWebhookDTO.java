package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração de Integração Notificação Webhook", description = "Configurações para integração de notificações via Webhook.")
public class ConfiguracaoIntegracaoNotificacaoWebhookDTO {

    @Schema(description = "Indica se o Webhook deve ser notificado.", example = "true")
    private boolean notificarWebhook;

    @Schema(description = "URL para onde o Webhook deve enviar as notificações.", example = "https://api.exemplo.com/webhook")
    private String urlWebhookNotificar;

    @Schema(description = "Detalhes da empresa associada à configuração de notificação Webhook.")
    private EmpresaDTO empresa;

    public boolean isNotificarWebhook() {
        return notificarWebhook;
    }

    public void setNotificarWebhook(boolean notificarWebhook) {
        this.notificarWebhook = notificarWebhook;
    }

    public String getUrlWebhookNotificar() {
        return urlWebhookNotificar;
    }

    public void setUrlWebhookNotificar(String urlWebhookNotificar) {
        this.urlWebhookNotificar = urlWebhookNotificar;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
