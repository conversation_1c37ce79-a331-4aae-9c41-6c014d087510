package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(name = "Aluguel de Armário", description = "Informações do aluguel de um armário")
public class AluguelArmarioDTO {

    @Schema(description = "Código único identificador do aluguel", example = "109")
    private Integer codigo;

    @Schema(description = "Data que foi realizado o cadastro do aluguel", example = "2025-04-08T14:30:00Z")
    private Date dataCadastro;

    @Schema(description = "Valor do aluguel", example = "4.90")
    private Double valor;

    @Schema(description = "Data final original que o aluguel acaba", example = "2025-04-09T14:30:00Z")
    private Date fimOriginal;

    @Schema(description = "Data da renovação automática do aluguel", example = "2025-04-09T14:31:00Z")
    private Date dataRenovacaoAutomatica;

    @Schema(description = "Indica se o contrato do aluguel foi assinado", example = "true")
    private Boolean contratoAssinado;

    @Schema(description = "Indica se deve ocorrer a renovação do aluguel de forma automática", example = "true")
    private Boolean renovarAutomatico;

    @Schema(description = "Data de início do aluguel", example = "2025-04-08T14:30:00Z")
    private Date dataInicio;

    @Schema(description = "Indica se a chave do armário já foi devolvida", example = "false")
    private Boolean chaveDevolvida;

    @Schema(description = "Armário que foi alugado")
    private ArmarioDTO armario;

    @Schema(description = "Cliente que realizou o aluguel do armário")
    private ClienteDTO cliente;

    @Schema(description = "Responsável pelo aluguel do armário")
    private UsuarioDTO responsavelCadastro;

    @Schema(description = "Movimentação de produto relacionado ao aluguel do armário")
    private MovProdutoDTO movProduto;

    @Schema(description = "Venda avulsa relacionada ao aluguel do armário")
    private VendaAvulsaDTO vendaAvulsa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Date getDataRenovacaoAutomatica() {
        return dataRenovacaoAutomatica;
    }

    public void setDataRenovacaoAutomatica(Date dataRenovacaoAutomatica) {
        this.dataRenovacaoAutomatica = dataRenovacaoAutomatica;
    }

    public Boolean getContratoAssinado() {
        return contratoAssinado;
    }

    public void setContratoAssinado(Boolean contratoAssinado) {
        this.contratoAssinado = contratoAssinado;
    }

    public Boolean getRenovarAutomatico() {
        return renovarAutomatico;
    }

    public void setRenovarAutomatico(Boolean renovarAutomatico) {
        this.renovarAutomatico = renovarAutomatico;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Boolean getChaveDevolvida() {
        return chaveDevolvida;
    }

    public void setChaveDevolvida(Boolean chaveDevolvida) {
        this.chaveDevolvida = chaveDevolvida;
    }

    public ArmarioDTO getArmario() {
        return armario;
    }

    public void setArmario(ArmarioDTO armario) {
        this.armario = armario;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public UsuarioDTO getResponsavelCadastro() {
        return responsavelCadastro;
    }

    public void setResponsavelCadastro(UsuarioDTO responsavelCadastro) {
        this.responsavelCadastro = responsavelCadastro;
    }

    public MovProdutoDTO getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProdutoDTO movProduto) {
        this.movProduto = movProduto;
    }

    public VendaAvulsaDTO getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(VendaAvulsaDTO vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public Date getFimOriginal() {
        return fimOriginal;
    }

    public void setFimOriginal(Date fimOriginal) {
        this.fimOriginal = fimOriginal;
    }
}
