package com.pacto.adm.core.dto.integracaomanychat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(name = "Tag", description = "Informações da tag utilizada em integrações ou classificações.")
public class TagDTO {

    @Schema(description = "Código identificador da tag.", example = "101")
    private Integer id;

    @Schema(description = "Nome da tag.", example = "Campanha Verão 2025")
    private String name;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
