package com.pacto.adm.core.dto.enveloperesposta.acesso.perfilacesso;

import com.pacto.adm.core.dto.enveloperesposta.acesso.permissao.EnvelopeRespostaPermissao;
import com.pacto.adm.core.dto.swagger.RepresentacaoPaginadorDTO;
import com.pacto.adm.core.entities.PerfilAcesso;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Respostas Lista de Perfis de Acesso", description = "Representação das respostas contendo uma lista de perfis de acesso ao sistema")
public class EnvelopeRespostaPerfilAcesso {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private PerfilAcesso content;

    public PerfilAcesso getContent() {
        return content;
    }

    public void setContent(PerfilAcesso content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"nome\": \"PROFESSOR\", "
                    + "\"tipo\": \"1\", "
                    + "\"permissaoVOs\": [{" + EnvelopeRespostaPermissao.atributos + "}]";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";


}
