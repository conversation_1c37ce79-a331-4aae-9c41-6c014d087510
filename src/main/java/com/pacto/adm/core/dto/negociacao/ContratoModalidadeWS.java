package com.pacto.adm.core.dto.negociacao;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Contrato Modalidade", description = "Informações sobre a modalidade incluída em um contrato.")
public class ContratoModalidadeWS {

    @Schema(description = "Código identificador da modalidade no contrato.", example = "5")
    private int codigo;

    @Schema(description = "Nome ou descrição da modalidade.", example = "Musculação")
    private String modalidade;

    @Schema(description = "Número de vezes por semana que a modalidade será praticada.", example = "3")
    private int nrVezesSemana;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public int getNrVezesSemana() {
        return nrVezesSemana;
    }

    public void setNrVezesSemana(int nrVezesSemana) {
        this.nrVezesSemana = nrVezesSemana;
    }
}