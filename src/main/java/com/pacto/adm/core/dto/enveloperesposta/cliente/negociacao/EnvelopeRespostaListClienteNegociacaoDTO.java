package com.pacto.adm.core.dto.enveloperesposta.cliente.negociacao;

import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.negociacao.ClienteNegociacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListClienteNegociacaoDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ClienteNegociacaoDTO> content;

    public List<ClienteNegociacaoDTO> getContent() {
        return content;
    }

    public void setContent(List<ClienteNegociacaoDTO> content) {
        this.content = content;
    }


    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaClienteNegociacaoDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";


}
