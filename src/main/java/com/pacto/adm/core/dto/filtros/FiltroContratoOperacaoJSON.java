package com.pacto.adm.core.dto.filtros;

import org.json.JSONObject;

public class FiltroContratoOperacaoJSON {
    private String parametro;
    private String tipoOperacao;
    private Integer matricula;
    private boolean validarOperacoesContrato = false;

    public FiltroContratoOperacaoJSON(JSONObject filters) {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue").toUpperCase();
            this.tipoOperacao = filters.optString("tipoOperacao").toUpperCase();
            this.matricula = filters.optInt("matricula");
            this.validarOperacoesContrato = filters.optBoolean("validarOperacoesContrato");
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public boolean isValidarOperacoesContrato() {
        return validarOperacoesContrato;
    }

    public void setValidarOperacoesContrato(boolean validarOperacoesContrato) {
        this.validarOperacoesContrato = validarOperacoesContrato;
    }
}
