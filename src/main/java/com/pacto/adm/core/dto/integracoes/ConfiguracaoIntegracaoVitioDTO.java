package com.pacto.adm.core.dto.integracoes;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Configuração Integração Vitio", description = "Configurações para integração com o sistema Vitio.")
public class ConfiguracaoIntegracaoVitioDTO {

    @Schema(description = "Indica se a integração com o sistema Vitio está habilitada.", example = "true")
    private boolean usaVitio;

    @Schema(description = "Link do checkout da plataforma Vitio.", example = "https://checkout.vitio.com")
    private String linkCheckoutVitio;

    @Schema(description = "Link para o ebook associado ao sistema Vitio.", example = "https://ebook.vitio.com")
    private String linkEbook;

    @Schema(description = "Mensagem personalizada que será exibida para o cliente ao realizar a compra no Vitio.", example = "Você quer comprar seu plano no Vitio?")
    private String mensagemVitioQuerComprar;

    @Schema(description = "Mensagem personalizada para ser enviada via WhatsApp relacionado à compra no Vitio.", example = "Clique aqui para finalizar sua compra no Vitio!")
    private String mensagemVitioWpp;

    @Schema(description = "Dados da empresa vinculada à configuração.")
    private EmpresaDTO empresa;

    public boolean isUsaVitio() {
        return usaVitio;
    }

    public void setUsaVitio(boolean usaVitio) {
        this.usaVitio = usaVitio;
    }

    public String getLinkCheckoutVitio() {
        return linkCheckoutVitio;
    }

    public void setLinkCheckoutVitio(String linkCheckoutVitio) {
        this.linkCheckoutVitio = linkCheckoutVitio;
    }

    public String getLinkEbook() {
        return linkEbook;
    }

    public void setLinkEbook(String linkEbook) {
        this.linkEbook = linkEbook;
    }

    public String getMensagemVitioQuerComprar() {
        return mensagemVitioQuerComprar;
    }

    public void setMensagemVitioQuerComprar(String mensagemVitioQuerComprar) {
        this.mensagemVitioQuerComprar = mensagemVitioQuerComprar;
    }

    public String getMensagemVitioWpp() {
        return mensagemVitioWpp;
    }

    public void setMensagemVitioWpp(String mensagemVitioWpp) {
        this.mensagemVitioWpp = mensagemVitioWpp;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
