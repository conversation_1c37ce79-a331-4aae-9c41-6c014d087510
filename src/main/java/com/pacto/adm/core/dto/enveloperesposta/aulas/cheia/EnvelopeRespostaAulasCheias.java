package com.pacto.adm.core.dto.enveloperesposta.aulas.cheia;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.HashMap;
import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaAulasCheias {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas", example = "{\"aulasCheias\": [\"1\", \"2\", \"3\", \"4\"]}")
    private HashMap<String, List<String>> content;

    public HashMap<String, List<String>> getContent() {
        return content;
    }

    public void setContent(HashMap<String, List<String>> content) {
        this.content = content;
    }

    public static final String atributos = "\"aulasCheias\": [\"1\", \"2\", \"3\", \"4\"]";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
