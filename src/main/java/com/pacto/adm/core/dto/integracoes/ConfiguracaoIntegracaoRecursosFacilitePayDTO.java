package com.pacto.adm.core.dto.integracoes;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Configuração de Integração com Recursos FacilitePay", description = "Configurações dos recursos disponíveis na integração com a FacilitePay.")
public class ConfiguracaoIntegracaoRecursosFacilitePayDTO {

    @Schema(description = "Detalhes da empresa associada à configuração.")
    private EmpresaDTO empresa;

    @Schema(description = "Indica se a conciliação de contas a pagar via FacilitePay está habilitada.", example = "true")
    private boolean concContasPagarFacilitePay;

    @Schema(description = "Indica se a conciliação de contas a receber via FacilitePay está habilitada.", example = "true")
    private boolean concContasReceberFacilitePay;

    @Schema(description = "Indica se a conciliação de cartões pela FacilitePay está habilitada.", example = "false")
    private boolean facilitePayConciliacaoCartao;

    @Schema(description = "Indica se a régua de cobrança da FacilitePay está habilitada.", example = "true")
    private boolean facilitePayReguaCobranca;

    @Schema(description = "Indica se a integração com o CDL/SPC pela FacilitePay está habilitada.", example = "false")
    private boolean facilitePayCDLSPC;

    @Schema(description = "Valor de meta financeira definida para a FacilitePay.", example = "5000.00")
    private Double valorMetaFacilitePay;

    @Schema(description = "Indica se a régua de cobrança por e-mail via FacilitePay está habilitada.", example = "true")
    private boolean facilitePayReguaCobrancaEmail;

    @Schema(description = "Indica se a régua de cobrança por SMS via FacilitePay está habilitada.", example = "false")
    private boolean facilitePayReguaCobrancaSms;

    @Schema(description = "Indica se a régua de cobrança pelo aplicativo via FacilitePay está habilitada.", example = "true")
    private boolean facilitePayReguaCobrancaApp;

    @Schema(description = "Indica se a régua de cobrança por WhatsApp via FacilitePay está habilitada.", example = "true")
    private boolean facilitePayReguaCobrancaWhatsApp;

    @Schema(description = "Quantidade limite de contas conciliadas com a FacilitePay.", example = "100")
    private int qtdLmtContasConcFacilitePay;

    @Schema(description = "Indica se a régua de cobrança via Gymbot Pro pela FacilitePay está habilitada.", example = "false")
    private boolean facilitePayReguaCobrancaGymbotPro;

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public boolean isConcContasPagarFacilitePay() {
        return concContasPagarFacilitePay;
    }

    public void setConcContasPagarFacilitePay(boolean concContasPagarFacilitePay) {
        this.concContasPagarFacilitePay = concContasPagarFacilitePay;
    }

    public boolean isConcContasReceberFacilitePay() {
        return concContasReceberFacilitePay;
    }

    public void setConcContasReceberFacilitePay(boolean concContasReceberFacilitePay) {
        this.concContasReceberFacilitePay = concContasReceberFacilitePay;
    }

    public boolean isFacilitePayReguaCobranca() {
        return facilitePayReguaCobranca;
    }

    public void setFacilitePayReguaCobranca(boolean facilitePayReguaCobranca) {
        this.facilitePayReguaCobranca = facilitePayReguaCobranca;
    }

    public boolean isFacilitePayCDLSPC() {
        return facilitePayCDLSPC;
    }

    public void setFacilitePayCDLSPC(boolean facilitePayCDLSPC) {
        this.facilitePayCDLSPC = facilitePayCDLSPC;
    }

    public Double getValorMetaFacilitePay() {
        return valorMetaFacilitePay;
    }

    public void setValorMetaFacilitePay(Double valorMetaFacilitePay) {
        this.valorMetaFacilitePay = valorMetaFacilitePay;
    }

    public boolean isFacilitePayConciliacaoCartao() {
        return facilitePayConciliacaoCartao;
    }

    public void setFacilitePayConciliacaoCartao(boolean facilitePayConciliacaoCartao) {
        this.facilitePayConciliacaoCartao = facilitePayConciliacaoCartao;
    }

    public boolean isFacilitePayReguaCobrancaEmail() {
        return facilitePayReguaCobrancaEmail;
    }

    public void setFacilitePayReguaCobrancaEmail(boolean facilitePayReguaCobrancaEmail) {
        this.facilitePayReguaCobrancaEmail = facilitePayReguaCobrancaEmail;
    }

    public boolean isFacilitePayReguaCobrancaSms() {
        return facilitePayReguaCobrancaSms;
    }

    public void setFacilitePayReguaCobrancaSms(boolean facilitePayReguaCobrancaSms) {
        this.facilitePayReguaCobrancaSms = facilitePayReguaCobrancaSms;
    }

    public boolean isFacilitePayReguaCobrancaApp() {
        return facilitePayReguaCobrancaApp;
    }

    public void setFacilitePayReguaCobrancaApp(boolean facilitePayReguaCobrancaApp) {
        this.facilitePayReguaCobrancaApp = facilitePayReguaCobrancaApp;
    }

    public boolean isFacilitePayReguaCobrancaWhatsApp() {
        return facilitePayReguaCobrancaWhatsApp;
    }

    public void setFacilitePayReguaCobrancaWhatsApp(boolean facilitePayReguaCobrancaWhatsApp) {
        this.facilitePayReguaCobrancaWhatsApp = facilitePayReguaCobrancaWhatsApp;
    }

    public int getQtdLmtContasConcFacilitePay() {
        return qtdLmtContasConcFacilitePay;
    }

    public void setQtdLmtContasConcFacilitePay(int qtdLmtContasConcFacilitePay) {
        this.qtdLmtContasConcFacilitePay = qtdLmtContasConcFacilitePay;
    }

    public boolean isFacilitePayReguaCobrancaGymbotPro() {
        return facilitePayReguaCobrancaGymbotPro;
    }

    public void setFacilitePayReguaCobrancaGymbotPro(boolean facilitePayReguaCobrancaGymbotPro) {
        this.facilitePayReguaCobrancaGymbotPro = facilitePayReguaCobrancaGymbotPro;
    }
}
