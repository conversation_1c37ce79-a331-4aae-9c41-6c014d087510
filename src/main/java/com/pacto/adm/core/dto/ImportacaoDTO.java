package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@JsonInclude
@Schema(description = "Dados de importação")
public class ImportacaoDTO {

    @Schema(description = "Indica se foi importado", example = "true")
    private Boolean importacao;

    @Schema(description = "Indica se o cliente foi verificado", example = "true")
    private Boolean verificarCliente;

    @Schema(description = "Data que foi realizada a verificação", example = "2025-04-28T00:00:00Z")
    private Date verificadoEm;

    @Schema(description = "Código do usuário que foi verificado", example = "5")
    private Integer usuarioVerificacao;

    public ImportacaoDTO() {
    }

    public Boolean getVerificarCliente() {
        return verificarCliente;
    }

    public void setVerificarCliente(Boolean verificarCliente) {
        this.verificarCliente = verificarCliente;
    }

    public Date getVerificadoEm() {
        return verificadoEm;
    }

    public void setVerificadoEm(Date verificadoEm) {
        this.verificadoEm = verificadoEm;
    }

    public Integer getUsuarioVerificacao() {
        return usuarioVerificacao;
    }

    public void setUsuarioVerificacao(Integer usuarioVerificacao) {
        this.usuarioVerificacao = usuarioVerificacao;
    }

    public Boolean getImportacao() {
        return importacao;
    }

    public void setImportacao(Boolean importacao) {
        this.importacao = importacao;
    }

    public boolean isApresentarVerificarCliente() {
        return this.getVerificarCliente() != null && this.getVerificarCliente() && this.getVerificadoEm() == null;
    }
    public boolean isApresentarDesverificarCliente() {
        return this.getVerificarCliente() != null && this.getVerificarCliente() && this.getVerificadoEm() != null;
    }
}
