package com.pacto.adm.core.dto.enveloperesposta.configuracao;

import com.pacto.adm.core.dto.ConfiguracaoSistemaDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Informações Cliente Colaborador", description = "Representação das respostas contendo as informações do cliente visualizadas por um colaborador")
public class EnvelopeRespostaConfiguracaoSistemaDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoSistemaDTO content;

    public ConfiguracaoSistemaDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoSistemaDTO content) {
        this.content = content;
    }

    public static final String resposta = "{\"content\": {\"codigo\":1,\"utilizarServicoSesiSc\":false,\"realizarEnvioSesiSc\":true,\"carenciaRenovacao\":10}}";

}
