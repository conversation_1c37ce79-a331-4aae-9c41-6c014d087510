package com.pacto.adm.core.dto.enveloperesposta.cliente.observacao;

import com.pacto.adm.core.dto.ClienteObservacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

//@Schema(name = "Resposta Informações Cliente Imprimir", description = "Representação das respostas quando a opção imprimir informações está ativa")
public class EnvelopeRespostaListClienteObservacaoDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ClienteObservacaoDTO> content;

    public List<ClienteObservacaoDTO> getContent() {
        return content;
    }

    public void setContent(List<ClienteObservacaoDTO> content) {
        this.content = content;
    }


    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaClienteObservacaoDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

}
