package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Item da Venda Avulsa", description = "Item vinculado a uma venda avulsa")
public class ItemVendaAvulsaDTO {

    @Schema(description = "Código único identificador do item", example = "398")
    private Integer codigoProduto;

    @Schema(description = "Quantidade desse item que foi vendida", example = "2")
    private Integer qtd;

    @Schema(description = "Quantidade de pontos", example = "10")
    private Integer pontos;

    @Schema(description = "Descrição do produto", example = "Creatina MonoHidratada - 250 Gramas - Marca: Pacto")
    private String descricaoProduto;

    @Schema(description = "Preço Unitário do Produto", example = "59.90")
    private Double precoProduto;

    @Schema(description = "Desconto manual aplicado ao produto", example = "9.90")
    private Double descontoManual;

    @Schema(description = "Desconto padrão que é aplicado ao produto", example = "10")
    private Integer descontoPadrao;

    @Schema(description = "Valor parcial do produto", example = "10")
    private Double valorParcial;

    @Schema(description = "Pacote de Personal vinculado ao item vendido")
    private PacotePersonalDTO pacoteEscolhido;

    public Integer getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(Integer codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public Integer getQtd() {
        return qtd;
    }

    public void setQtd(Integer qtd) {
        this.qtd = qtd;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public String getDescricaoProduto() {
        return descricaoProduto;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public Double getPrecoProduto() {
        return precoProduto;
    }

    public void setPrecoProduto(Double precoProduto) {
        this.precoProduto = precoProduto;
    }

    public Double getDescontoManual() {
        return descontoManual;
    }

    public void setDescontoManual(Double descontoManual) {
        this.descontoManual = descontoManual;
    }

    public Double getValorParcial() {
        return valorParcial;
    }

    public void setValorParcial(Double valorParcial) {
        this.valorParcial = valorParcial;
    }

    public Integer getDescontoPadrao() {
        return descontoPadrao;
    }

    public void setDescontoPadrao(Integer descontoPadrao) {
        this.descontoPadrao = descontoPadrao;
    }

    public PacotePersonalDTO getPacoteEscolhido() {
        return pacoteEscolhido;
    }

    public void setPacoteEscolhido(PacotePersonalDTO pacoteEscolhido) {
        this.pacoteEscolhido = pacoteEscolhido;
    }
}
