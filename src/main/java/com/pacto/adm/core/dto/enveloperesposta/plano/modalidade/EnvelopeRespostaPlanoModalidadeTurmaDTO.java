package com.pacto.adm.core.dto.enveloperesposta.plano.modalidade;

import com.pacto.adm.core.dto.negociacao.PlanoModalidadeTurmaDTO;
import com.pacto.adm.core.dto.negociacao.PlanoModalidadeVezesDTO;
import io.swagger.v3.oas.annotations.media.Schema;


public class EnvelopeRespostaPlanoModalidadeTurmaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private PlanoModalidadeTurmaDTO content;

    public PlanoModalidadeTurmaDTO getContent() {
        return content;
    }

    public void setContent(PlanoModalidadeTurmaDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 234";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
