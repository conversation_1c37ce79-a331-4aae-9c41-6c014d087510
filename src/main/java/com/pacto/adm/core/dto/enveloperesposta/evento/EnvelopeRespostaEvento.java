package com.pacto.adm.core.dto.enveloperesposta.evento;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaConfigEstacionamentoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaParceiroFidelidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaCidade;
import com.pacto.adm.core.dto.enveloperesposta.endereco.EnvelopeRespostaEstado;
import com.pacto.adm.core.entities.Evento;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaEvento {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private Evento content;

    public Evento getContent() {
        return content;
    }

    public void setContent(Evento content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 1234, "
                    + "\"descricao\": \"Aula de Spinning\", "
                    + "\"status\": \"Ativo\", "
                    + "\"vigenciaInicial\": \"2025-06-01T08:00:00Z\", "
                    + "\"vigenciaFinal\": \"2025-06-01T09:00:00Z\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";
}
