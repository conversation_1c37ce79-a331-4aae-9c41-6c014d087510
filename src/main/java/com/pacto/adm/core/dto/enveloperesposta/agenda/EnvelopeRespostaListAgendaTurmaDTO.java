package com.pacto.adm.core.dto.enveloperesposta.agenda;

import com.pacto.adm.core.dto.negociacao.AgendaTurmaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaListAgendaTurmaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<AgendaTurmaDTO> content;

    public List<AgendaTurmaDTO>  getContent() {
        return content;
    }

    public void setContent(List<AgendaTurmaDTO>  content) {
        this.content = content;
    }


    public final static String resposta = "{"
            + "  \"content\": [{" + EnvelopeRespostaAgendaTurmaDTO.atributos + " }],"
            + "  \"totalElements\": 1,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";

}
