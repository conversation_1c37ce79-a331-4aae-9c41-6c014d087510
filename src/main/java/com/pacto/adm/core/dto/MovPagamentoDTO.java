package com.pacto.adm.core.dto;

import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.enumerador.StatusPagamentoConciliadoraEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;

@Schema(description = "Informações do Pagamento")
public class MovPagamentoDTO {

    @Schema(description = "Código único identificador do movimento de pagamento", example = "1")
    private Integer codigo;

    @Schema(description = "Código do recibo de pagamento associado", example = "10")
    private Integer reciboPagamento;

    @Schema(description = "Detalhes da empresa associada ao pagamento")
    private EmpresaDTO empresa;

    @Schema(description = "Nome da pessoa responsável pelo pagamento", example = "João Silva")
    private String nomePessoaPagador;

    @Schema(description = "Data de lançamento do pagamento", example = "2023-10-15")
    private Date dataLancamento;

    @Schema(description = "Valor total do pagamento", example = "299.99")
    private BigDecimal valorTotal;

    @Schema(description = "Descrição da forma de pagamento utilizada", example = "Cartão de Crédito")
    private String descricaoFormaPagamento;

    @Schema(description = "Detalhes do usuário que realizou o lançamento do pagamento")
    private UsuarioDTO usuario;
    private Date dataPagamento;
    private Date dataAlteracaoManual;
    private Date dataQuitacao;
    private UsuarioDTO responsavelPagamento;

    @Schema(description = "Detalhes da forma de pagamento utilizada")
    private FormaPagamentoDTO formaPagamento;

    @Schema(description = "Status da conciliadora (código)", example = "1")
    private Integer statusConciliadora;

    @Schema(description = "Indica se o pagamento foi realizado como crédito", example = "true")
    private Boolean credito;

    @Schema(description = "Valor do pagamento", example = "299.99")
    private BigDecimal valor;
    private ClienteDTO cliente;

    public MovPagamentoDTO() {
    }

    public MovPagamentoDTO(ClienteDTO cliente, BigDecimal valor, FormaPagamentoDTO formaPagamento, UsuarioDTO responsavelPagamento, Date dataQuitacao, Date dataAlteracaoManual, Date dataPagamento, Date dataLancamento) {
        this.cliente = cliente;
        this.valor = valor;
        this.formaPagamento = formaPagamento;
        this.responsavelPagamento = responsavelPagamento;
        this.dataQuitacao = dataQuitacao;
        this.dataAlteracaoManual = dataAlteracaoManual;
        this.dataPagamento = dataPagamento;
        this.dataLancamento = dataLancamento;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(Integer reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public String getNomePessoaPagador() {
        return nomePessoaPagador;
    }

    public void setNomePessoaPagador(String nomePessoaPagador) {
        this.nomePessoaPagador = nomePessoaPagador;
    }

    public FormaPagamentoDTO getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamentoDTO formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public BigDecimal getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(BigDecimal valorTotal) {
        this.valorTotal = valorTotal;
    }

    public void setDescricaoFormaPagamento(String descricaoFormaPagamento) {
        this.descricaoFormaPagamento = descricaoFormaPagamento;
    }

    public String getDescricaoFormaPagamento() {
        return descricaoFormaPagamento;
    }

    public UsuarioDTO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioDTO usuario) {
        this.usuario = usuario;
    }

    public Boolean isCredito() {
        return credito;
    }

    public void setCredito(Boolean credito) {
        this.credito = credito;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
    }

    public Integer getStatusConciliadora() {
        return statusConciliadora;
    }

    public void setStatusConciliadora(Integer statusConciliadora) {
        this.statusConciliadora = statusConciliadora;
    }

    public Boolean getCredito() {
        return credito;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public Date getDataAlteracaoManual() {
        return dataAlteracaoManual;
    }

    public void setDataAlteracaoManual(Date dataAlteracaoManual) {
        this.dataAlteracaoManual = dataAlteracaoManual;
    }

    public Date getDataQuitacao() {
        return dataQuitacao;
    }

    public void setDataQuitacao(Date dataQuitacao) {
        this.dataQuitacao = dataQuitacao;
    }

    public UsuarioDTO getResponsavelPagamento() {
        return responsavelPagamento;
    }

    public void setResponsavelPagamento(UsuarioDTO responsavelPagamento) {
        this.responsavelPagamento = responsavelPagamento;
    }
}
