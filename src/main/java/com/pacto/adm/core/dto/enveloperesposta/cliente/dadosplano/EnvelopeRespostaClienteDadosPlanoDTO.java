package com.pacto.adm.core.dto.enveloperesposta.cliente.dadosplano;

import com.pacto.adm.core.dto.ClienteDadosPlanoDTO;
import io.swagger.v3.oas.annotations.media.Schema;


//@Schema(name = "Resposta Dados do Plano Cliente", description = "Representação das respostas das requisições que devolvem dados de um plano de um cliente")
public class EnvelopeRespostaClienteDadosPlanoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ClienteDadosPlanoDTO content;

    public ClienteDadosPlanoDTO getContent() {
        return content;
    }

    public void setContent(ClienteDadosPlanoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"situacao\": \"ATIVO\", "
                    + "\"nomePlano\": \"Mensalidade de Academia\", "
                    + "\"avisos\": 1, "
                    + "\"observacoes\": 58, "
                    + "\"dataCadastro\": 1745808000000, "
                    + "\"dataMatricula\": 1745808000000, "
                    + "\"dataRematricula\": 1745808000000, "
                    + "\"inicioVinculoAtual\": \"2025-05-07T00:00:00Z\", "
                    + "\"vinculos\": [ {" + EnvelopeRespostaVinculoDTO.atributos + "} ], "
                    + "\"gympass\": false, "
                    + "\"totalpass\": false, "
                    + "\"titularContratoNome\": \"Renato Alves Cariri\", "
                    + "\"titularContratoMatricula\": \"554\", "
                    + "\"valorReceitaCliente\": 1200.00, "
                    + "\"valorReceitaMediaCliente\": 1200.00, "
                    + "\"atualizarBv\": false";

    public final static String resposta = "{\"content\": {" + atributos + "}}";


}
