package com.pacto.adm.core.dto.integracoes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Configuração de Integração com Envio de Acesso Pratique", description = "Configurações para integração com o sistema de envio de acesso da Pratique.")
public class ConfiguracaoIntegracaoEnvioAcessoPratiqueDTO {

    @Schema(description = "Código identificador da configuração da integração.", example = "1")
    private Integer codigo;

    @Schema(description = "Indica se a integração com o envio de acesso da Pratique está habilitada.", example = "true")
    private Boolean habilitada;

    @Schema(description = "Detalhes da empresa associada à configuração da integração.")
    private EmpresaDTO empresa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getHabilitada() {
        if (habilitada == null) {
            habilitada = false;
        }
        return habilitada;
    }

    public void setHabilitada(Boolean habilitada) {
        this.habilitada = habilitada;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }
}
