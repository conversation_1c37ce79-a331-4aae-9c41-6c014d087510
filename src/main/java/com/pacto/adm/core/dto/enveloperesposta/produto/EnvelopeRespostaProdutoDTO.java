package com.pacto.adm.core.dto.enveloperesposta.produto;

import com.pacto.adm.core.dto.ProdutoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaProdutoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ProdutoDTO content;

    public ProdutoDTO getContent() {
        return content;
    }

    public void setContent(ProdutoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"descricao\": \"Plano Mensal Básico\", "
                    + "\"valorFinal\": 99.99, "
                    + "\"desativado\": false, "
                    + "\"tipoProduto\": \"PM\", "
                    + "\"tipoProdutoDescricao\": \"Mês de Referência Plano\", "
                    + "\"prevalecerVigenciaContrato\": true, "
                    + "\"bloqueiaPelaVigencia\": false, "
                    + "\"nrDiasVigencia\": 30, "
                    + "\"pontos\": 100, "
                    + "\"tipoVigencia\": \"Mensal\", "
                    + "\"renovavelAutomaticamente\": true, "
                    + "\"contratoTextoPadrao\": 1";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";
}
