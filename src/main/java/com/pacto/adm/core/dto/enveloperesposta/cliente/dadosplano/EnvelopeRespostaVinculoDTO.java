package com.pacto.adm.core.dto.enveloperesposta.cliente.dadosplano;

import com.pacto.adm.core.dto.VinculoDTO;
import io.swagger.v3.oas.annotations.media.Schema;


//@Schema(name = "Resposta Dados do Plano Cliente", description = "Representação das respostas das requisições que devolvem dados de um plano de um cliente")
public class EnvelopeRespostaVinculoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private VinculoDTO content;

    public VinculoDTO getContent() {
        return content;
    }

    public void setContent(VinculoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 65, "
                    + "\"colaborador\": \"Amanda Silva Reis\", "
                    + "\"tipoVinculo\": \"CO\", "
                    + "\"codigoColaborador\": 82";

    public final static String resposta = "{\"content\": {" + atributos + "}}";


}
