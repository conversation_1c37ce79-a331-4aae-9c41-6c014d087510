package com.pacto.adm.core.dto.swagger;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Elementos de Paginação", description = "Representação do Paginador")

public class RepresentacaoPaginadorDTO {


    @Schema(description = "Quantidade de resultados por página", example = "10", type = "integer", format = "int64")
    private Long size;

    @Schema(description = "Número da página da requisição",example = "0", type = "integer", format = "int64")
    private Long page;

    @Schema(description = "Orderna as respostas por um atributo contido na resposta", type = "string", example="atributo1,ASC;atributo2,desc")
    private String sort;

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Long getPage() {
        return page;
    }

    public void setPage(Long page) {
        this.page = page;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public RepresentacaoPaginadorDTO() {
    }

    public RepresentacaoPaginadorDTO(Long size, Long page, String sort) {
        this.size = size;
        this.page = page;
        this.sort = sort;
    }
}
