package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.empresa.EmpresaLocalDeAcessoDTO;
import com.pacto.adm.core.entities.Cliente;
import com.pacto.adm.core.entities.Pessoa;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor

@Schema(description = "Local que foi realizado o acesso", name = "Local de Acesso")
public class LocalDeAcessoDTO {

    @Schema(description = "Código identificador do local de acesso", example = "2")
    private Integer codigo;

    @Schema(description = "Descrição do local de acesso", example = "ACADEMIA PACTO - RECEPÇÃO")
    private String descricao;

    @Schema(description = "Coletor que gerou o acesso")
    private ColetorDTO listaColetor;
}
