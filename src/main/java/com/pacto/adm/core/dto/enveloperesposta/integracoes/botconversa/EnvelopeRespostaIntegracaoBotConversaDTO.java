package com.pacto.adm.core.dto.enveloperesposta.integracoes.botconversa;

import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoBotConversaDTO;
import com.pacto.adm.core.dto.integracoes.IntegracaoBotConversaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaIntegracaoBotConversaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private IntegracaoBotConversaDTO content;

    public IntegracaoBotConversaDTO getContent() {
        return content;
    }

    public void setContent(IntegracaoBotConversaDTO content) {
        this.content = content;
    }
    public static final String atributos =
            "\"numero\": \"123456\", "
                    + "\"nome\": \"Bot Conversa\", "
                    + "\"idEmpresa\": \"1\", "
                    + "\"idCliente\": \"123\", "
                    + "\"urlWebhookGenerico\": \"https://www.exemplo.com/webhook-bot\", "
                    + "\"nomeUsuario\": \"bot.academia\", "
                    + "\"idUsuario\": \"1\"";

    public static final String resposta = "{\"content\": {" + atributos + "}}";
    public static final String requestBody = "{" + atributos + "}";


}
