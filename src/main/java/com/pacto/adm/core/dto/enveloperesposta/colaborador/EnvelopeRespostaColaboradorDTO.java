package com.pacto.adm.core.dto.enveloperesposta.colaborador;

import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.pessoa.EnvelopeRespostaPessoaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Informações Cliente Colaborador", description = "Representação das respostas contendo as informações do cliente visualizadas por um colaborador")
public class EnvelopeRespostaColaboradorDTO extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ColaboradorDTO content;

    public ColaboradorDTO getContent() {
        return content;
    }

    public void setContent(ColaboradorDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"situacao\": \"ATIVO\", "
                    + "\"pessoa\": { " + EnvelopeRespostaPessoaDTO.atributos + " }, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + " }, "
                    + "\"tiposColaborador\": [{" + EnvelopeRespostaTipoColaboradorString.atributos +"}]";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";
}
