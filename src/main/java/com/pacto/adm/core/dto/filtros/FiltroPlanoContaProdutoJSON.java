package com.pacto.adm.core.dto.filtros;

import org.json.JSONObject;

public class FiltroPlanoContaProdutoJSON {
    private String parametro;
    private Boolean somenteServicos;

    public FiltroPlanoContaProdutoJSON(String filters) {
        if (filters != null) {
            JSONObject filtersJson = new JSONObject(filters);
            this.parametro = filtersJson.optString("quicksearchValue");
            this.somenteServicos = filtersJson.optBoolean("somenteServicos");
        }
    }

    public String getParametro() {
        return parametro;
    }

    public Boolean getSomenteServicos() {
        if (somenteServicos == null) {
            somenteServicos = false;
        }
        return somenteServicos;
    }
}
