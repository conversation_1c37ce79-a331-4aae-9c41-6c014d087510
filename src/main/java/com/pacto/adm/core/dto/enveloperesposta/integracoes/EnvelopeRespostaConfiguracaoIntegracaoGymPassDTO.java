package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGymPassDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoGymPassDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoGymPassDTO content;

    public ConfiguracaoIntegracaoGymPassDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoGymPassDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigoGympass\": \"codigo-gympass-123\", "
                    + "\"tokenApiGympass\": \"token-api-gympass-xyz\", "
                    + "\"limiteDeAcessosPorDia\": 5, "
                    + "\"limiteDeAulasPorDia\": 3, "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public static final String atributosSemEmpresa =
            "\"codigoGympass\": \"codigo-gympass-123\", "
                    + "\"tokenApiGympass\": \"token-api-gympass-xyz\", "
                    + "\"limiteDeAcessosPorDia\": 5, "
                    + "\"limiteDeAulasPorDia\": 3, "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
