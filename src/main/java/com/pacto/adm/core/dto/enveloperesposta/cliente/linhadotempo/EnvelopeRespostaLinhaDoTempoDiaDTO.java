package com.pacto.adm.core.dto.enveloperesposta.cliente.linhadotempo;

import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.linhaTempo.LinhaDoTempoDiaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaLinhaDoTempoDiaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private LinhaDoTempoDiaDTO content;

    public LinhaDoTempoDiaDTO getContent() {
        return content;
    }

    public void setContent(LinhaDoTempoDiaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"data\": \"2025-08-30T00:00:00.000\", "
                    + "\"itens\": [{" + EnvelopeRespostaLinhaDoTempoDTO.atributos + "}]";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";




}
