package com.pacto.adm.core.dto.enveloperesposta.cliente.restricao;

import com.pacto.adm.core.dto.ClienteRestricaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Informações Cliente Colaborador", description = "Representação das respostas contendo as informações do cliente visualizadas por um colaborador")
public class EnvelopeRespostaClienteRestricaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ClienteRestricaoDTO content;

    public ClienteRestricaoDTO getContent() {
        return content;
    }

    public void setContent(ClienteRestricaoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 394, "
                    + "\"nome\": \"Augusto <PERSON>\", "
                    + "\"codigoMatricula\": 543209, "
                    + "\"cpf\": \"455.123.234-10\", "
                    + "\"observacao\": \"Inadimplência, cliente não está mais perdido acessar a academia até pagar todos os débitos\", "
                    + "\"codigoEmpresa\": 1, "
                    + "\"nomeEmpresa\": \"ACADEMIA PACTO\", "
                    + "\"chaveEmpresa\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\", "
                    + "\"tipo\": \"IN\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";




}
