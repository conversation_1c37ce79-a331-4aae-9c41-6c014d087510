package com.pacto.adm.core.dto.negociacao;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Negociação", description = "Dados detalhados da negociação de plano e condições.")
public class NegociacaoDTO {

    @Schema(description = "Código do plano selecionado para a negociação.", example = "7")
    private Integer plano;

    @Schema(description = "Indica se o cliente possui bolsa.", example = "false")
    private Boolean bolsa = false;

    @Schema(description = "Indica se a edição de turma está habilitada na negociação.", example = "true")
    private Boolean exibirEditarTurma = true;

    @Schema(description = "Indica se o valor da modalidade deve ser exibido.", example = "true")
    private Boolean exibirValorModalidade = true;

    @Schema(description = "Indica se a negociação segue modelo de recorrência.", example = "true")
    private Boolean recorrencia = true;

    @Schema(description = "Indica se deve ser aplicado arredondamento nos valores.", example = "true")
    private Boolean usaArredondamento = true;

    @Schema(description = "Permite fechar negociação sem autorização do DCC.", example = "true")
    private Boolean fecharNegociacaoSemAutorizacaoDCC = true;

    @Schema(description = "Indica se a venda de crédito é apenas para treino.", example = "true")
    private Boolean vendaCreditoTreino = true;

    @Schema(description = "Indica se o crédito é do tipo sessão.", example = "false")
    private Boolean creditoSessao = false;

    @Schema(description = "Indica se o crédito de treino não pode ser acumulado.", example = "false")
    private Boolean creditoTreinoNaoCumulativo = false;

    @Schema(description = "Lista de durações disponíveis para o plano.")
    private List<PlanoDuracaoDTO> duracoes;

    @Schema(description = "Lista de horários disponíveis para o plano.")
    private List<PlanoHorarioDTO> horarios;

    @Schema(description = "Lista de modalidades disponíveis para o plano.")
    private List<PlanoModalidadeDTO> modalidades;

    @Schema(description = "Lista de produtos associados ao plano.")
    private List<PlanoProdutoDTO> produtos;

    @Schema(description = "Lista de pacotes associados à negociação.")
    private List<PacoteDTO> pacotes;

    @Schema(description = "Lista de convênios aplicáveis à negociação.")
    private List<ConvenioDTO> convenios;

    @Schema(description = "Lista de descontos disponíveis na negociação.")
    private List<DescontoDTO> descontos;

    @Schema(description = "Indica se a rematrícula será isenta de cobrança.", example = "false")
    private Boolean isentarRematricula = false;

    @Schema(description = "Quantidade de dias para iniciar a cobrança de rematrícula.", example = "30")
    private Integer qtdDiasCobrarRematricula;

    @Schema(description = "Duração sugerida para o plano em meses.", example = "12")
    private Integer duracaoSugerida;

    @Schema(description = "Código do horário sugerido para o plano.", example = "4")
    private Integer horarioSugerido;

    @Schema(description = "Lista de códigos de modalidades sugeridas.", example = "[1, 3, 5]")
    private List<Integer> modalidadesSugeridas;

    @Schema(description = "Lista de códigos de pacotes sugeridos.", example = "[10, 12]")
    private List<Integer> pacotesSugeridos;

    @Schema(description = "Lista de dias de cobrança no cartão.")
    private List<DiaCartaoDTO> diasCartao;

    @Schema(description = "Lista de dias para cobrança de valores proporcionais (pro rata).")
    private List<DiaCartaoDTO> diasProRata;

    @Schema(description = "Código da condição de pagamento sugerida.", example = "2")
    private Integer condicaoSugerida;

    @Schema(description = "Dia sugerido para vencimento ou início da cobrança.", example = "5")
    private Integer diaSugerido;

    @Schema(description = "Número de vezes que a adesão pode ser parcelada.", example = "3")
    private Integer nrVezesParcelarAdesao;

    @Schema(description = "Indica se a adesão deve ser cobrada separadamente.", example = "false")
    private Boolean cobrarAdesaoSeparada = false;

    @Schema(description = "Indica se o valor proporcional (pro rata) é obrigatório.", example = "false")
    private Boolean proRataObrigatorio = false;

    @Schema(description = "Indica se os produtos devem ser cobrados separadamente.", example = "false")
    private Boolean cobrarProdutoSeparado = false;

    @Schema(description = "Número de parcelas para cobrança dos produtos.", example = "2")
    private Integer nrVezesparcelarProduto;

    @Schema(description = "Código do contrato anterior que serve de base para esta negociação.", example = "150")
    private Integer contratoBaseado;

    @Schema(description = "Indica se as parcelas podem ser geradas com valores diferentes.", example = "true")
    private Boolean deveGerarParcelasComValorDiferente;

    public Boolean getCreditoTreinoNaoCumulativo() {
        return creditoTreinoNaoCumulativo;
    }

    public void setCreditoTreinoNaoCumulativo(Boolean creditoTreinoNaoCumulativo) {
        this.creditoTreinoNaoCumulativo = creditoTreinoNaoCumulativo;
    }

    public Boolean getCreditoSessao() {
        return creditoSessao;
    }

    public void setCreditoSessao(Boolean creditoSessao) {
        this.creditoSessao = creditoSessao;
    }

    public Boolean getRecorrencia() {
        return recorrencia;
    }

    public void setRecorrencia(Boolean recorrencia) {
        this.recorrencia = recorrencia;
    }

    public Boolean getVendaCreditoTreino() {
        return vendaCreditoTreino;
    }

    public void setVendaCreditoTreino(Boolean vendaCreditoTreino) {
        this.vendaCreditoTreino = vendaCreditoTreino;
    }

    public List<DiaCartaoDTO> getDiasProRata() {
        return diasProRata;
    }

    public void setDiasProRata(List<DiaCartaoDTO> diasProRata) {
        this.diasProRata = diasProRata;
    }

    public Integer getNrVezesParcelarAdesao() {
        return nrVezesParcelarAdesao;
    }

    public void setNrVezesParcelarAdesao(Integer nrVezesParcelarAdesao) {
        this.nrVezesParcelarAdesao = nrVezesParcelarAdesao;
    }

    public Boolean getCobrarAdesaoSeparada() {
        return cobrarAdesaoSeparada;
    }

    public void setCobrarAdesaoSeparada(Boolean cobrarAdesaoSeparada) {
        this.cobrarAdesaoSeparada = cobrarAdesaoSeparada;
    }

    public Boolean getProRataObrigatorio() {
        return proRataObrigatorio;
    }

    public void setProRataObrigatorio(Boolean proRataObrigatorio) {
        this.proRataObrigatorio = proRataObrigatorio;
    }

    public Boolean getCobrarProdutoSeparado() {
        return cobrarProdutoSeparado;
    }

    public void setCobrarProdutoSeparado(Boolean cobrarProdutoSeparado) {
        this.cobrarProdutoSeparado = cobrarProdutoSeparado;
    }

    public Integer getNrVezesparcelarProduto() {
        return nrVezesparcelarProduto;
    }

    public void setNrVezesparcelarProduto(Integer nrVezesparcelarProduto) {
        this.nrVezesparcelarProduto = nrVezesparcelarProduto;
    }

    public Integer getDiaSugerido() {
        return diaSugerido;
    }

    public void setDiaSugerido(Integer diaSugerido) {
        this.diaSugerido = diaSugerido;
    }

    public List<DiaCartaoDTO> getDiasCartao() {
        return diasCartao;
    }

    public void setDiasCartao(List<DiaCartaoDTO> diasCartao) {
        this.diasCartao = diasCartao;
    }

    public Integer getDuracaoSugerida() {
        return duracaoSugerida;
    }

    public void setDuracaoSugerida(Integer duracaoSugerida) {
        this.duracaoSugerida = duracaoSugerida;
    }

    public Integer getHorarioSugerido() {
        return horarioSugerido;
    }

    public void setHorarioSugerido(Integer horarioSugerido) {
        this.horarioSugerido = horarioSugerido;
    }

    public List<Integer> getModalidadesSugeridas() {
        return modalidadesSugeridas;
    }

    public void setModalidadesSugeridas(List<Integer> modalidadesSugeridas) {
        this.modalidadesSugeridas = modalidadesSugeridas;
    }

    public Integer getCondicaoSugerida() {
        return condicaoSugerida;
    }

    public void setCondicaoSugerida(Integer condicaoSugerida) {
        this.condicaoSugerida = condicaoSugerida;
    }

    public List<PlanoDuracaoDTO> getDuracoes() {
        return duracoes;
    }

    public void setDuracoes(List<PlanoDuracaoDTO> duracoes) {
        this.duracoes = duracoes;
    }

    public List<PlanoHorarioDTO> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<PlanoHorarioDTO> horarios) {
        this.horarios = horarios;
    }

    public List<PlanoModalidadeDTO> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<PlanoModalidadeDTO> modalidades) {
        this.modalidades = modalidades;
    }

    public List<PlanoProdutoDTO> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<PlanoProdutoDTO> produtos) {
        this.produtos = produtos;
    }

    public List<PacoteDTO> getPacotes() {
        return pacotes;
    }

    public void setPacotes(List<PacoteDTO> pacotes) {
        this.pacotes = pacotes;
    }

    public List<ConvenioDTO> getConvenios() {
        return convenios;
    }

    public void setConvenios(List<ConvenioDTO> convenios) {
        this.convenios = convenios;
    }

    public List<DescontoDTO> getDescontos() {
        return descontos;
    }

    public void setDescontos(List<DescontoDTO> descontos) {
        this.descontos = descontos;
    }

    public Integer getPlano() {


        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Boolean getExibirEditarTurma() {
        return exibirEditarTurma;
    }

    public void setExibirEditarTurma(Boolean exibirEditarTurma) {
        this.exibirEditarTurma = exibirEditarTurma;
    }

    public Boolean getExibirValorModalidade() {
        return exibirValorModalidade;
    }

    public void setExibirValorModalidade(Boolean exibirValorModalidade) {
        this.exibirValorModalidade = exibirValorModalidade;
    }

    public Integer getContratoBaseado() {
        return contratoBaseado;
    }

    public void setContratoBaseado(Integer contratoBaseado) {
        this.contratoBaseado = contratoBaseado;
    }

    public Boolean getUsaArredondamento() {
        return usaArredondamento;
    }

    public void setUsaArredondamento(Boolean usaArredondamento) {
        this.usaArredondamento = usaArredondamento;
    }

    public Boolean getFecharNegociacaoSemAutorizacaoDCC() {
        return fecharNegociacaoSemAutorizacaoDCC;
    }

    public void setFecharNegociacaoSemAutorizacaoDCC(Boolean fecharNegociacaoSemAutorizacaoDCC) {
        this.fecharNegociacaoSemAutorizacaoDCC = fecharNegociacaoSemAutorizacaoDCC;
    }

    public List<Integer> getPacotesSugeridos() {
        return pacotesSugeridos;
    }

    public void setPacotesSugeridos(List<Integer> pacotesSugeridas) {
        this.pacotesSugeridos = pacotesSugeridas;
    }

    public Boolean getBolsa() {
        return bolsa;
    }

    public void setBolsa(Boolean bolsa) {
        this.bolsa = bolsa;
    }

    public Boolean getDeveGerarParcelasComValorDiferente() {
        return deveGerarParcelasComValorDiferente;
    }

    public void setDeveGerarParcelasComValorDiferente(Boolean deveGerarParcelasComValorDiferente) {
        this.deveGerarParcelasComValorDiferente = deveGerarParcelasComValorDiferente;
    }

    public Boolean getIsentarRematricula() {
        return isentarRematricula;
    }

    public void setIsentarRematricula(Boolean isentarRematricula) {
        this.isentarRematricula = isentarRematricula;
    }

    public Integer getQtdDiasCobrarRematricula() {
        if(qtdDiasCobrarRematricula == null){
            qtdDiasCobrarRematricula = 0;
        }
        return qtdDiasCobrarRematricula;
    }

    public void setQtdDiasCobrarRematricula(Integer qtdDiasCobrarRematricula) {
        this.qtdDiasCobrarRematricula = qtdDiasCobrarRematricula;
    }
}
