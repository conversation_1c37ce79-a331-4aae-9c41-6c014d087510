package com.pacto.adm.core.dto.empresa;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.integracoes.EmpresaConfigEstacionamentoDTO;
import com.pacto.adm.core.dto.integracoes.ParceiroFidelidadeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Informações da Empresa")
@NoArgsConstructor
@Data
public class EmpresaDTO {

    @Schema(description = "Código único identificador da empresa", example = "1")
    private Integer codigo;

    @Schema(description = "Nome fantasia da empresa", example = "ACADEMIA PACTO")
    private String nome;

    @Schema(description = "Indica se a empresa está ativa no sistema", example = "true")
    private Boolean ativa;

    @Schema(description = "Setor ao qual a empresa pertence", example = "Academia")
    private String setor;

    @Schema(description = "Detalhes do estado onde a empresa está localizada")
    private EstadoDTO estado;

    @Schema(description = "Detalhes da cidade onde a empresa está localizada")
    private CidadeDTO cidade;

    @Schema(description = "Indica se a empresa trabalha com pontuação", example = "false")
    private Boolean trabalharComPontuacao;

    @Schema(description = "Indica se a empresa utiliza NFSE (Nota Fiscal de Serviços Eletrônica)", example = "false")
    private Boolean usarNfse;

    @Schema(description = "Indica se a empresa utiliza NFC-e (Nota Fiscal do Consumidor Eletrônica)", example = "false")
    private Boolean usarNfce;

    @Schema(description = "Indica se a integração com MyWellness está habilitada", example = "false")
    private boolean integracaoMyWellneHabilitada;

    @Schema(description = "Indica se a empresa envia vínculos para MyWellness", example = "true")
    private boolean integracaoMyWellnessEnviarVinculos;

    @Schema(description = "Indica se a empresa envia grupos para MyWellness", example = "true")
    private boolean integracaoMyWellnessEnviarGrupos;

    @Schema(description = "URL da instalação MyWellness Facility", example = "https://mywellness.example.com")
    private String integracaoMyWellnessFacilityUrl;

    @Schema(description = "Chave API para integração com MyWellness", example = "api-key-12345")
    private String integracaMyWellneApiKey;

    @Schema(description = "Usuário para autenticação na integração MyWellness", example = "usuario-mywellness")
    private String integracaoMyWellnessUser;

    @Schema(description = "Senha para autenticação na integração MyWellness", example = "senha-mywellness")
    private String integracaoMyWellnessPassword;

    @Schema(description = "Número de dias de vigência para GymPass na integração MyWellness", example = "30")
    private Integer nrDiasVigenciaMyWellnessGymPass;

    @Schema(description = "Tipo de vigência para GymPass na integração MyWellness", example = "1")
    private Integer tipoVigenciaMyWellnessGympass;

    @Schema(description = "Indica se a integração MentorWeb está habilitada", example = "false")
    private boolean integracaoMentorWebHabilitada;

    @Schema(description = "URL para integração MentorWeb", example = "https://mentorweb.example.com")
    private String integracaoMentorWebUrl;

    @Schema(description = "Serviço utilizado na integração MentorWeb", example = "servico-mentorweb")
    private String integracaoMentorWebServico;

    @Schema(description = "Usuário para autenticação na integração MentorWeb", example = "usuario-mentorweb")
    private String integracaoMentorWebUser;

    @Schema(description = "Senha para autenticação na integração MentorWeb", example = "senha-mentorweb")
    private String integracaoMentorWebPassword;

    @Schema(description = "Indica se a empresa utiliza sistema de estacionamento", example = "false")
    private boolean utilizaSistemaEstacionamento;

    @Schema(description = "Configurações de estacionamento da empresa")
    private EmpresaConfigEstacionamentoDTO empresaConfigEstacionamento;

    @Schema(description = "Indica se a empresa utiliza parceiros de fidelidade", example = "false")
    private boolean usarParceiroFidelidade;

    @Schema(description = "Detalhes do parceiro de fidelidade associado à empresa")
    private ParceiroFidelidadeDTO parceiroFidelidade;

    @Schema(description = "Indica se a empresa notifica via webhook", example = "false")
    private boolean notificarWebhook;

    @Schema(description = "URL para notificação via webhook", example = "https://webhook.example.com/notify")
    private String urlWebhookNotificar;

    @Schema(description = "Indica se a integração AmigoFit está habilitada", example = "false")
    private boolean integracaoAmigoFitHabilitada;

    @Schema(description = "Nome de usuário para integração AmigoFit", example = "usuario-amigofit")
    private String nomeUsuarioAmigoFit;

    @Schema(description = "Senha para integração AmigoFit", example = "senha-amigofit")
    private String senhaUsuarioAmigoFit;

    @Schema(description = "Indica se o CPF é usado como código interno no WeHelp", example = "false")
    private boolean cpfCodigoInternoWeHelp;

    @Schema(description = "Token BuzzLead para integração", example = "token-buzzlead-12345")
    private String tokenBuzzLead;

    @Schema(description = "Token SMS para envio de mensagens", example = "token-sms-67890")
    private String tokenSMS;

    @Schema(description = "Token ShortCode para envio de SMS", example = "shortcode-12345")
    private String tokenSMSShortCode;

    @Schema(description = "Indica se a integração F360 Relatório Financeiro está habilitada", example = "false")
    private boolean integracaoF360RelFatHabilitada;

    @Schema(description = "Servidor FTP para integração F360", example = "ftp.f360.example.com")
    private String integracaoF360FtpServer;

    @Schema(description = "Porta FTP para integração F360", example = "21")
    private Integer integracaoF360FtpPort;

    @Schema(description = "Usuário para autenticação FTP na integração F360", example = "usuario-f360")
    private String integracaoF360User;

    @Schema(description = "Senha para autenticação FTP na integração F360", example = "senha-f360")
    private String integracaoF360Password;

    @Schema(description = "Diretório FTP para integração F360", example = "/arquivos/f360")
    private String integracaoF360Dir;

    @Schema(description = "Indica se a integração F360 é quinzenal", example = "false")
    private boolean integracaoF360Quinzenal;

    @Schema(description = "Indica se a empresa utiliza conciliadora", example = "false")
    private boolean usarConciliadora;

    @Schema(description = "Nome da empresa conciliadora", example = "Conciliadora Exemplo")
    private String empresaConciliadora;

    @Schema(description = "Senha da empresa conciliadora", example = "senha-conciliadora")
    private String senhaConciliadora;

    @Schema(description = "Código GymPass associado à empresa", example = "gym-pass-12345")
    private String codigoGymPass;

    @Schema(description = "Token API para integração GymPass", example = "api-token-gympass")
    private String tokenApiGymPass;

    @Schema(description = "Indica se a integração Spivi está habilitada", example = "false")
    private boolean integracaoSpiviHabilitada;

    @Schema(description = "Nome da fonte na integração Spivi", example = "Fonte Exemplo")
    private String integracaoSpiviSourceName;

    @Schema(description = "ID do site na integração Spivi", example = "12345")
    private Integer integracaoSpiviSiteID;

    @Schema(description = "Senha para integração Spivi", example = "senha-spivi")
    private String integracaoSpiviPassword;

    @Schema(description = "Indica se a empresa utiliza integrações CRM", example = "false")
    private boolean usaIntegracoesCrm;

    @Schema(description = "Período de carência para renovação em dias", example = "15")
    private Integer carenciaRenovacao;

    @Schema(description = "Indica se a empresa utiliza gestão de créditos personalizados", example = "false")
    private Boolean usarGestaoCreditosPersonal;

    @Schema(description = "Tipo de gestão de NFS-e", example = "1")
    private Integer tipoGestaoNfse;

    @Schema(description = "CNPJ da empresa", example = "12.345.678/0001-90")
    private String cnpj;

    @Schema(description = "Razão social da empresa", example = "RAZÃO SOCIAL DA EMPRESA")
    private String razaoSocial;

    @Schema(description = "E-mail oficial da empresa", example = "<EMAIL>")
    private String email;

    @Schema(description = "Arredondamento utilizado pela empresa", example = "2")
    private Integer arredondamento;

    @Schema(description = "Indica se o cadastro SESI da empresa está habilitado", example = "false")
    private boolean habilitarCadastroEmpresaSesi;

    @Schema(description = "Indica se a empresa utiliza gestão de clientes com restrições", example = "false")
    private Boolean utilizaGestaoClientesComRestricoes;

    @Schema(description = "Nome do aplicativo Nuvemshop", example = "App Exemplo")
    private String integracaoNuvemshopNomeApp;

    @Schema(description = "E-mail associado à conta Nuvemshop", example = "<EMAIL>")
    private String integracaoNuvemshopEmail;

    @Schema(description = "Token de acesso para Nuvemshop", example = "token-nuvemshop-12345")
    private String integracaoNuvemshopTokenAcesso;

    @Schema(description = "ID da loja Nuvemshop", example = "store-id-67890")
    private String integracaoNuvemshopStoreId;

    @Schema(description = "Indica se a integração Nuvemshop está habilitada", example = "false")
    private boolean integracaoNuvemshopHabilitada = false;

    @Schema(description = "Indica se a empresa permite contratos concomitantes", example = "false")
    private boolean permiteContratosConcomintante = false;

    private Boolean pontuarApenasCampanhasAtivas = false;

    @Schema(description = "Indica se a empresa utiliza PactoPrint", example = "false")
    private Boolean utilizarPactoPrint = false;

    @Schema(description = "Indica se o BV é obrigatório", example = "false")
    private Boolean bvObrigatorio;
    private Boolean integracaoManyChatHabilitada = false;
    private String integracaoManyChatTokenApi = "";
    private String nomeCurto;
    private Boolean usarSescDf;
    private String tokenSescDf;
    private Boolean utilizaConfigCancelamentoSesc;
    private boolean horariocapacidadeporcategoria = false;

    public EmpresaDTO(Integer codigo, String nome) {
        this.codigo = codigo;
        this.nome = nome;
    }

    public EmpresaDTO(Integer codigo, String nome, String setor, EstadoDTO estado, CidadeDTO cidade) {
        this.codigo = codigo;
        this.nome = nome;
        this.setor = setor;
        this.estado = estado;
        this.cidade = cidade;
    }
}
