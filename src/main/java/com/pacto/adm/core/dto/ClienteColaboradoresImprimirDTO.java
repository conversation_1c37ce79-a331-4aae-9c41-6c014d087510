package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Cliente Imprimir", description = "Informações do cliente que são impressas")
public class ClienteColaboradoresImprimirDTO {

    @Schema(description = "Código de matrícula", example = "001023")
    private String matricula;

    @Schema(description = "Nome do cliente", example = "Renato Alves Cariani")
    private String nome;

    @Schema(description = "CPF do cliente", example = "123.456.789-10")
    private String cpf;

    @Schema(description = "Tipo do colaborador", example = "Aluno")
    private String tipo;

    @Schema(description = "Email do cliente", example = "<EMAIL>")
    private String email;

    @Schema(description = "Telefone do cliente", example = "(99)1234500000")
    private String telefone;

    @Schema(description = "Situação do cliente", example = "ATIVO")
    private String  situacaoCliente;

    @Schema(description = "Situação do colaborador da academia que o cliente possui vínculo (Cadastro, Contrato etc)", example = "ATIVO")
    private String  situacaoColaborador;

    @Schema(description = "Nome da empresa que o cliente está cadastrado", example = "ACADEMIA PACTO")
    private String empresa;

    @Schema(description = "Categoria do cliente", example = "Aluno")
    private String categoria;

    @Schema(description = "Classificação do cliente", example = "10")
    private String classificacao;

    @Schema(description = "Grupo do cliente", example = "BodyBuilder")
    private String grupo;

    public ClienteColaboradoresImprimirDTO() {

    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getSituacaoCliente() {
        return situacaoCliente;
    }

    public void setSituacaoCliente(String situacaoCliente) {
        this.situacaoCliente = situacaoCliente;
    }

    public String getSituacaoColaborador() {
        return situacaoColaborador;
    }

    public void setSituacaoColaborador(String situacaoColaborador) {
        this.situacaoColaborador = situacaoColaborador;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public String getClassificacao() {
        return classificacao;
    }

    public void setClassificacao(String classificacao) {
        this.classificacao = classificacao;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getGrupo() {
        return grupo;
    }

    public void setGrupo(String grupo) {
        this.grupo = grupo;
    }
}
