package com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis;

import io.swagger.v3.oas.annotations.media.Schema;


//@Schema(name = "Resposta Número Double", description = "Representação das respostas das requisições que devolvem um número double")
public class EnvelopeRespostaDouble {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas", example = "50.00")
    private Double content;

    public Double getContent() {
        return content;
    }

    public void setContent(Double content) {
        this.content = content;
    }

    public final static String resposta = "{\"content\":  50.00}";

}
