package com.pacto.adm.core.dto.enveloperesposta.aulas.reposicao;

import com.pacto.adm.core.dto.enveloperesposta.aulas.EnvelopeRespostaHorarioTurmaDTO;
import com.pacto.adm.core.dto.enveloperesposta.turma.EnvelopeRespostaTurmaDTO;
import com.pacto.adm.core.dto.enveloperesposta.aulas.experimental.EnvelopeRespostaConviteAulaExperimentalDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.EnvelopeRespostaClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaUsuarioDTO;
import com.pacto.adm.core.dto.reposicao.ReposicaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Respostas Aluguel de Armários", description = "Representação das respostas contendo uma lista de aluguel de armários")
public class EnvelopeRespostaReposicaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ReposicaoDTO content;

    public ReposicaoDTO getContent() {
        return content;
    }

    public void setContent(ReposicaoDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 2001, "
                    + "\"dataLancamento\": \"2023-07-12T00:00:00Z\", "
                    + "\"dataOrigem\": \"2023-07-10T08:00:00Z\", "
                    + "\"dataPresenca\": \"2023-07-15T08:00:00Z\", "
                    + "\"dataReposicao\": \"2023-07-15T08:00:00Z\", "
                    + "\"marcacaoAula\": true, "
                    + "\"origemSistema\": 1, "
                    + "\"spiviEventId\": 3001, "
                    + "\"spiviSearId\": 4001, "
                    + "\"cliente\": {" + EnvelopeRespostaClienteDTO.atributos + "}, "
                    + "\"contrato\": \"INFORMAÇÕES DO CONTRATO\", "
                    + "\"conviteAulaExperimental\": {" + EnvelopeRespostaConviteAulaExperimentalDTO.atributos + "}, "
                    + "\"horarioTurma\": {" + EnvelopeRespostaHorarioTurmaDTO.atributos + "}, "
                    + "\"horarioTurmaOrigem\": {" + EnvelopeRespostaHorarioTurmaDTO.atributos + "}, "
                    + "\"turmaDestino\": {" + EnvelopeRespostaTurmaDTO.atributos + "}, "
                    + "\"turmaOrigem\": {" + EnvelopeRespostaTurmaDTO.atributos + "}, "
                    + "\"usuario\": {" + EnvelopeRespostaUsuarioDTO.atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

    public final static String requestBody = "{" + atributos + "}";



}
