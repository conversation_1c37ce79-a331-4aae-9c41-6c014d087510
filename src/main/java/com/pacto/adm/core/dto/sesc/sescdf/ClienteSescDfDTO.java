package com.pacto.adm.core.dto.sesc.sescdf;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

@Data
public class ClienteSescDfDTO {
    private String barmatricula;
    private String gmatricula;
    private String situacao;
    private String cpf;
    private String nome;
    private String nomesocial;
    private String nomeafetivo;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate datanascimento;

    private String codgrupo;
    private String codcategoria;
    private String dscategoria;
    private String abreviacao;
    private String subcategoria;
    private String subcateg;
    private int unidade;
    private int matricula;
    private int serie;
    private int cduop;
    private int sqmatric;
    private int via;
    private int nudv;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate datacadastro;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate datarenovacao;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate dataemissao;

    private String cgc;
    private String nomeempresa;
    private String nomefantasia;
    private int unidadeimp;
    private String endereco;
    private String bairro;
    private String complemento;
    private int cdcidade;
    private String cidade;
    private String uf;
    private String cep;
    private String ddd;
    private String fonecelular;
    private String fonecomercial;
    private String telefone;
    private String email;
    private String aceite;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.S")
    private String dataAceite;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate valexdfe;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate valexpiscina;

    private int cdsexo;
    private String descsexo;
    private String pai;
    private String mae;
    private String rg;
    private String orgaoemissor;
    private String naturalidade;
    private String estadocivil;
    private String nacionalidade;
    private String habitacao;
    private String categ;
}
