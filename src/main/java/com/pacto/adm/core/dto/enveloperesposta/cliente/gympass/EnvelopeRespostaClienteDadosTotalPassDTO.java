package com.pacto.adm.core.dto.enveloperesposta.cliente.gympass;

import com.pacto.adm.core.dto.ClienteDadosGymPassDTO;
import com.pacto.adm.core.dto.ClienteDadosTotalPassDTO;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Atestado", description = "Representação das respostas contendo as informações do atestado")
public class EnvelopeRespostaClienteDadosTotalPassDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ClienteDadosTotalPassDTO content;

    public ClienteDadosTotalPassDTO getContent() {
        return content;
    }

    public void setContent(ClienteDadosTotalPassDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"empresa\": 1, "
                    + "\"cpf\": \"123.456.789-00\", "
                    + "\"pessoa\": 3001, "
                    + "\"matricula\": \"3221\"";


    public final static String requestBody = "{" + atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";



}
