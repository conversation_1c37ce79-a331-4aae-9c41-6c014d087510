package com.pacto.adm.core.dto.enveloperesposta.classificacao;

import com.pacto.adm.core.entities.Classificacao;
import io.swagger.v3.oas.annotations.media.Schema;

//@Schema(name = "Resposta Lista de Classificação", description = "Representação das respostas contendo uma lista de classificação")
public class EnvelopeRespostaClassificacao {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private Classificacao content;

    public Classificacao getContent() {
        return content;
    }

    public void setContent(Classificacao content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"nome\": \"Classificação da Pacto\", "
                    + "\"enviarsmsautomatico\": true";

    public final static String resposta = "{\"content\": {" + atributos + "}}";


}
