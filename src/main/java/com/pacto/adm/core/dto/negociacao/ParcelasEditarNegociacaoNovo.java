package com.pacto.adm.core.dto.negociacao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(name = "Parcelas Editar Negociação", description = "Informações sobre as parcelas utilizadas na edição da negociação.")
public class ParcelasEditarNegociacaoNovo {

    @Schema(description = "Descrição da parcela ou identificação adicional.", example = "Parcela 1 de 6")
    private String descricao = "";

    @Schema(description = "Cupom de desconto aplicado à parcela, se houver.", example = "DESC10")
    private String cupom = "";

    @Schema(description = "Número da parcela na sequência da negociação.", example = "1")
    private int nrParcela = 0;

    @Schema(description = "Valor financeiro da parcela.", example = "150.00")
    private double valorParcela = 0.0;

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public double getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public int getNrParcela() {
        return nrParcela;
    }

    public void setNrParcela(int nrParcela) {
        this.nrParcela = nrParcela;
    }

    public String getCupom() {
        return cupom;
    }

    public void setCupom(String cupom) {
        this.cupom = cupom;
    }
}