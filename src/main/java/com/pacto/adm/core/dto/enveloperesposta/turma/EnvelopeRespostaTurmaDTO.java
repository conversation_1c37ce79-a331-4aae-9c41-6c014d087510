package com.pacto.adm.core.dto.enveloperesposta.turma;

import com.pacto.adm.core.dto.enveloperesposta.modalidade.EnvelopeRespostaModalidadeDTO;
import com.pacto.adm.core.dto.turma.TurmaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaTurmaDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private TurmaDTO content;

    public TurmaDTO getContent() {
        return content;
    }

    public void setContent(TurmaDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"codigo\": 80, "
                    + "\"descricao\": \"Turma de Yoga Matinal\", "
                    + "\"turmaEscolhida\": true, "
                    + "\"modalidade\": {" + EnvelopeRespostaModalidadeDTO.atributos + "}";

    public final static String resposta = "{\"content\": {" + atributos + "}}";

}
