package com.pacto.adm.core.dto.negociacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "Plano Modalidade", description = "Informações sobre as modalidades associadas a um plano, incluindo quantidade, valor e configurações de turma.")
public class PlanoModalidadeDTO {

    @Schema(description = "Código identificador da modalidade no plano.", example = "1")
    private Integer codigo;

    @Schema(description = "Número de vezes que a modalidade pode ser utilizada no plano.", example = "4")
    private Integer nrvezes;

    @Schema(description = "Descrição da modalidade associada ao plano.", example = "Musculação")
    private String descricao;

    @Schema(description = "Valor unitário da modalidade no plano.", example = "150.00")
    private Double valorModalidade;

    @Schema(description = "Indica se a modalidade utiliza turma para as aulas.", example = "false")
    private boolean utilizarTurma = false;

    @Schema(description = "Indica se a modalidade é uma aula coletiva fixa.", example = "true")
    private boolean aulaColetivaFixa = false;

    @Schema(description = "Lista de configurações de vezes que a modalidade pode ser utilizada no plano.")
    private List<PlanoModalidadeVezesDTO> configsVezes;

    @Schema(description = "Lista de turmas associadas à modalidade no plano.")
    private List<PlanoModalidadeTurmaDTO> turmas;

    @Schema(description = "Lista de modalidades variáveis associadas ao plano.")
    private List<Integer> modalidadesVariaveis;

    @Schema(description = "Configurações de vezes sugeridas para a modalidade no plano.")
    private PlanoModalidadeVezesDTO vezesSugeridas;

    public List<PlanoModalidadeTurmaDTO> getTurmas() {
        return turmas;
    }

    public void setTurmas(List<PlanoModalidadeTurmaDTO> turmas) {
        this.turmas = turmas;
    }

    public PlanoModalidadeVezesDTO getVezesSugeridas() {
        return vezesSugeridas;
    }

    public void setVezesSugeridas(PlanoModalidadeVezesDTO vezesSugeridas) {
        this.vezesSugeridas = vezesSugeridas;
    }

    public List<PlanoModalidadeVezesDTO> getConfigsVezes() {
        return configsVezes;
    }

    public void setConfigsVezes(List<PlanoModalidadeVezesDTO> configsVezes) {
        this.configsVezes = configsVezes;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValorModalidade() {
        return valorModalidade;
    }

    public void setValorModalidade(Double valorModalidade) {
        this.valorModalidade = valorModalidade;
    }

    public boolean isUtilizarTurma() {
        return utilizarTurma;
    }

    public void setUtilizarTurma(boolean utilizarTurma) {
        this.utilizarTurma = utilizarTurma;
    }

    public Integer getNrvezes() {
        return nrvezes;
    }

    public void setNrvezes(Integer nrvezes) {
        this.nrvezes = nrvezes;
    }

    public boolean isAulaColetivaFixa() {
        return aulaColetivaFixa;
    }

    public void setAulaColetivaFixa(boolean aulaColetivaFixa) {
        this.aulaColetivaFixa = aulaColetivaFixa;
    }

    public List<Integer> getModalidadesVariaveis() {
        return modalidadesVariaveis;
    }

    public void setModalidadesVariaveis(List<Integer> modalidadesVariaveis) {
        this.modalidadesVariaveis = modalidadesVariaveis;
    }
}
