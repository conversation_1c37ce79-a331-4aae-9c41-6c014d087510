package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude
@Schema(name = "Cliente", description = "Informações detalhadas do cliente")
public class ClienteDTO {

    @Schema(description = "Código único identificador do Cliente", example = "10")
    private Integer codigo;

    @Schema(description = "Situação do cliente. \n\n" +
            "**Valores disponíveis**\n" +
            "- AV (A Vencer)\n" +
            "- AT (Ativo)\n" +
            "- CA (Cancelado)\n" +
            "- DE (Desistente)\n" +
            "- IN (Inativo)\n" +
            "- TR (Trancado)\n" +
            "- VE (Vencido)\n" +
            "- VI (Visitante)\n", example = "AT")
    private String situacao;

    @Schema(description = "Código da matrícula do Cliente", example = "36")
    private Integer codigoMatricula;

    @Schema(description = "Código da matrícula do Cliente no formato de texto", example = "000003")
    private String matricula;

    @Schema(description = "Pessoa relacionada ao cadastro do cliente")
    private PessoaDTO pessoa;

    @Schema(description = "Empresa do cliente")
    private EmpresaDTO empresa;

    @Schema(description = "Data de início do contrato", example = "28-04-2022")
    private String dataInicioContrato;

    @Schema(description = "Data de termino do contrato", example = "28-04-2025")
    private String dataTerminoContrato;

    @Schema(description = "Telefones do cliente", example = "(99) 99999-9999")
    private String telefones;

    public ClienteDTO() {}

    public ClienteDTO(String matricula, Integer codigoMatricula, PessoaDTO pessoa) {
        this.matricula = matricula;
        this.codigoMatricula = codigoMatricula;
        this.pessoa = pessoa;
    }

    public ClienteDTO(String matricula, Integer codigoMatricula, String situacao, PessoaDTO pessoa) {
        this.matricula = matricula;
        this.codigoMatricula = codigoMatricula;
        this.situacao = situacao;
        this.pessoa = pessoa;
    }

    public ClienteDTO(Integer codigo, String matricula, Integer codigoMatricula, String situacao, PessoaDTO pessoa) {
        this.codigo = codigo;
        this.matricula = matricula;
        this.codigoMatricula = codigoMatricula;
        this.situacao = situacao;
        this.pessoa = pessoa;
    }

    public ClienteDTO(Integer codigo, Integer codigoMatricula, String situacao, String telefones, PessoaDTO pessoa) {
        this.codigo = codigo;
        this.codigoMatricula = codigoMatricula;
        this.situacao = situacao;
        this.telefones = telefones;
        this.pessoa = pessoa;
    }

    public ClienteDTO(Integer codigoMatricula, String situacao, PessoaDTO pessoa, String telefones) {
        this.codigoMatricula = codigoMatricula;
        this.situacao = situacao;
        this.pessoa = pessoa;
        this.telefones = telefones;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public String getDataInicioContrato() {
        return dataInicioContrato;
    }

    public void setDataInicioContrato(String dataInicioContrato) {
        this.dataInicioContrato = dataInicioContrato;
    }

    public String getDataTerminoContrato() {
        return dataTerminoContrato;
    }
    public void setDataTerminoContrato(String dataTerminoContrato) {
        this.dataTerminoContrato = dataTerminoContrato;
    }

    public String getSituacaoApresentar() {
        if (situacao == null) {
            return "";
        }
        if (situacao.equals("AT")) {
            return "Ativo";
        }
        if (situacao.equals("IN")) {
            return "Inativo";
        }
        if (situacao.equals("VI")) {
            return "Visitante";
        }
        if (situacao.equals("TR")) {
            return "Trancado";
        }
        return situacao;
    }

    public String getTelefones() {
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }
}
