package com.pacto.adm.core.dto.enveloperesposta.negociacao.cartao;

import com.pacto.adm.core.dto.negociacao.DiaCartaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Representação das respostas")
public class EnvelopeRespostaDiaCartaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private DiaCartaoDTO content;

    public DiaCartaoDTO getContent() {
        return content;
    }

    public void setContent(DiaCartaoDTO content) {
        this.content = content;
    }


    public static final String atributos =
            "\"codigo\": 1, "
                    + "\"descricao\": \"Dia 10 de cada mês\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
