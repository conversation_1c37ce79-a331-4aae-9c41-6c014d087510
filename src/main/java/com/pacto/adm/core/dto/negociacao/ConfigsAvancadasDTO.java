package com.pacto.adm.core.dto.negociacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "Configurações Avançadas Contrato", description = "Parâmetros adicionais e personalizados para geração de contrato.")
public class ConfigsAvancadasDTO {

    @Schema(description = "Indica se deve cobrar taxa de matrícula.", example = "true")
    private Boolean cobrarMatricula;

    @Schema(description = "Indica se os produtos devem ser cobrados separadamente.", example = "false")
    private Boolean cobrarProdutosSeparados;

    @Schema(description = "Dia da primeira parcela em formato timestamp.", example = "1718054400000")
    private Long diaPrimeiraParcela;

    @Schema(description = "Dia do mês para cálculo de pró-rata.", example = "10")
    private Integer diaProrata;

    @Schema(description = "Permite ao usuário escolher o dia da primeira parcela.", example = "true")
    private Boolean escolherDiaPrimeiraParcela;

    @Schema(description = "Indica se os valores dos produtos devem ser divididos nas parcelas.", example = "false")
    private Boolean dividirProdutoParcela;

    @Schema(description = "Permite ao usuário escolher o dia de cálculo do pró-rata.", example = "true")
    private Boolean escolherDiaProrata;

    @Schema(description = "Quantidade de vezes que a matrícula será cobrada.", example = "1")
    private Integer vezesCobrarMatricula;

    @Schema(description = "Quantidade de vezes que os produtos separados serão cobrados.", example = "2")
    private Integer vezesCobrarProdutosSeparados;

    public Boolean getDividirProdutoParcela() {
        return dividirProdutoParcela;
    }

    public void setDividirProdutoParcela(Boolean dividirProdutoParcela) {
        this.dividirProdutoParcela = dividirProdutoParcela;
    }

    public Boolean getCobrarMatricula() {
        return cobrarMatricula;
    }

    public void setCobrarMatricula(Boolean cobrarMatricula) {
        this.cobrarMatricula = cobrarMatricula;
    }

    public Boolean getCobrarProdutosSeparados() {
        return cobrarProdutosSeparados;
    }

    public void setCobrarProdutosSeparados(Boolean cobrarProdutosSeparados) {
        this.cobrarProdutosSeparados = cobrarProdutosSeparados;
    }

    public Long getDiaPrimeiraParcela() {
        return diaPrimeiraParcela;
    }

    public void setDiaPrimeiraParcela(Long diaPrimeiraParcela) {
        this.diaPrimeiraParcela = diaPrimeiraParcela;
    }

    public Integer getDiaProrata() {
        return diaProrata;
    }

    public void setDiaProrata(Integer diaProrata) {
        this.diaProrata = diaProrata;
    }

    public Boolean getEscolherDiaPrimeiraParcela() {
        return escolherDiaPrimeiraParcela;
    }

    public void setEscolherDiaPrimeiraParcela(Boolean escolherDiaPrimeiraParcela) {
        this.escolherDiaPrimeiraParcela = escolherDiaPrimeiraParcela;
    }

    public Boolean getEscolherDiaProrata() {
        return escolherDiaProrata;
    }

    public void setEscolherDiaProrata(Boolean escolherDiaProrata) {
        this.escolherDiaProrata = escolherDiaProrata;
    }

    public Integer getVezesCobrarMatricula() {
        return vezesCobrarMatricula;
    }

    public void setVezesCobrarMatricula(Integer vezesCobrarMatricula) {
        this.vezesCobrarMatricula = vezesCobrarMatricula;
    }

    public Integer getVezesCobrarProdutosSeparados() {
        return vezesCobrarProdutosSeparados;
    }

    public void setVezesCobrarProdutosSeparados(Integer vezesCobrarProdutosSeparados) {
        this.vezesCobrarProdutosSeparados = vezesCobrarProdutosSeparados;
    }
}
