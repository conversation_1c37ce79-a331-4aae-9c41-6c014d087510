package com.pacto.adm.core.dto.observacaooperacao;

import com.pacto.adm.core.dto.MovParcelaDTO;
import com.pacto.adm.core.dto.PessoaDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(description = "Informações das observações de operações realizadas no sistema")
public class ObservacaoOperacaoDTO {

    @Schema(description = "Código único identificador da observação de operação", example = "1001")
    private Integer codigo;

    @Schema(description = "Justificativa da operação realizada", example = "Cancelamento solicitado pelo cliente devido a mudança de cidade")
    private String justificativa;

    @Schema(description = "Data em que a operação foi realizada", example = "2024-01-15T10:30:00.000Z")
    private Date dataOperacao;

    @Schema(description = "Tipo da observação de operação. \n\n" +
            "**Valores disponíveis**\n" +
            "- PC (PARCELA_CANCELADA)\n", example = "PC")
    private String tipoObservacao;

    @Schema(description = "Informações da parcela associada à operação")
    private MovParcelaDTO movParcela;

    @Schema(description = "Valor monetário da operação realizada", example = "199.99")
    private Double valorOperacao;

    @Schema(description = "Nome do usuário responsável pela operação", example = "João Silva")
    private String usuarioResponsavel;

    @Schema(description = "Tipo de cancelamento realizado", example = "Manual")
    private String tipoCancelamento;

    @Schema(description = "Informações da pessoa associada à operação")
    private PessoaDTO pessoa;

    @Schema(description = "Código identificador do cliente", example = "12345")
    private Integer codigoCliente;

    @Schema(description = "Matrícula do cliente no sistema", example = "MAT001234")
    private String matriculaCliente;

    @Schema(description = "Código da matrícula do cliente", example = "5678")
    private Integer codigoMatriculaCliente;

    public ObservacaoOperacaoDTO(
            Integer codigo, String justificativa, Date dataOperacao, String tipoObservacao, MovParcelaDTO movParcela,
            Double valorOperacao, String usuarioResponsavel, String tipoCancelamento, PessoaDTO pessoa,
            Integer codigoCliente, String matriculaCliente, Integer codigoMatriculaCliente
    ) {
        this.codigo = codigo;
        this.justificativa = justificativa;
        this.dataOperacao = dataOperacao;
        this.tipoObservacao = tipoObservacao;
        this.movParcela = movParcela;
        this.valorOperacao = valorOperacao;
        this.usuarioResponsavel = usuarioResponsavel;
        this.tipoCancelamento = tipoCancelamento;
        this.pessoa = pessoa;
        this.codigoCliente = codigoCliente;
        this.matriculaCliente = matriculaCliente;
        this.codigoMatriculaCliente = codigoMatriculaCliente;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public String getTipoObservacao() {
        return tipoObservacao;
    }

    public void setTipoObservacao(String tipoObservacao) {
        this.tipoObservacao = tipoObservacao;
    }

    public MovParcelaDTO getMovParcela() {
        return movParcela;
    }

    public void setMovParcela(MovParcelaDTO movParcela) {
        this.movParcela = movParcela;
    }

    public Double getValorOperacao() {
        return valorOperacao;
    }

    public void setValorOperacao(Double valorOperacao) {
        this.valorOperacao = valorOperacao;
    }

    public String getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(String usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public String getTipoCancelamento() {
        return tipoCancelamento;
    }

    public void setTipoCancelamento(String tipoCancelamento) {
        this.tipoCancelamento = tipoCancelamento;
    }

    public PessoaDTO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaDTO pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    public void setMatriculaCliente(String matriculaCliente) {
        this.matriculaCliente = matriculaCliente;
    }

    public Integer getCodigoMatriculaCliente() {
        return codigoMatriculaCliente;
    }

    public void setCodigoMatriculaCliente(Integer codigoMatriculaCliente) {
        this.codigoMatriculaCliente = codigoMatriculaCliente;
    }
}
