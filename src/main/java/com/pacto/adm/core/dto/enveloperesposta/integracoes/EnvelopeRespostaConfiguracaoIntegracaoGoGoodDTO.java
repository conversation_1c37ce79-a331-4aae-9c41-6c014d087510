package com.pacto.adm.core.dto.enveloperesposta.integracoes;

import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.integracoes.ConfiguracaoIntegracaoGoGoodDTO;
import io.swagger.v3.oas.annotations.media.Schema;

public class EnvelopeRespostaConfiguracaoIntegracaoGoGoodDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ConfiguracaoIntegracaoGoGoodDTO content;

    public ConfiguracaoIntegracaoGoGoodDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracaoGoGoodDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"tokenAcademyGoGood\": \"token-academy-gogood-abc123\", "
                    + "\"empresa\": {" + EnvelopeRespostaEmpresaDTO.atributos + "}";

    public static final String atributosSemEmpresa =
            "\"tokenAcademyGoGood\": \"token-academy-gogood-abc123\", "
                    + "\"empresa\": \"INFORMAÇÕES DA EMPRESA\"";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";

}
