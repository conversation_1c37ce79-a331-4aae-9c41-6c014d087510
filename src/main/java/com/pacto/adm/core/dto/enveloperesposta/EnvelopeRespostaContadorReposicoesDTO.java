package com.pacto.adm.core.dto.enveloperesposta;

import com.pacto.adm.core.dto.ClienteDadosAuxiliaresDTO;
import com.pacto.adm.core.dto.auladesmarcada.ContadorReposicoesDTO;
import io.swagger.v3.oas.annotations.media.Schema;


//@Schema(name = "Resposta Dados Auxiliares de Cliente", description = "Representação das respostas das requisições que devolvem dados auxiliares de um cliente")
public class EnvelopeRespostaContadorReposicoesDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private ContadorReposicoesDTO content;

    public ContadorReposicoesDTO getContent() {
        return content;
    }

    public void setContent(ContadorReposicoesDTO content) {
        this.content = content;
    }

    public static final String atributos =
            "\"reposicoesDisponiveis\": 3, "
                    + "\"reposicoesUtilizadas\": 1, "
                    + "\"reposicoesExpiradas\": 0";

    public final static String resposta = "{\"content\": {" + atributos + "}}";
    public final static String requestBody = "{" + atributos + "}";


}
