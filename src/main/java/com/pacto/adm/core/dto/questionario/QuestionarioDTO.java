package com.pacto.adm.core.dto.questionario;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(name = "Questionário", description = "Informações do questionário")
public class QuestionarioDTO {

    @Schema(description = "Código identificador do questionário", example = "1")
    private Integer codigo;

    @Schema(description = "Nome interno do questionário", example = "Questionário Aula")
    private String nomeInterno;

    @Schema(description = "Tipo do questionário", example = "PL")
    private String tipoQuestionario;

    @Schema(description = "Cor de Fundo do Questionário", example = "#3FA9F5")
    private String fundoCor;

    @Schema(description = "Imagem de Fundo do Questionário", example = "www.pactosolucoes.com.br/imagens/questionario.png")
    private String fundoImagem;

    @Schema(description = "Texto de Início do Questionário", example = "Questionário Aula")
    private String textoInicio;

    @Schema(description = "Texto de Fim do Questionário", example = "Obrigado por responder!")
    private String textoFim;

    @Schema(description = "Indica se as perguntas possuem apenas uma resposta", example = "false")
    private Boolean somenteUmaResposta;

    @Schema(description = "Indica se o questionário está ativo", example = "true")
    private Boolean ativo;

    @Schema(description = "Título de pesquisa", example = "AULA")
    private String tituloPesquisa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeInterno() {
        return nomeInterno;
    }

    public void setNomeInterno(String nomeInterno) {
        this.nomeInterno = nomeInterno;
    }

    public String getTipoQuestionario() {
        return tipoQuestionario;
    }

    public void setTipoQuestionario(String tipoQuestionario) {
        this.tipoQuestionario = tipoQuestionario;
    }

    public String getFundoCor() {
        return fundoCor;
    }

    public void setFundoCor(String fundoCor) {
        this.fundoCor = fundoCor;
    }

    public String getFundoImagem() {
        return fundoImagem;
    }

    public void setFundoImagem(String fundoImagem) {
        this.fundoImagem = fundoImagem;
    }

    public String getTextoInicio() {
        return textoInicio;
    }

    public void setTextoInicio(String textoInicio) {
        this.textoInicio = textoInicio;
    }

    public String getTextoFim() {
        return textoFim;
    }

    public void setTextoFim(String textoFim) {
        this.textoFim = textoFim;
    }

    public Boolean getSomenteUmaResposta() {
        return somenteUmaResposta;
    }

    public void setSomenteUmaResposta(Boolean somenteUmaResposta) {
        this.somenteUmaResposta = somenteUmaResposta;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public String getTituloPesquisa() {
        return tituloPesquisa;
    }

    public void setTituloPesquisa(String tituloPesquisa) {
        this.tituloPesquisa = tituloPesquisa;
    }
}
