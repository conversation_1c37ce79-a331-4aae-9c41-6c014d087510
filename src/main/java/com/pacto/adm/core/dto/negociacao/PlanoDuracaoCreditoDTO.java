package com.pacto.adm.core.dto.negociacao;

import com.pacto.adm.core.enumerador.TipoHorarioCreditoTreinoEnum;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Plano Duração Crédito", description = "Créditos relacionados à duração de um plano, incluindo frequência e valores.")
public class PlanoDuracaoCreditoDTO {

    @Schema(description = "Código identificador do plano duração de crédito.", example = "1")
    private Integer codigo;

    @Schema(description = "Código da duração do plano ao qual este crédito está vinculado.", example = "12")
    private Integer planoDuracao;

    @Schema(description = "Número de vezes por semana em que os créditos podem ser utilizados.", example = "3")
    private Integer numeroVezesSemana;

    @Schema(description = "Quantidade total de créditos disponíveis para compra.", example = "24")
    private Integer quantidadeCreditoCompra;

    @Schema(description = "Quantidade de créditos disponibilizados mensalmente.", example = "8")
    private Integer quantidadeCreditoMensal;

    @Schema(description = "Tipo de horário permitido para uso dos créditos de treino." +
            "0 - NAO UTILIZADO , 1 - LIVRE, 2 - LIVRE OBRIGATÓRIO MARCAR AULA, 3 - HORÁRIO DA TURMA;", example = "2", implementation = TipoHorarioCreditoTreinoEnum.class)
    private Integer tipoHorarioCreditoTreino;

    @Schema(description = "Valor unitário de cada crédito.", example = "25.00")
    private Double valorUnitario;

    @Schema(description = "Indica se o uso dos créditos é livre, sem restrição de horários.", example = "true")
    private Boolean livre;

    public Boolean getLivre() {
        return livre;
    }

    public void setLivre(Boolean livre) {
        this.livre = livre;
    }

    public Integer getTipoHorarioCreditoTreino() {
        return tipoHorarioCreditoTreino;
    }

    public void setTipoHorarioCreditoTreino(Integer tipoHorarioCreditoTreino) {
        this.tipoHorarioCreditoTreino = tipoHorarioCreditoTreino;
    }

    public Double getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public Integer getNumeroVezesSemana() {
        return numeroVezesSemana;
    }

    public void setNumeroVezesSemana(Integer numeroVezesSemana) {
        this.numeroVezesSemana = numeroVezesSemana;
    }

    public Integer getQuantidadeCreditoCompra() {
        return quantidadeCreditoCompra;
    }

    public void setQuantidadeCreditoCompra(Integer quantidadeCreditoCompra) {
        this.quantidadeCreditoCompra = quantidadeCreditoCompra;
    }

    public Integer getQuantidadeCreditoMensal() {
        return quantidadeCreditoMensal;
    }

    public void setQuantidadeCreditoMensal(Integer quantidadeCreditoMensal) {
        this.quantidadeCreditoMensal = quantidadeCreditoMensal;
    }

    public Integer getPlanoDuracao() {
        return planoDuracao;
    }

    public void setPlanoDuracao(Integer planoDuracao) {
        this.planoDuracao = planoDuracao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }



}
