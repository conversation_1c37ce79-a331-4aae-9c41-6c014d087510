package com.pacto.adm.core.enumerador;

public enum PerfilUsuarioEnum {
    TODOS(0,"Todos"),
    ADMINISTRADOR(1,"Administrador"),
    CONSULTOR(2,"Consultor"),
    GERENTE(3,"<PERSON>erente"),
    PROFESSOR(4,"Professor");

    private int id;
    private String nome;

    private PerfilUsuarioEnum(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public String getNome(){
        return nome;
    }

    public int getId() {
        return id;
    }

    public static PerfilUsuarioEnum getFromOrdinal(int o){
        for(PerfilUsuarioEnum ind : values()){
            if(ind.ordinal() == o){
                return ind;
            }
        }
        return null;
    }
}
