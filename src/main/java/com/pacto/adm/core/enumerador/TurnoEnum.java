package com.pacto.adm.core.enumerador;

public enum TurnoEnum {

    MANHA(0, 1159),
    TARDE(1200, 1859),
    NOIT<PERSON>(1900, 2359);

    private int horaInicial;
    private int horaFinal;

    TurnoEnum(int horaInicial, int horaFinal) {
        this.horaInicial = horaInicial;
        this.horaFinal = horaFinal;
    }

    public static TurnoEnum turno(String turno){
        try {
            return valueOf(turno.toUpperCase());
        }catch (Exception e){
            return null;
        }
    }

    public static boolean estaNoTurno(String horario, TurnoEnum turno){
        try {
            if(turno == null){
                return true;
            }
            Integer horarioInt = Integer.valueOf(horario.replace(":", ""));
            return horarioInt >= turno.horaInicial && horarioInt < turno.horaFinal;
        }catch (Exception e){
            return true;
        }
    }
}
