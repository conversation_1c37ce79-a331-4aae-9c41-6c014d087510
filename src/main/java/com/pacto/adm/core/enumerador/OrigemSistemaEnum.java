package com.pacto.adm.core.enumerador;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Created by ulisses on 04/12/2015.
 */
public enum OrigemSistemaEnum {

    ZW(1, "ZillyonWeb"),
    AULA_CHEIA(2, "Agenda Web"),
    TREINO(3, "Pacto Treino"),
    APP_TREINO(4, "App Treino"),
    APP_PROFESSOR(5, "App Professor"),
    AUTO_ATENDIMENTO(6, "Autoatendimento"),
    SITE(7, "Site Vendas"),
    BUZZLEAD(8, "Buzz Lead"),
    VENDAS_ONLINE_2(9, "Vendas 2.0"),
    APP_CONSULTOR(10, "App do consultor"),
    BOOKING_GYMPASS(11, "Booking Gympass"),
    FILA_ESPERA(12, "Fila de espera"),
    IMPORTACAO_API(13, "Importação API"),
    HUBSPOT(14, "<PERSON>bspot Lead"),
    CRM_META_DIARIA(15, "CRM Meta Diaria"),
    APP_FLOW (16, "Pacto Flow"),
    NOVA_TELA_NEGOCIACAO(17, "Nova Tela de Negociação"),
    NOVA_TELA_CAIXA_ABERTO_V2(18, "Nova Tela de Caixa Aberto"),
    NOVA_TELA_CADASTRO_CLIENTE_V2(19, "Nova Tela de Cadastro Cliente"),
    API_SISTEMA_PACTO(20, "API Sistema Pacto"), //API PACTO UTILIZANDO TOKEN GERADO NAS INTEGRACOES
    CONVERSAS_IA(21, "Conversas IA"),
    ZW_BOOT(22, "ZW Boot"),
    ;


    OrigemSistemaEnum(Integer codigo, String descricao){
        this.codigo = codigo;
        this.descricao =descricao;
    }

    private Integer codigo;
    private String descricao;

    public static OrigemSistemaEnum getOrigemSistema(Integer codigo){
        for (OrigemSistemaEnum obj: OrigemSistemaEnum.values()){
            if (obj.getCodigo().equals(codigo))
                return obj;
        }
        return null;
    }

    @JsonValue
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
