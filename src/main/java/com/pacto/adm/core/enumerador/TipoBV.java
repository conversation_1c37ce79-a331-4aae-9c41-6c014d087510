package com.pacto.adm.core.enumerador;

/**
 * Enumerador de tipo de boletim de visita.
 * 
 */
public enum TipoBV {

	MA(1, "<PERSON>r<PERSON><PERSON>", "MA"),
	RT(2, "Retorno", "RT"),
	RE(3, "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RE"),
	SS_PRIMEIRA_COMPRA(4, "Sessão - Primeira Compra", "SS-P"),
	SS_RETORNO_COMPRA(5, "Sessão - Retorno", "SS-R");
	private Integer codigo;
	private String descricao;
	private String sigla;

	private TipoBV(Integer codigo, String descricao, String sigla) {
		this.codigo = codigo;
		this.descricao = descricao;
		this.sigla = sigla;
	}

	public static Integer getTipoPorSigla(String consulta) {
		for (TipoBV origem : TipoBV.values()) {
			if (origem.getSigla().equals(consulta)) {
				return origem.codigo;
			}
		}
		return null;
	}

	public static TipoBV getTipo(int consulta) {
		for (TipoBV origem : TipoBV.values()) {
			if (origem.getCodigo() == consulta) {
				return origem;
			}
		}
		return null;
	}


	public Integer getCodigo() {
		return codigo;
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}

	public String getDescricao() {
		return descricao;
	}

	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	public String getSigla() {
		return sigla;
	}

	public void setSigla(String sigla) {
		this.sigla = sigla;
	}

}
