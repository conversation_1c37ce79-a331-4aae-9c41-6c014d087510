package com.pacto.adm.core.enumerador;

public enum UsoCreditoPersonalEnum {
    SOMENTE_PRE_PAGO(1,"Somente pré-pago"),
    PERMITIR_POS_PAGO(2,"Permitir pós-pago");
    
    public Integer codigo;
    public String nome;

    UsoCreditoPersonalEnum(Integer codigo, String nome) {
        this.codigo = codigo;
        this.nome = nome;
    }

    public static UsoCreditoPersonalEnum getEnum(Integer codigo){
        for(UsoCreditoPersonalEnum e : UsoCreditoPersonalEnum.values()){
            if(e.codigo.equals(codigo)){
                return e;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getNome() {
        return nome;
    }

}