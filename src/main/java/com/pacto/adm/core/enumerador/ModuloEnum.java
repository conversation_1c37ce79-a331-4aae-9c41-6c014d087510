package com.pacto.adm.core.enumerador;

public enum ModuloEnum {
    ADM,
    CRM,
    FINANCEIRO,
    TREINO,
    PACTO_PAY,
    FACILITE_PAY,
    SISTEMA_SESC;

    public static ModuloEnum fromSigla(String siglaModulo) throws Exception {
        for (ModuloEnum value : values()) {
            if (value.name().equals(siglaModulo)) {
                return value;
            }
        }
        throw new Exception("Modulo não encontrado!");
    }
}
