package com.pacto.adm.core.util;

import com.pacto.config.dto.PaginadorDTO;
import org.springframework.util.CollectionUtils;

import javax.persistence.Query;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class QueryUtils {

    private static final String QUICK_SEARCH_PARAM_NAME = "quickSearchValue";

    private static final List<String> DATE_FORMAT_PATTERNS = Arrays.asList(
            "dd/MM/yyyy HH",
            "dd/MM/yyyy HH:mm",
            "dd/MM/yyyy HH:mm:ss",
            "dd/MM/yyyy"
    );

    private static final Map<String, SimpleDateFormat> DATE_FORMATS = new HashMap<>();

    static {
        for (String pattern : DATE_FORMAT_PATTERNS) {
            DATE_FORMATS.put(pattern, new SimpleDateFormat(pattern));
        }
    }

    public static void addQuickSearchParams(String quickSearchList, Query query) {
        addQuickSearchParams(quickSearchList, query, QUICK_SEARCH_PARAM_NAME);
    }

    public static void addQuickSearchParams(String quickSearchValue, Query query, String quickSearchParamName) {
        String value = quickSearchValue.replaceAll(" ", "%");
        query.setParameter("quickSearchValue", "%" + value + "%");
    }

    public static void buildSqlQuickSearchByType(StringBuilder sbWhere, LinkedHashMap<String, Class<?>> columnTypes) {
        buildSqlQuickSearchByType(QUICK_SEARCH_PARAM_NAME, sbWhere, columnTypes);
    }

    public static void buildSqlQuickSearchByType(String quickSearchParamName, StringBuilder sbWhere, LinkedHashMap<String, Class<?>> columnTypes) {
        if (CollectionUtils.isEmpty(columnTypes)) {
            throw new IllegalArgumentException("columns cannot be empty");
        }

        sbWhere.append(" CONCAT_WS (' ', ");

        columnTypes.keySet().forEach(col -> {
            Class<?> columnType = columnTypes.get(col);
            if (columnType == null) {
                columnType = String.class;
            }
            if (columnType == Double.class || columnType == Float.class) {
                String colRounded = "ROUND(" + col + "\\:\\:numeric, 2)";
                sbWhere.append(colRounded).append(", ");
                return;
            }
            if (Number.class.isAssignableFrom(columnType) || Boolean.class.equals(columnType)) {
                String colText = col + "\\:\\:text";
                sbWhere.append("UNACCENT(UPPER(").append(colText).append(")), ");
                return;
            }
            if (Date.class.equals(columnType)) {
                String colText = "TO_CHAR(" + col + ", 'DD/MM/YYYY HH24:MI:SS')";
                sbWhere.append(colText).append(", ");
                return;
            }
            sbWhere.append("UNACCENT(UPPER(").append(col).append(")), ");
        });
        sbWhere.delete(sbWhere.length() - 2, sbWhere.length());
        sbWhere.append(") LIKE UNACCENT(UPPER(:").append(quickSearchParamName).append("))\n");
    }

    public static void buildHqlQuickSearchByType(StringBuilder sbWhere, Map<String, Class<?>> columnTypes) {
        buildHqlQuickSearchByType(QUICK_SEARCH_PARAM_NAME, sbWhere, columnTypes);
    }

    public static void buildHqlQuickSearchByType(String quickSearchParamName, StringBuilder sbWhere, Map<String, Class<?>> columnTypes) {
        if (CollectionUtils.isEmpty(columnTypes)) {
            throw new IllegalArgumentException("columns cannot be empty");
        }

        sbWhere.append(" CONCAT_WS (' ', ");

        columnTypes.keySet().forEach(col -> {
            Class<?> columnType = columnTypes.get(col);
            if (columnType == null) {
                columnType = String.class;
            }
            if (columnType == Double.class || columnType == Float.class) {
                String colRounded = "ROUND(cast(" + col + " as numeric), 2)";
                sbWhere.append(colRounded).append(", ");
                return;
            }
            if (Number.class.isAssignableFrom(columnType) || Boolean.class.equals(columnType)) {
                String colText = col + "\\:\\:text";
                sbWhere.append("UNACCENT(UPPER(").append(colText).append(")), ");
                return;
            }
            if (Date.class.equals(columnType)) {
                String colText = "TO_CHAR(" + col + ", 'DD/MM/YYYY HH24:MI:SS')";
                sbWhere.append(colText).append(", ");
                return;
            }
            sbWhere.append("UNACCENT(UPPER(").append(col).append(")), ");
        });
        sbWhere.delete(sbWhere.length() - 2, sbWhere.length());
        sbWhere.append(") LIKE UNACCENT(UPPER(:").append(quickSearchParamName).append("))\n");
    }

    public static void setQueryPagination(Query query, PaginadorDTO paginadorDTO) {
        setQueryPagination(query, paginadorDTO, 1, 3);
    }

    public static void setQueryPagination(Query query, PaginadorDTO paginadorDTO, int page, int size) {
        if (paginadorDTO != null) {
            page = paginadorDTO.getPage().intValue();
            if (page == 0) {
                page = 1;
            }
            size = paginadorDTO.getSize().intValue();
        }

        query.setMaxResults(size);
        query.setFirstResult((page - 1) * size);
    }

}
