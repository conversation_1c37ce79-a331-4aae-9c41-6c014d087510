package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.AtestadoContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.atestado.EnvelopeRespostaAtestadoContratoDTO;
import com.pacto.adm.core.dto.enveloperesposta.contrato.*;
import com.pacto.adm.core.dto.enveloperesposta.modalidade.EnvelopeRespostaListModalidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.produto.EnvelopeRespostaListMovProdutoDTO;
import com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis.EnvelopeRespostaBoolan;
import com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis.EnvelopeRespostaInteger;
import com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis.EnvelopeRespostaListInteger;
import com.pacto.adm.core.dto.filtros.FiltroContratoJSON;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.filtros.FiltroContratoAutorizacaoJSON;
import com.pacto.adm.core.dto.filtros.FiltroContratoOperacaoJSON;
import com.pacto.adm.core.services.interfaces.*;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.config.utils.UteisValidacao;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/contratos")
public class ContratoController {

    @Autowired
    private ContratoService contratoService;
    @Autowired
    private MovPagamentoService movPagamentoService;
    @Autowired
    private MovParcelaService movParcelaService;
    @Autowired
    private MovProdutoService movProdutoService;
    @Autowired
    private ContratoOperacaoService contratoOperacaoService;
    @Autowired
    private HistoricoContratoService historicoContratoService;
    @Autowired
    private ContratoModalidadeService contratoModalidadeService;

    @Operation(
            summary = "Consultar contratos de uma pessoa",
            description = "Consulta os contratos de uma pessoa pelo código dela.",
            tags = {"Clientes"},
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da empresa que se deseja consultar os contratos", required = true, example = "5"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>vigenciaAteAjustada:</strong> Ordena pela data de vigência ajustada</li>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>situacaoContrato:</strong> Ordena pela situação do contrato</li>" +
                                    "<li><strong>responsavelContrato:</strong> Ordena pelo responsável do contrato</li>" +
                                    "<li><strong>vigenciaDe:</strong> Ordena pela data de vigência de</li>" +
                                    "<li><strong>situacao:</strong> Ordena pela situação</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação, coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pela vigência ajustada.<br/><br/>",
                            example = "vigenciaAteAjustada,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListContratoDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListContratoDTO.resposta)
                    )
            )}
    )
    @GetMapping("by-pessoa/{codPessoa}")
    public ResponseEntity<EnvelopeRespostaDTO> findByPessoa(@PathVariable Integer codPessoa,
                                                            @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(contratoService.findAllByCodPessoa(codPessoa, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar contratos de uma pessoa pelo código da matrícula dela",
            description = "Consulta os contratos de uma pessoa pelo código da matrícula dela.",
            tags = {"Clientes"},
            parameters = {
                    @Parameter(name = "codMatricula", description = "Código da empresa que se deseja consultar", required = true, example = "1"),
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca para contratos.<br/>" +
                                    "<br/><strong>Filtro disponível</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Código do contrato utilizado para filtrar registros.</li>" +
                                    "<li><strong>permiteSituacaoAtestadoContrato:</strong> Define se a situação do atestado do contrato deve ser considerada.</li>" +
                                    "<li><strong>situacoes:</strong> Lista de situações do contrato. Valores possíveis: AT (Ativo), CA (Cancelado), IN (Inativo), TR (Trabalhando).</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"123\",\"permiteSituacaoAtestadoContrato\":true,\"situacoes\":[\"AT\",\"IN\"]}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>vigenciaAteAjustada:</strong> Ordena pela data de vigência ajustada</li>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>situacaoContrato:</strong> Ordena pela situação do contrato</li>" +
                                    "<li><strong>responsavelContrato:</strong> Ordena pelo responsável do contrato</li>" +
                                    "<li><strong>vigenciaDe:</strong> Ordena pela data de vigência de</li>" +
                                    "<li><strong>situacao:</strong> Ordena pela situação</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação, coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pela vigência ajustada.<br/><br/>",
                            example = "vigenciaAteAjustada,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListContratoDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListContratoDTO.resposta)
                    )
            )}
    )
    @GetMapping("by-matricula/{codMatricula}")
    public ResponseEntity<EnvelopeRespostaDTO> findByMatricula(@PathVariable Integer codMatricula,
                                                               @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                               @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroContratoJSON filtroContratoJSON = new FiltroContratoJSON(filtros);
            return ResponseEntityFactory.ok(contratoService.findAllByCodMatricula(codMatricula, filtroContratoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar quantidade de contratos assinados de uma pessoa",
            description = "Consulta a quantidade de contratos assinados de uma pessoa pelo código da matrícula dela.",
            tags = {"Clientes"},
            parameters = {
                    @Parameter(name = "codMatricula", description = "Código da empresa que se deseja consultar", required = true, example = "1"),
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaInteger.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaInteger.resposta)
                    )
            )}
    )
    @GetMapping("qtd-contratos-assinados/{codMatricula}")
    public ResponseEntity<EnvelopeRespostaDTO> consultarQuantidadeContratosAssinados(@PathVariable Integer codMatricula) {
        try {
            return ResponseEntityFactory.ok(contratoService.consultarQuantidadeContratosAssinados(codMatricula));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Consultar um contrato",
            description = "Consulta um contrato pelo código dele.",
            tags = {"Plano"},
            parameters = {
                    @Parameter(name = "contrato", description = "Código do contrato que se deseja consultar", required = true, example = "182"),
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaContratoDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaContratoDTO.resposta)
                    )
            )}
    )
    @GetMapping("/{contrato}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer contrato) {
        try {
            return ResponseEntityFactory.ok(contratoService.findById(contrato));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar pagamentos de um contrato",
            description = "Consulta os pagamentos de um contrato pelo código dele.",
            tags = {"Plano"},
            parameters = {
                    @Parameter(name = "contrato", description = "Código do contrato que se deseja consultar", required = true, example = "1"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListMovPagamentoDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListMovPagamentoDTO.resposta)
                    )
            )}
    )
    @GetMapping("/{contrato}/pagamentos")
    public ResponseEntity<EnvelopeRespostaDTO> findAllPagamentosByContrato(@PathVariable Integer contrato,
                                                                           @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                           @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(movPagamentoService.findAllByCodContrato(contrato, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar parcelas de um contrato",
            description = "Consulta as parcelas de um contrato pelo código dele.",
            tags = {"Plano"},
            parameters = {
                    @Parameter(name = "contrato", description = "Código do contrato que se deseja consultar", required = true, example = "1"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>contrato:</strong> Ordena pelo código do contrato</li>" +
                                    "<li><strong>empresa:</strong> Ordena pelo nome da empresa</li>" +
                                    "<li><strong>descricao:</strong> Ordena pela descrição</li>" +
                                    "<li><strong>valorParcela:</strong> Ordena pelo valor da parcela</li>" +
                                    "<li><strong>situacao:</strong> Ordena pela situação</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação, coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pela data de lançamento.<br/><br/>",
                            example = "empresa,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListMovParcelaDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListMovParcelaDTO.resposta)
                    )
            )}
    )
    @GetMapping("/{contrato}/parcelas")
    public ResponseEntity<EnvelopeRespostaDTO> findAllParcelasByContrato(@PathVariable Integer contrato,
                                                                         @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                         @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(movParcelaService.findAllByCodContrato(contrato, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar produtos de um contrato",
            description = "Consulta os produtos de um contrato pelo código dele.",
            tags = {"Plano"},
            parameters = {
                    @Parameter(name = "contrato", description = "Código do contrato que se deseja consultar", required = true, example = "1"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListMovProdutoDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListMovProdutoDTO.resposta)
                    )
            )}
    )
    @GetMapping("/{contrato}/produtos")
    public ResponseEntity<EnvelopeRespostaDTO> findAllProdutosByContrato(@PathVariable Integer contrato,
                                                                         @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                         @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(movProdutoService.findAllByCodContrato(contrato, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar operações de um contrato",
            description = "Consulta os operações de um contrato pelo código dele.",
            tags = {"Plano"},
            parameters = {
                    @Parameter(name = "contrato", description = "Código do contrato que se deseja consultar", required = true, example = "1"),
                    @Parameter(name = "validarOperacoesContrato", description = "Indica se deve validar as operações do contrato. Se for true o sistema verifica se as operações podem ser estornadas, de acordo com a situação do contrato e o tipo de operação.", example = "false"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>tipoJustificativa:</strong> Ordena pelo tipo de justificativa, considerando o tipo de operação</li>" +
                                    "<li><strong>dataInicioEfetivacaoOperacao:</strong> Ordena pela data de início da efetivação da operação</li>" +
                                    "<li><strong>nrDiasOperacao:</strong> Ordena pelo número de dias da operação</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação, coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem ascendente pela data de início da efetivação da operação.<br/><br/>",
                            example = "dataInicioEfetivacaoOperacao,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListContratoOperacaoDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListContratoOperacaoDTO.resposta)
                    )
            )}
    )
    @GetMapping("/{contrato}/operacoes")
    public ResponseEntity<EnvelopeRespostaDTO> findAllContratoOperacaoByContrato(@PathVariable Integer contrato,
                                                                                 @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                                 @RequestParam(required = false) Boolean validarOperacoesContrato,
                                                                                 @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroContratoOperacaoJSON filtroContratoOperacaoJSON = new FiltroContratoOperacaoJSON(filtros);
            if (validarOperacoesContrato != null) {
                filtroContratoOperacaoJSON.setValidarOperacoesContrato(validarOperacoesContrato);
                if (UteisValidacao.emptyString(paginadorDTO.getSort())) {
                    paginadorDTO.setSort("dataInicioEfetivacaoOperacao,ASC");
                }
            }
            return ResponseEntityFactory.ok(contratoOperacaoService.findAllByContrato(contrato, filtroContratoOperacaoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar histórico de um contrato",
            description = "Consulta o histórico de um contrato pelo código dele.",
            tags = {"Plano"},
            parameters = {
                    @Parameter(name = "contrato", description = "Código do contrato que se deseja consultar", required = true, example = "1"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>descricao:</strong> Ordena pela descrição</li>" +
                                    "<li><strong>dataInicioSituacao:</strong> Ordena pela data de início da situação</li>" +
                                    "<li><strong>dataFinalSituacao:</strong> Ordena pela data final da situação</li>" +
                                    "<li><strong>responsavelRegistro:</strong> Ordena pelo nome do responsável pelo registro</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação, coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas pela data de início e final da situação.<br/><br/>",
                            example = "dataInicioSituacao,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListHistoricoContratoDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListHistoricoContratoDTO.resposta)
                    )
            )}
    )
    @GetMapping("/{contrato}/historicos")
    public ResponseEntity<EnvelopeRespostaDTO> findAllHistoricoContratoByContrato(@PathVariable Integer contrato,
                                                                                  @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                                  @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(historicoContratoService.findAllByContrato(contrato, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar modalidades de um contrato",
            description = "Consulta o modalidades de um contrato pelo código dele.",
            tags = {"Plano"},
            parameters = {
                    @Parameter(name = "contrato", description = "Código do contrato que se deseja consultar", required = true, example = "1"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação, coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pelo código identificador.<br/><br/>",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListModalidadeDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListModalidadeDTO.resposta)
                    )
            )}
    )
    @GetMapping("/{contrato}/modalidades")
    public ResponseEntity<EnvelopeRespostaDTO> findAllContratoModalidadesByContrato(@PathVariable Integer contrato,
                                                                                    @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                                    @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(contratoModalidadeService.findAllByContrato(contrato, paginadorDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar se um contrato está trancado",
            description = "Consulta se um contrato está trancado.",
            tags = {"Plano"},
            parameters = {
                    @Parameter(name = "contrato", description = "Código do contrato que se deseja consultar", required = true, example = "1"),
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaBoolan.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaBoolan.resposta)
                    )
            )}
    )
    @GetMapping("/{contrato}/verificar-trancamento")
    public ResponseEntity<EnvelopeRespostaDTO> findAllContratoModalidadesByContrato(@PathVariable Integer contrato) {
        try {
            return ResponseEntityFactory.ok(contratoOperacaoService.validarSeExisteTrancamentoSemRetorno(contrato, true));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Criar ou atualizar o atestado de um contrato",
            description = "Cria ou atualiza o atestado de um contrato",
            tags = {"Plano"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar ou atualizar um atestado vinculado a um contrato.<br/>" +
                            "Se for criar, envie a requisição sem o atributo código.<br/>" +
                            "Se for atualizar, envie a requisição com o atribbuto código contendo o código do item que se deseja atualizar.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AtestadoContratoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaAtestadoContratoDTO.requestBody)}
                    )),
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaAtestadoContratoDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaAtestadoContratoDTO.resposta)
                    )
            )}
    )
    @PostMapping("/operacoes/atestados")
    public ResponseEntity<EnvelopeRespostaDTO> salvarOuAtualizarAtestadoContrato(@RequestBody AtestadoContratoDTO atestadoContratoDTO) {
        try {
            return ResponseEntityFactory.ok(contratoOperacaoService.salvarOuAtualizarAtestadoContrato(atestadoContratoDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Deletar uma operação vinculada a um contrato",
            description = "Deleta uma operação vinculada a um contrato pelo código da operação",
            tags = {"Plano"},
            parameters = @Parameter(name = "codigoContratoOperacao", description = "Código da operação que será excluída", example = "2324"),
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.")}
    )
    @DeleteMapping("/operacoes/{codigoContratoOperacao}")
    public ResponseEntity<EnvelopeRespostaDTO> estornarContratoOperacao(@PathVariable Integer codigoContratoOperacao) {
        try {
            contratoOperacaoService.estornarContratoOperacao(codigoContratoOperacao);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Gerar o período de retorno para um atestado",
            description = "Gera o período de retorno para um atestado.",
            tags = {"Plano"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para gerar o período de retorno de um atestado vinculado",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AtestadoContratoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaAtestadoContratoDTO.requestBody)}
                    )
            ),
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaAtestadoContratoDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaAtestadoContratoDTO.resposta)
                    )
            )}
    )
    @PostMapping("/periodo-retorno-atestado")
    public ResponseEntity<EnvelopeRespostaDTO> gerarPeriodoRetornoAtestado(@RequestBody AtestadoContratoDTO atestadoContrato) {
        try {
            return ResponseEntityFactory.ok(contratoOperacaoService.gerarPeriodoRetornoAtestado(atestadoContrato));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Consultar dependentes de um contrato pelo código de um cliente",
            description = "Consulta os dependentes de um contrato pelo código de matrícula do cliente.",
            tags = {"Clientes"},
            parameters = {
                    @Parameter(name = "codMatricula", description = "Código da matrícula do cliente que se deseja consultar", required = true, example = "432"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>dataFinalAjustada:</strong> Ordena pela data final ajustada</li>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>dataInicio:</strong> Ordena pela data de início</li>" +
                                    "<li><strong>dataFinal:</strong> Ordena pela data final</li>" +
                                    "<li><strong>posicaoDependente:</strong> Ordena pela posição do dependente</li>" +
                                    "<li><strong>titular:</strong> Ordena pelo titular</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação, coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pela data final ajustada.<br/><br/>",
                            example = "dataFinalAjustada,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListContratoDependenteDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListContratoDependenteDTO.resposta)
                    )
            )}
    )
    @GetMapping("/dependente/by-matricula/{codMatricula}")
    public ResponseEntity<EnvelopeRespostaDTO> findAllContratoDependenteByCliente(@PathVariable Integer codMatricula,
                                                                                  @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                                  @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(contratoService.findAllContratoDependente(codMatricula, null, null, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar dependentes pelo código do contrato",
            description = "Consulta os dependentes pelo código do contrato.",
            tags = {"Plano"},
            parameters = {
                    @Parameter(name = "codContrato", description = "Código do contrato que se deseja consultar os dependentes", required = true, example = "1361"),
                    @Parameter(name = "somenteComCliente", description = "Indica se deve retornar apenas contratos com clientes", example = "false"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>dataFinalAjustada:</strong> Ordena pela data final ajustada</li>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>dataInicio:</strong> Ordena pela data de início</li>" +
                                    "<li><strong>dataFinal:</strong> Ordena pela data final</li>" +
                                    "<li><strong>posicaoDependente:</strong> Ordena pela posição do dependente</li>" +
                                    "<li><strong>titular:</strong> Ordena pelo titular</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação, coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pela data final ajustada.<br/><br/>",
                            example = "dataFinalAjustada,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListContratoDependenteDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListContratoDependenteDTO.resposta)
                    )
            )}
    )
    @GetMapping("/dependente/by-contrato/{codContrato}")
    public ResponseEntity<EnvelopeRespostaDTO> findAllContratoDependenteByContrato(@PathVariable Integer codContrato,
                                                                                   @RequestParam(value = "somenteComCliente", required = false) Boolean somenteComCliente,
                                                                                   @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                                   @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(contratoService.findAllContratoDependente(null, codContrato, somenteComCliente, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar afatasmentos de um contrato",
            description = "Consulta os afastamentos de um contrato pelo código do contrato dependente.",
            tags = {"Plano"},
            parameters = {
                    @Parameter(name = "codContratoDependente", description = "Código do contrato dependente", required = true, example = "432"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>inicioAfastamento:</strong> Ordena pela data de início do afastamento</li>" +
                                    "<li><strong>finalAfastamento:</strong> Ordena pela data final do afastamento</li>" +
                                    "<li><strong>nrDiasSomar:</strong> Ordena pelo número de dias a somar</li>" +
                                    "<li><strong>dataRegistro:</strong> Ordena pela data de registro</li>" +
                                    "<li><strong>titular:</strong> Ordena pelo titular</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação, coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pelo código identificador.<br/><br/>",
                            example = "inicioAfastamento,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaAfastamentoContratoDependenteDTO.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaAfastamentoContratoDependenteDTO.resposta)
                    )
            )}
    )
    @GetMapping("/dependente/{codContratoDependente}/afastamentos")
    public ResponseEntity<EnvelopeRespostaDTO> findAllAfastamentoContratoDependenteByContratoDependente(@PathVariable Integer codContratoDependente,
                                                                                                        @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                                                        @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(contratoService.findAllAfastamentoContratoDependenteByContratoDependente(codContratoDependente, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar contratos não assinados",
            description = "Consulta os códigos dos contratos não assinados de um cliente pelo código da matrícula dele.",
            tags = {"Plano"},
           parameters = {
                   @Parameter(name = "codMatricula", description = "Código da matrícula da pessoa que será consultada", required = true, example = "432"),
           },
            responses = {@ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListInteger.class),
                            examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListInteger.resposta)
                    )
            )}
    )

    @PostMapping("atualizar/{codMatricula}")
    public ResponseEntity<EnvelopeRespostaDTO> atualizarContratosNaoAssinados(@PathVariable Integer codMatricula) {
        try {
            return ResponseEntityFactory.ok(contratoService.consultarNaoContratosAssinados(codMatricula));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @GetMapping("/data-base-alterada")
    public ResponseEntity<EnvelopeRespostaDTO> contratosDataBaseAlterada(
            @RequestParam(value = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {

            FiltroBIControleOperacoesJSON filtroContratoJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroContratoJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }

            return ResponseEntityFactory.ok(
                    contratoService.contratosComDataBaseAlterada(filtroContratoJSON, paginadorDTO), paginadorDTO
            );
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping("/bolsa")
    public ResponseEntity<EnvelopeRespostaDTO> contratosBolsa(
            @RequestParam(value = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {

            FiltroBIControleOperacoesJSON filtroContratoJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroContratoJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }

            return ResponseEntityFactory.ok(
                    contratoService.findContratoBolsa(filtroContratoJSON, paginadorDTO), paginadorDTO
            );
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping("/com-autorizacao")
    public ResponseEntity<EnvelopeRespostaDTO> comAutorizacaoRenovavel(
            @RequestParam(value = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {

            FiltroBIControleOperacoesJSON filtroContratoJSON = new FiltroBIControleOperacoesJSON();
            JSONObject jsonObject = new JSONObject(filtros);
            if (filtros != null) {
                filtroContratoJSON = new FiltroBIControleOperacoesJSON(jsonObject);
            }
            FiltroContratoAutorizacaoJSON filtroContratoAutorizacaoJSON = new FiltroContratoAutorizacaoJSON(filtroContratoJSON);
            if (filtros != null) {
                filtroContratoAutorizacaoJSON.setComAutorizacaoRenovavel(jsonObject.optBoolean("comAutorizacaoRenovavel"));
            }

            return ResponseEntityFactory.ok(
                    contratoService.findAllContratosAutorizacao(filtroContratoAutorizacaoJSON, paginadorDTO), paginadorDTO
            );
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
