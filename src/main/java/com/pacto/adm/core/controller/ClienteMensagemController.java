package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.ClienteMensagemDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.mensagem.EnvelopeRespostaClienteMensagemDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.mensagem.EnvelopeRespostaListClienteMensagemDTO;
import com.pacto.adm.core.dto.filtros.FiltroClienteMensagemJSON;
import com.pacto.adm.core.enumerador.TiposMensagensEnum;
import com.pacto.adm.core.services.interfaces.ClienteMensagemService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.config.utils.UteisValidacao;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/cliente-mensagem")
@Tag(name = "Cliente Mensagem")
public class ClienteMensagemController {

    @Autowired
    private ClienteMensagemService clienteMensagemService;


    @Operation(
            summary = "Consultar mensagens enviadas para uma pessoa",
            description = "Consulta e lista as mensagens enviadas para uma pessoa pelo código dela.",
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa que recebeu as mensagens", required = true, example = "5"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as resposta por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>mensagem:</strong> Ordena pelo conteúdo da mensagem</li>" +
                                    "<li><strong>tipoMensagem:</strong> Ordena pelo tipo da mensagem: NENHUM, AA (Aviso Consultor), " +
                                    "AC (Catraca), AM (Aviso Médico), BP (Boletim), DI (Dados Incompletos), PA (Parcela Atraso), RI (Risco)" +
                                    "OC (Objetivo Curto), OB (Observação), OP (Observação Cliente), PV (Produto Vencido), CV (Cartão de crédito vencido)" +
                                    "PV (Aluguel de ármario vencido e que não foi devolvido a chave)</li>" +
                                    "<li><strong>dataRegistro:</strong> Ordena pela data de registro da mensagem.</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pelo código identificador<br/><br/>",
                            example = "dataRegistro,desc",
                            schema = @Schema(implementation = String.class))
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListClienteMensagemDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListClienteMensagemDTO.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("by-pessoa/{codPessoa}")
    public ResponseEntity<EnvelopeRespostaDTO> findByPessoa(@PathVariable Integer codPessoa, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(clienteMensagemService.findAllByCodPessoa(codPessoa, null, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar mensagens de aviso do consultor",
            description = "Consulta as mensagens de aviso do consultor",
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa que recebeu as mensagens", required = true, example = "5"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as resposta por um dos atributos disponíveis na resposta<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código identificador</li>" +
                                    "<li><strong>mensagem:</strong> Ordena pelo conteúdo da mensagem</li>" +
                                    "<li><strong>tipoMensagem:</strong> Ordena pelo tipo da mensagem: NENHUM, AA (Aviso Consultor), " +
                                    "AC (Catraca), AM (Aviso Médico), BP (Boletim), DI (Dados Incompletos), PA (Parcela Atraso), RI (Risco)" +
                                    "OC (Objetivo Curto), OB (Observação), OP (Observação Cliente), PV (Produto Vencido), CV (Cartão de crédito vencido)" +
                                    "PV (Aluguel de ármario vencido e que não foi devolvido a chave)</li>" +
                                    "<li><strong>dataRegistro:</strong> Ordena pela data de registro da mensagem.</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pelo código identificador<br/><br/>",
                            example = "dataRegistro,desc"
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListClienteMensagemDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListClienteMensagemDTO.resposta)}
                            )
                    ),
                   
            }
    )
    @GetMapping("by-pessoa/{codPessoa}/consultor")
    public ResponseEntity<EnvelopeRespostaDTO> findByPessoaMensagemConsultor(@PathVariable Integer codPessoa, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroClienteMensagemJSON filtrJSON = new FiltroClienteMensagemJSON(null);
            filtrJSON.setTipoMensagem(TiposMensagensEnum.AVISO_CONSULTOR.getSigla());
            List<ClienteMensagemDTO> lista = clienteMensagemService.findAllByCodPessoa(codPessoa, filtrJSON, paginadorDTO);
            ClienteMensagemDTO clienteMensagemDTO = UteisValidacao.emptyList(lista) ? null : lista.get(0);

            return ResponseEntityFactory.ok(clienteMensagemDTO != null ? clienteMensagemDTO : new ClienteMensagemDTO());

        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar uma mensagem",
            description = "Consulta uma mensagem através do código dela",
            parameters = {
                    @Parameter(name = "codigo", description = "Código da mensagem", required = true, example = "5"),
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaClienteMensagemDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaClienteMensagemDTO.resposta)}
                            )
                    ),
                   
            }
    )
    @GetMapping("/{codigo}")
    public ResponseEntity<EnvelopeRespostaDTO> obter(@PathVariable Integer codigo) {
        try {
            return ResponseEntityFactory.ok(clienteMensagemService.obter(codigo));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Salvar ou atualizar uma mensagem",
            description = "Salva ou atualiza uma mensagem." +
                    "<br/> Para salvar uma nova mensagem envie a requisição sem o atributo código." +
                    "<br/>Para atualizar uma mensagem envie a requisição com o atributo código contendo o código da mensagem que se deseja atualizar.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar ou atualizar.<br/>" +
                            "Se for criar um novo registro, envie a requisição sem o atributo código.<br/>" +
                            "Se for atualizar um registro, envie a requisição com o atribbuto código contendo o valor do registro que será atualizado.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = EnvelopeRespostaClienteMensagemDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaClienteMensagemDTO.requestBody)}
                    )),
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaClienteMensagemDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaClienteMensagemDTO.resposta)}
                            )
                    ),
                   
            }
    )
    @PostMapping()
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@RequestBody ClienteMensagemDTO dto) {
        try {

            return ResponseEntityFactory.ok(clienteMensagemService.saveOrUpdate(dto));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Deletar uma mensagem",
            description = "Deleta uma mensagem",
            parameters = {
                    @Parameter(name = "codigo", description = "Código da mensagem que se deseja deletar", required = true, example = "44"),
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida."
                    ),
            }
    )
    @DeleteMapping("/{codigo}")
    public ResponseEntity<EnvelopeRespostaDTO> excluir(@PathVariable Integer codigo) {
        try {
            clienteMensagemService.excluir(codigo);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }
}
