package com.pacto.adm.core.controller.metafinanceiraempresa;

import com.pacto.adm.core.adapters.metafinanceiraempresa.MetaFinanceiraEmpresaAdapter;
import com.pacto.adm.core.dto.filtros.metafinanceiraempresa.FiltroMetaFinanceiraEmpresaJSON;
import com.pacto.adm.core.services.interfaces.metafinanceiraempresa.MetaFinanceiraEmpresaService;
import com.pacto.adm.core.swagger.respostas.metafinanceiraempresa.ExemploRespostaListMetaFinanceiraEmpresaPaginacao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/meta-financeira")
@Tag(name = "BI Administrativo")
public class MetaFinanceiraEmpresaController {

    private final MetaFinanceiraEmpresaService metaFinanceiraEmpresaService;
    private final MetaFinanceiraEmpresaAdapter metaFinanceiraEmpresaAdapter;

    public MetaFinanceiraEmpresaController(MetaFinanceiraEmpresaService metaFinanceiraEmpresaService, MetaFinanceiraEmpresaAdapter metaFinanceiraEmpresaAdapter) {
        this.metaFinanceiraEmpresaService = metaFinanceiraEmpresaService;
        this.metaFinanceiraEmpresaAdapter = metaFinanceiraEmpresaAdapter;
    }

    @Operation(
            summary = "Consultar metas financeiras por empresa, mês e ano",
            description = "Consulta as metas financeiras estabelecidas para empresas filtradas por mês, ano e lista de empresas específicas.",
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação:**\n" +
                                    "- **codigo**: Código da meta financeira\n" +
                                    "- **descricao**: Descrição da meta financeira\n" +
                                    "- **mes**: Mês de referência\n" +
                                    "- **ano**: Ano de referência",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **mes**: Filtra pelo mês de referência da meta financeira (1-12)\n" +
                                    "- **ano**: Filtra pelo ano de referência da meta financeira\n" +
                                    "- **empresas**: Lista de códigos das empresas para filtrar as metas financeiras",
                            example = "{\"mes\":10,\"ano\":2024,\"empresas\":[1,2,3]}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListMetaFinanceiraEmpresaPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping("/empresa-mes-ano")
    public ResponseEntity<EnvelopeRespostaDTO> findAllByEmpresaMesAno(
            @RequestParam(required = false) String filters,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        return ResponseEntityFactory.ok(
                metaFinanceiraEmpresaAdapter.toDtos(
                        metaFinanceiraEmpresaService.findByEmpresaIdAndMesAndAno(
                            new FiltroMetaFinanceiraEmpresaJSON(filters), paginadorDTO
                        )
                ), paginadorDTO
        );
    }


}
