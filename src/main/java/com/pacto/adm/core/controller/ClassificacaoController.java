package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.categoria.EnvelopeRespostaListCategoria;
import com.pacto.adm.core.dto.enveloperesposta.classificacao.EnvelopeRespostaClassificacao;
import com.pacto.adm.core.dto.enveloperesposta.classificacao.EnvelopeRespostaListClassificacao;
import com.pacto.adm.core.services.interfaces.ClassificacaoService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/classificacao")
@Tag(name = "Classificação")
public class ClassificacaoController {

    @Autowired
    private ClassificacaoService classificacaoService;


    @Operation(
            summary = "Consultar classificações",
            description = "Lista todas as classificações",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListClassificacao.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListClassificacao.resposta)}
                            )),
                   
            }
    )
    @GetMapping("")
    public ResponseEntity<EnvelopeRespostaDTO> findAll() {
        try {
            return ResponseEntityFactory.ok(classificacaoService.findAll());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
