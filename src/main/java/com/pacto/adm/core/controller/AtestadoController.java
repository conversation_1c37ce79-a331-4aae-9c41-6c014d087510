package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.AtestadoDTO;
import com.pacto.adm.core.dto.enveloperesposta.atestado.EnvelopeRespostaAtestadoDTO;
import com.pacto.adm.core.dto.enveloperesposta.atestado.EnvelopeRespostaListAtestadoDTO;
import com.pacto.adm.core.services.interfaces.AtestadoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/atestados")
@Tag(name = "Clientes")
public class AtestadoController {

    @Autowired
    private AtestadoService atestadoService;

    @Operation(
            summary = "Consultar os atestados de aptidão física de uma pessoa pelo código da matrícula",
            description = "Retorna todos os atestados de aptidão física de uma pessoa buscando pelo código da matrícula dela",
            parameters = {
                    @Parameter(name = "matricula", description = "Código da matrícula", example = "2"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class))
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListAtestadoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListAtestadoDTO.resposta)}
                            ))
            }
    )
    @GetMapping("/{matricula}")
    public ResponseEntity<EnvelopeRespostaDTO> findAllByMatricula(@PathVariable Integer matricula, @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(atestadoService.findAllByMatricula(matricula, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Cadastrar ou atualizar atestado de aptidão física de uma pessoa",
            description = "Cadastra ou atualiza o atestado de aptidão física de uma pessoa através do código da matrícula dela<br/><br/>" +
                    "Para cadastrar, envie a requisição do body sem o código do atestado. Para atualizar, envie a requisição do body com o código do atestado",
            parameters = {
                    @Parameter(name = "matricula", description = "Código da matrícula", example = "2"),
                    @Parameter(name = "empresaId", description = "Código identificador da empresa", required = true, example = "1", in = ParameterIn.HEADER)
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar ou atualizar.<br/>" +
                            "Se for criar um novo registro, envie a requisição sem o atributo código.<br/>" +
                            "Se for atualizar um registro, envie a requisição com o atribbuto código contendo o valor do registro que será atualizado.",
                    content = {@Content(schema = @Schema(implementation = EnvelopeRespostaAtestadoDTO.class), examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaAtestadoDTO.requestBody)})},
                    useParameterTypeSchema = true
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaAtestadoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaAtestadoDTO.resposta)}
                            )),
            }
    )
    @PostMapping("/{matricula}")
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@RequestBody AtestadoDTO atestadoDTO, @PathVariable Integer matricula) {
        try {
            return ResponseEntityFactory.ok(atestadoService.saveOrUpdate(atestadoDTO, matricula));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Excluir atestado de aptidão física",
            description = "Exclui um atestado de aptidão física pelo código dele",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(examples = @ExampleObject(name = "Exemplo Resposta 200 (Não retorna corpo de resposta", value = ""))
                    )
            }
    )
    @DeleteMapping("/{codigoAtestado}")
    public ResponseEntity<EnvelopeRespostaDTO> excluirAtestado(
            @Parameter(description = "Código do atestado que será excluído")
            @PathVariable Integer codigoAtestado
    ) {
        try {
            atestadoService.excluirAtestado(codigoAtestado);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }
}
