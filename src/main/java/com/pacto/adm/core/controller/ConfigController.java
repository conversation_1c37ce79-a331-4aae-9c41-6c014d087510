package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.statushttp.*;
import com.pacto.adm.core.dto.enveloperesposta.vacina.EnvelopeRespostaListComprovanteVacinaDTO;
import com.pacto.adm.core.services.interfaces.DiscoveryService;
import com.pacto.config.datasource.oamd.OAMDCompanyFactory;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/config")
@Tag(name = "Configurações ADM")
public class ConfigController {

    @Autowired
    private DiscoveryService discoveryService;
    @Autowired
    private OAMDCompanyFactory oamdCompanyFactory;


    @Operation(
            summary = "Recarregar módulo administrador",
            description = "Recarrega todo o módulo administrador limpando o cache do sistema e recarregando as conexões com os outros sistemas.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListComprovanteVacinaDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = "")
                            )
                    )
            }
    )
    @ResponseBody
    @GetMapping("reload")
    public ResponseEntity<EnvelopeRespostaDTO> reload() {
        discoveryService.clearCache();
        oamdCompanyFactory.loadConnectionsDatasources();
        return ResponseEntityFactory.ok();
    }


    @Operation(
            summary = "Consultar o cache do discovery",
            description = "Consulta o cache do discovery.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200",
                                            value = "{ \"content\":\"cache\"}")
                            )
                    )
            }
    )
    @ResponseBody
    @GetMapping("discovery/cache")
    public ResponseEntity<EnvelopeRespostaDTO> discoveryCache() {
        return ResponseEntityFactory.ok(discoveryService.getDiscoveryCache());
    }

}
