package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.ClienteRedeEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.observacao.EnvelopeRespostaClienteObservacaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.redeempresa.EnvelopeRespostaClienteRedeEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.redeempresa.EnvelopeRespostaListClienteRedeEmpresaDTO;
import com.pacto.adm.core.services.interfaces.ClienteRedeEmpresaService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/cliente-rede-empresa")
@Tag(name = "Clientes")
public class ClienteRedeEmpresaController {

    private final ClienteRedeEmpresaService clienteRedeEmpresaService;

    public ClienteRedeEmpresaController(ClienteRedeEmpresaService clienteRedeEmpresaService) {
        this.clienteRedeEmpresaService = clienteRedeEmpresaService;
    }

    @Operation(
            summary = "Consultar os vínculos de um cliente as redes de academia",
            description = "Consulta o vínculo de um cliente as empresas da rede de academias pelo CPF dele.",
            parameters = {
                    @Parameter(name = "cpf", description = "CPF do cliente que será consultado", required = true, example = "123.456.789-10"),
            },
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListClienteRedeEmpresaDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListClienteRedeEmpresaDTO.resposta)}
                            )
                    ),
            }
    )
    @GetMapping
    public ResponseEntity<EnvelopeRespostaDTO> findByCpf(@RequestParam String cpf) {
        try {
            return ResponseEntityFactory.ok(clienteRedeEmpresaService.findByCpf(cpf));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Criar ou atualizar vínculo do cliente a uma academia da rede",
            description = "Cria ou atualiza o vínculo de um cliente a uma academia da rede.<br/>" +
                    "Para criar, envie o corpo da requisição sem o código.<br/>" +
                    "Para atualizar, envie o corpo da requisição com o código que se deseja atualizar.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar ou atualizar.<br/>" +
                            "Se for criar um novo registro, envie a requisição sem o atributo código.<br/>" +
                            "Se for atualizar um registro, envie a requisição com o atribbuto código contendo o valor do registro que será atualizado.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ClienteRedeEmpresaDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaClienteRedeEmpresaDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200",
                            description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaClienteObservacaoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaClienteRedeEmpresaDTO.resposta)}
                            )
                    ),
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@RequestBody ClienteRedeEmpresaDTO clienteRedeEmpresaDTO) {
        try {
            return ResponseEntityFactory.ok(clienteRedeEmpresaService.saveOrUpdate(clienteRedeEmpresaDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }
}
