package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.acesso.EnvelopeRespostaClienteInfoAcessosDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.EnvelopeRespostaListAcessoClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis.EnvelopeRespostaListInteger;
import com.pacto.adm.core.services.interfaces.AcessoClienteService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/acessos-cliente")
public class AcessoClienteController {

    @Autowired
    private AcessoClienteService acessoClienteService;

    @Operation(
            summary = "Consultar os registros de acesso de uma pessoa à academia",
            description = "Retorna os detalhes do acesso de uma pessoa à academia, como data do acesso, data de saída" +
                    "local que foi feito o acesso, etc.",
            tags = {"Lista de Acessos"},
            parameters = {
                    @Parameter(name = "codPessoa", required = true, description = "Código da pessoa que será usado na consulta", example = "5"),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca",
                            example = "{\"filtro\":\"valorDoFiltro\"}",
                            hidden = true,
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas<br/>" +
                                    "<br/><strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>dHrEntrada</strong>: Ordena as respostas pela 'data hora' de entrada<br/></li>" +
                                    "<li><strong>codigo:</strong> Ordena as respostas pelos códigos identificadores<br/></li>" +
                                    "<li><strong>dtHrSaida:</strong> Ordena as respostas pela 'data hora' de saída<br/></li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para poder fazer a ordenação coloque seguindo o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas em ordem decrescente pela 'data hora' de entrada<br/><br/>",
                            example = "dtHrEntrada,asc", schema = @Schema(implementation = String.class)),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida!",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListAcessoClienteDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListAcessoClienteDTO.resposta)}
                            ))
            }
    )
    @GetMapping("by-pessoa/{codPessoa}")
    public ResponseEntity<EnvelopeRespostaDTO> findByPessoa(@PathVariable Integer codPessoa,
                                                            @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                            PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(acessoClienteService.findAllByCodPessoa(codPessoa, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar os detalhes e estatísticas dos acesso de uma pessoa à academia",
            description = "Retorna a data do último acesso da pessoa à academia, a média de acessos nas últimas semanas e a média de acesso nos últimos meses",
            tags = {"Lista de Acessos"},
            parameters = {@Parameter(name = "codPessoa", required = true, description = "Código da pessoa que será usado na consulta", example = "5")},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida!",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaClienteInfoAcessosDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaClienteInfoAcessosDTO.resposta)}
                            )
                    )
            }
    )
    @GetMapping("{codPessoa}/info-acessos")
    public ResponseEntity<EnvelopeRespostaDTO> infoAcessos(@PathVariable Integer codPessoa) {
        try {
            return ResponseEntityFactory.ok(acessoClienteService.obterInfo(codPessoa));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar a quantidade de acessos de uma pessoa à academia nos últimos meses",
            description = "Retorna a quantidade de acessos de uma pessoa à academia nos últimos quatro meses à partir de uma data, separado pelos acessos por cada um dos meses",
            tags = {"Lista de Acessos"},
            parameters = {
                    @Parameter(name = "codPessoa", required = true, description = "Código da pessoa que será usado na consulta", example = "5"),
                    @Parameter(name = "data", required = false, description = "Data que será usada para a consulta, não é obrigatória", example = "2025-09-29")
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida!",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListInteger.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListInteger.resposta)}
                            )
                    ),
            }
    )
    @GetMapping("{codPessoa}/ultimos-meses")
    public ResponseEntity<EnvelopeRespostaDTO> ultimosMeses(@PathVariable Integer codPessoa, @RequestParam(value = "data", required = false) String data) {
        try {
            return ResponseEntityFactory.ok(acessoClienteService.ultimosMeses(codPessoa, data));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiResponses(value = {

    })
    @Operation(
            summary = "Consultar a quantidade de acessos de uma pessoa à academia nas últimas semanas",
            description = "Retorna a quantidade de acessos de uma pessoa à academia nas últimas quatro semanas à partir de uma data, separado pelos acessos por cada uma das semanadas",
            tags = {"Lista de Acessos"},
            parameters = {
                    @Parameter(name = "codPessoa", required = true, description = "Código da pessoa que será usado na consulta", example = "5"),
                    @Parameter(name = "data", required = false, description = "Data que será usada para a consulta, não é obrigatória", example = "2025-09-29")
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida!",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListInteger.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListInteger.resposta)}
                            )
                    )
            }
    )
    @GetMapping("{codPessoa}/ultimas-semanas")
    public ResponseEntity<EnvelopeRespostaDTO> ultimasSemanas(
             @PathVariable Integer codPessoa,
            @RequestParam(value = "data", required = false) String data) {
        try {
            return ResponseEntityFactory.ok(acessoClienteService.ultimasSemanas(codPessoa, data));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
