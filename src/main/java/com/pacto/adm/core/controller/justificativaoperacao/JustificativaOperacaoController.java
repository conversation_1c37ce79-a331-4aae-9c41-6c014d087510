package com.pacto.adm.core.controller.justificativaoperacao;

import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.services.interfaces.justificativaoperacao.JustificativaOperacaoService;
import com.pacto.adm.core.swagger.respostas.justificativaoperacao.ExemploRespostaListJustificativaOperacaoPaginacao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/justificativa-operacao")
public class JustificativaOperacaoController {

    private final JustificativaOperacaoService justificativaOperacaoService;

    public JustificativaOperacaoController(JustificativaOperacaoService justificativaOperacaoService) {
        this.justificativaOperacaoService = justificativaOperacaoService;
    }

    @Operation(
            summary = "Consultar contratos cancelados transferidos",
            description = "Consulta justificativas de operações de contratos cancelados que foram transferidos para outro aluno.",
            tags = {"BI Administrativo"},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **descricao**: Ordena pela descrição da justificativa",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quickSearchValue**: Filtra pela descrição da justificativa de operação<br/>" +
                                    "- **empresa**: Filtra pelo código da empresa",
                            example = "{\"quickSearchValue\":\"CT.12345.CA\",\"empresa\":1}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListJustificativaOperacaoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping("/contratos-cancelados-transferidos")
    public ResponseEntity<EnvelopeRespostaDTO> consultarContratosCancelados(
            @RequestParam(value = "filters", required = false) String filtros,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            return ResponseEntityFactory.ok(justificativaOperacaoService.contratosCanceladosTransferidosOutroAluno(filtroBIControleOperacoesJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
