package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.log.EnvelopeRespostaListLogDTO;
import com.pacto.adm.core.dto.enveloperesposta.modalidade.EnvelopeRespostaListModalidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.modalidade.EnvelopeRespostaModalidadeDTO;
import com.pacto.adm.core.dto.enveloperesposta.modalidade.EnvelopeRespostaModalidadeExcluida;
import com.pacto.adm.core.dto.enveloperesposta.modalidade.EnvelopeRespostaModalidadeRequestDTO;
import com.pacto.adm.core.dto.filtros.FiltroLogClienteJSON;
import com.pacto.adm.core.dto.filtros.FiltroModalidadeJSON;
import com.pacto.adm.core.dto.modalidade.ModalidadeRequestDTO;
import com.pacto.adm.core.services.interfaces.ModalidadeService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/modalidade")
public class ModalidadeController {

    @Autowired
    private ModalidadeService modalidadeService;

    @Operation(
            summary = "Consultar as modalidades de um contrato",
            description = "Consulta todas as modalidades de um contrato pelo código dele.",
            tags = {"Plano"},
            parameters = {
                    @Parameter(name = "codContrato", description = "Código do contrato que será consultado as modalidades", example = "1213")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListModalidadeDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListModalidadeDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("by-contrato/{codContrato}")
    public ResponseEntity<EnvelopeRespostaDTO> findAllByCodContrato(@PathVariable Integer codContrato) {
        try {
            return ResponseEntityFactory.ok(modalidadeService.findAllByContrato(codContrato));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar uma modalidade",
            description = "Consulta as informações de uma modalidade buscando pelo código dela.",
            tags = {"Modalidade"},
            parameters = {
                    @Parameter(name = "id", description = "Código da modalidade que será consultada", example = "22")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaModalidadeDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaModalidadeDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(modalidadeService.findById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar todas as modalidades",
            description = "Consulta as informações de todas as modalidades podendo aplicar filtros de busca.",
            tags = {"Modalidade"},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca para o recurso.<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Texto usado para buscar pelo nome. Caso o valor seja numérico, também busca por <code>codigo</code> e <code>nrVezes</code> com correspondência exata.</li>" +
                                    "<li><strong>situacao:</strong> Define o status de atividade. Valores possíveis: <ul>" +
                                    "<li><strong>A</strong> = Ativo</li>" +
                                    "<li><strong>I</strong> = Inativo</li>" +
                                    "</ul></li>" +
                                    "<li><strong>empresa:</strong> Código da empresa para filtrar resultados. Se não informado, assume o ID da empresa logada.</li>" +
                                    "<li><strong>utilizarTurma:</strong> Indica se o recurso utiliza turma. Valores possíveis: <ul>" +
                                    "<li><strong>A</strong> = Ativo (utiliza turma)</li>" +
                                    "<li><strong>I</strong> = Inativo (não utiliza turma)</li>" +
                                    "</ul></li>" +
                                    "</ul>" +
                                    "<br/><strong>Observações</strong>" +
                                    "<ul>" +
                                    "<li>O filtro <code>quicksearchValue</code> é multiuso: busca por nome (texto), ou por código/número de vezes se o valor informado for numérico.</li>" +
                                    "<li>Se <code>empresa</code> não for enviada, o sistema usa automaticamente a empresa da sessão/logada.</li>" +
                                    "</ul>",
                            example = "{" +
                                    "\"quicksearchValue\": \"Academia X\", " +
                                    "\"situacao\": \"A\", " +
                                    "\"empresa\": 5, " +
                                    "\"utilizarTurma\": \"I\"" +
                                    "}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas pelos atributos disponíveis.<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código</li>" +
                                    "<li><strong>nome:</strong> Ordena pelo nome</li>" +
                                    "<li><strong>nrVezes:</strong> Ordena pelo número de vezes</li>" +
                                    "<li><strong>ativo:</strong> Ordena pelo status ativo</li>" +
                                    "<li><strong>empresa:</strong> Ordena pelo código da empresa</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para realizar a ordenação, utilize o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas por <strong>codigo desc</strong>.<br/><br/>",
                            example = "nome,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListModalidadeDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListModalidadeDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) String filters,
                                                       @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            JSONObject filtros = filters != null ? new JSONObject(filters) : null;
            FiltroModalidadeJSON filtroModalidadeJSON = new FiltroModalidadeJSON(filtros);
            return ResponseEntityFactory.ok(modalidadeService.findAllModalidade(filtroModalidadeJSON, paginadorDTO), paginadorDTO);
        } catch (JSONException e) {
            return ResponseEntityFactory.mensagemFront("invalid.filters", "Os filtros fornecidos são inválidos.");
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Cadastrar ou atualizar uma modalidade",
            description = "Cadastra ou atualiza uma modalidade.",
            tags = {"Modalidade"},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar ou atualizar.<br/>" +
                            "Se for criar um novo registro, envie a requisição sem o atributo código.<br/>" +
                            "Se for atualizar um registro, envie a requisição com o atribbuto código contendo o valor do registro que será atualizado.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ModalidadeRequestDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaModalidadeRequestDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaModalidadeDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaModalidadeDTO.resposta)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@RequestBody ModalidadeRequestDTO modalidadeRequestDTO) {
        try {
            return ResponseEntityFactory.ok(modalidadeService.saveOrUpdate(modalidadeRequestDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] stackTrace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : stackTrace) {
                result.append(stackTraceElement.toString() + "/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Excluir uma modalidade",
            description = "Exclui uma modalidade pelo código dela.",
            tags = {"Modalidade"},
            parameters = {
                    @Parameter(name = "id", description = "Código da modalidade que será excluída", example = "22")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaModalidadeExcluida.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaModalidadeExcluida.resposta)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> delete(@PathVariable Integer id) {
        try {
            modalidadeService.delete(id);
            return ResponseEntityFactory.ok("Modalidade excluída!");
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
