package com.pacto.adm.core.controller;

import com.pacto.adm.core.services.interfaces.GymPassService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/gympass")
public class GymPassController {

    private GymPassService gymPassService;

//    @GetMapping("by-cliente/{codCliente}")
//    public ResponseEntity<EnvelopeRespostaDTO> findAllByMatricula(@PathVariable Integer codCliente, PaginadorDTO paginadorDTO) {
//        try {
//            return ResponseEntityFactory.ok(gymPassService.findAllByCliente(codCliente, paginadorDTO), paginadorDTO);
//        } catch (ServiceException e) {
//            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
}
