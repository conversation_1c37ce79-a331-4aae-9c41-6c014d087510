package com.pacto.adm.core.controller;


import com.pacto.adm.core.dto.AplicacaoDescontoTotaisDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.movpagamento.MovPagamentoTotaisDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.adm.core.dto.enveloperesposta.desconto.EnvelopeRespostaListDescontoDTO;
import com.pacto.adm.core.dto.enveloperesposta.produto.EnvelopeRespostaListMovProdutoDTO;
import com.pacto.adm.core.dto.enveloperesposta.produto.EnvelopeRespostaListProdutoDTO;
import com.pacto.adm.core.dto.enveloperesposta.produto.EnvelopeRespostaListProdutoResumidoDTO;
import com.pacto.adm.core.dto.enveloperesposta.produto.EnvelopeRespostaMovProdutoDTO;
import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.filtros.FiltroMovProdutoJSON;
import com.pacto.adm.core.enumerador.TipoProdutoEnum;
import com.pacto.adm.core.services.interfaces.MovProdutoService;
import com.pacto.adm.core.services.interfaces.ProdutoService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/produtos")
@Tag(name = "Produto")
public class MovProdutoController {

    @Autowired
    MovProdutoService movProdutoService;
    @Autowired
    private ProdutoService service;

    @Operation(
            summary = "Consultar uma movimentação de produto",
            description = "Consulta os detalhes e informações de uma movimentação de produto.",
            parameters = {
                    @Parameter(name = "codigoMovProduto", description = "Código da movimentação que será consultada", example = "145")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaMovProdutoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaMovProdutoDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/{codigoMovProduto}")
    public ResponseEntity<EnvelopeRespostaDTO> details(@PathVariable Integer codigoMovProduto) {
        try {
            return ResponseEntityFactory.ok(movProdutoService.details(codigoMovProduto));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar uma movimentação de produto com validade",
            description = "Consulta uma movimentação de produto com validade pelo código da pessoa que adquiriu o produto.",
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa que será consultada", example = "13"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas pelos atributos disponíveis.<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código</li>" +
                                    "<li><strong>contrato:</strong> Ordena pelo contrato</li>" +
                                    "<li><strong>empresa:</strong> Ordena pelo nome da empresa</li>" +
                                    "<li><strong>descricao:</strong> Ordena pela descrição</li>" +
                                    "<li><strong>datalancamento:</strong> Ordena pela data de lançamento</li>" +
                                    "<li><strong>dataFinalVigencia:</strong> Ordena pela data final de vigência</li>" +
                                    "<li><strong>valordesconto:</strong> Ordena pelo valor do desconto</li>" +
                                    "<li><strong>totalfinal:</strong> Ordena pelo total final</li>" +
                                    "<li><strong>situacao:</strong> Ordena pela situação</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para realizar a ordenação, utilize o padrão: <strong>atributo,ordem</strong>.<br/>" +
                                    "Por padrão, caso não seja enviado nenhum parâmetro para ordenação, as respostas serão ordenadas por <strong>datafinalvigencia desc</strong>.<br/><br/>",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListMovProdutoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListMovProdutoDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/{codPessoa}/produto-vencimento")
    public ResponseEntity<EnvelopeRespostaDTO> consultarProdutoComValidadePorCodigoPessoa(
            @PathVariable Integer codPessoa,
            @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroMovProdutoJSON filtroMovProdutoJSON = new FiltroMovProdutoJSON(filtros);
            return ResponseEntityFactory.ok(movProdutoService.consultarProdutoComValidadePorCodigoPessoa(codPessoa, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Consultar descontos por tipos de produtos",
            description = "Consulta os descontos por tipos de produtos. Útil para vender planos ou produtos na academia.",
            parameters = {
                    @Parameter(
                            name = "tipo", description = "Tipo do produto que será consultado." +
                            "<strong>Valores disponíveis</strong>" +
                            "<ul>" +
                            "<li>1 - MA (Matrícula)</li>" +
                            "<li>2 - RE (Rematrícula)</li>" +
                            "<li>3 - RN (Renovação)</li>" +
                            "<li>4 - PE (Produto Estoque)</li>" +
                            "<li>5 - PM (Mês de Referência Plano)</li>" +
                            "<li>6 - SE (Serviço)</li>" +
                            "<li>7 - CD (Convênio de Desconto)</li>" +
                            "<li>8 - DE (Desconto)</li>" +
                            "<li>9 - DV (Devolução)</li>" +
                            "<li>10 - TR (Trancamento)</li>" +
                            "<li>11 - RT (Retorno Trancamento)</li>" +
                            "<li>12 - AA (Aula Avulsa)</li>" +
                            "<li>13 - DI (Diária)</li>" +
                            "<li>14 - FR (FreePass)</li>" +
                            "<li>15 - AH (Alterar - Horário)</li>" +
                            "<li>16 - MM (Manutenção Modalidade)</li>" +
                            "<li>17 - MC (Manutenção Conta Corrente)</li>" +
                            "<li>18 - DR (Desconto em Renovação Antecipada)</li>" +
                            "<li>19 - TP (Taxa de Personal)</li>" +
                            "<li>20 - SS (Sessão)</li>" +
                            "<li>21 - DC (Devolução de crédito de conta corrente do cliente)</li>" +
                            "<li>22 - AT (Atestado)</li>" +
                            "<li>23 - TD (Taxa de Adesão Plano Recorrência)</li>" +
                            "<li>24 - TN (Taxa de Renegociação)</li>" +
                            "<li>25 - CP (Crédito de personal)</li>" +
                            "<li>26 - TA (Taxa de Anuidade Plano Recorrência)</li>" +
                            "<li>27 - RD (Devolução de recebíveis)</li>" +
                            "<li>28 - CC (Depósito conta corrente do aluno)</li>" +
                            "<li>29 - AC (Acerto conta corrente do aluno)</li>" +
                            "<li>30 - QU (Quitação de dinheiro - Cancelamento)</li>" +
                            "<li>31 - AR (Armário)</li>" +
                            "<li>32 - MJ (Multa e Juros)</li>" +
                            "<li>33 - CH (Cheques devolvidos)</li>" +
                            "<li>34 - DS (Desafio)</li>" +
                            "<li>35 - HM (App Home Fit)</li>" +
                            "<li>36 - BT (Bio Totem)</li>" +
                            "<li>37 - VT (App Vitio)</li>" +
                            "<li>38 - CN (Consulta Nutricional)</li>" +
                            "<li>39 - OC (Ordem de compra)</li>" +
                            "<li>40 - LC (Locação)</li>" +
                            "<li>41 - CT (Carteirinha)</li>" +
                            "</ul>",
                            example = "MA", schema = @Schema(implementation = TipoProdutoEnum.class), required = true
                    ),
                    @Parameter(name = "empresaId", description = "ID da empresa que será consultado os descontos.", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListDescontoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListDescontoDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/{tipo}/desconto")
    public ResponseEntity<EnvelopeRespostaDTO> produtosPorDescricao(@PathVariable(value = "tipo") String tipo,
                                                             @RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(service.descontosTipoProdutoVenda(tipo, empresaId));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Consultar produtos vendidos",
            description = "Consulta os produtos que foram vendidos.",
            parameters = {
                    @Parameter(name = "quickSearch", description = "Procura os produtos pela descrição da venda. Se o parâmetro não for enviado a resposta irá conter os últimos produtos vendidos", example = "Plano da Pacto"),
                    @Parameter(name = "produtosAdicionados", description = "Filtro que excluí os produtos que já foram adicionados. Deve ser informado o código dos produtos separados por , ex: 1,2,3", example = "14,5,3"),
                    @Parameter(name = "empresaId", description = "Empresa que será consultado os produtos vendidos", example = "1")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListProdutoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListProdutoDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> produtosVenda(@RequestParam(value = "quickSearch", required = false) String quickSearch,
                                                               @RequestParam(value = "produtosAdicionados", required = false) String produtosAdicionados,
                                                             @RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(service.produtosVenda(quickSearch, produtosAdicionados, empresaId));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }


    @GetMapping("/plano/{plano}")
    public ResponseEntity<EnvelopeRespostaDTO> produtosPlano(@RequestParam(value = "quickSearch", required = false) String quickSearch,
                                                               @PathVariable Integer plano,
                                                               @RequestParam(value = "produtosAdicionados", required = false) String produtosAdicionados,
                                                             @RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(service.produtosPlano(quickSearch, produtosAdicionados, plano, empresaId));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Consultar os produtos de modalidades",
            description = "Consulta os produtos de modalidades.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListProdutoResumidoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListProdutoResumidoDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/produtos-modalidade")
    public ResponseEntity<EnvelopeRespostaDTO> consultarProdutosModalidade() {
        try {
            return ResponseEntityFactory.ok(service.findProdutosByListTipo());
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/aplicacao-desconto")
    public ResponseEntity<EnvelopeRespostaDTO> aplicacaoDesconto(
            @RequestParam(value = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroContratoJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroContratoJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            AplicacaoDescontoTotaisDTO aplicacaoDescontoTotaisDTO = movProdutoService.findAllAplicacaoDesconto(filtroContratoJSON, paginadorDTO);
            EnvelopeRespostaDTO envelopeRespostaDTO = EnvelopeRespostaDTO.of(
                    aplicacaoDescontoTotaisDTO.getAplicacaoDesconto(), paginadorDTO
            );
            envelopeRespostaDTO.setContent(aplicacaoDescontoTotaisDTO);
            return ResponseEntityFactory.ok(envelopeRespostaDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping("/aplicacao-desconto-sem-totais")
    public ResponseEntity<EnvelopeRespostaDTO> aplicacaoDescontoSemTotais(
            @RequestParam(value = "filters", required = false) String filtros,
            PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroContratoJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroContratoJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            AplicacaoDescontoTotaisDTO aplicacaoDescontoTotaisDTO = movProdutoService.findAllAplicacaoDesconto(filtroContratoJSON, paginadorDTO);
            return ResponseEntityFactory.ok(aplicacaoDescontoTotaisDTO.getAplicacaoDesconto(), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
