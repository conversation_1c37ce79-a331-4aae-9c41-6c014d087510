package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.infomigracao.EnvelopeRespostaInfoMigracaoDTO;
import com.pacto.adm.core.dto.enveloperesposta.infomigracao.EnvelopeRespostaOrigem;
import com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis.EnvelopeRespostaBoolan;
import com.pacto.adm.core.enumerador.OrigemSistemaEnum;
import com.pacto.adm.core.enumerador.TipoInfoMigracaoEnum;
import com.pacto.adm.core.services.interfaces.InfoMigracaoService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/info-migracao")
@Tag(name = "Informações de Migração", description = "Operações de gestão de migração")
public class InfoMigracaoController {

    @Autowired
    private InfoMigracaoService infoMigracaoService;

    @Operation(
            summary = "Consultar se um recurso está habilitado no sistema",
            description = "Consulta se um recurso está habilitado no sistema. Se a resposta conter true, o recurso está habilitado, se conter false, o recurso não está habilitado",
            parameters = {
                    @Parameter(name = "recurso", description = "Recurso que será consultado",
                            schema = @Schema(implementation = TipoInfoMigracaoEnum.class))
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaBoolan.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaBoolan.resposta)
                            )
                    )
            }
    )
    @GetMapping("/habilitado/{recurso}")
    public ResponseEntity<EnvelopeRespostaDTO> habilitado(@PathVariable String recurso) {
        try {
            return ResponseEntityFactory.ok(infoMigracaoService.recursoHabilitado(TipoInfoMigracaoEnum.valueOf(recurso)));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar a lista de recursos da migração habilitados no sistema",
            description = "Consulta a lista de resursos da migração habilitados no sistema",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaInfoMigracaoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaInfoMigracaoDTO.resposta)
                            )
                    )
            }
    )
    @GetMapping("/habilitados")
    public ResponseEntity<EnvelopeRespostaDTO> habilitados() {
        try {
            return ResponseEntityFactory.ok(infoMigracaoService.recursosHabilitados());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar informações de migração à partir de um recurso",
            description = "Consulta as informações de migração à partir de um recurso.",
            parameters = {
                    @Parameter(name = "recurso", description = "Recurso que será consultado",
                            schema = @Schema(implementation = TipoInfoMigracaoEnum.class), required = true)
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaInfoMigracaoDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaInfoMigracaoDTO.resposta)
                            )
                    )}
    )
    @GetMapping("/consultar/{recurso}")
    public ResponseEntity<EnvelopeRespostaDTO> consultar(@PathVariable String recurso) {
        try {
            return ResponseEntityFactory.ok(infoMigracaoService.consultar(TipoInfoMigracaoEnum.valueOf(recurso)));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar o sistema de origem de um recurso",
            description = "Consulta o sistema de origem de um recurso.",
            parameters = {
                    @Parameter(name = "recurso", description = "Recurso que será consultado",
                            schema = @Schema(implementation = TipoInfoMigracaoEnum.class), required = true
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaOrigem.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200",
                                            description = "Os possíveis valores de retorno são:" +
                                                    "<ul>" +
                                                    "<li>1 - ZW (ZillyonWeb)</li>" +
                                                    "<li>2 - AULA_CHEIA (Agenda Web)</li>" +
                                                    "<li>3 - TREINO (Pacto Treino)</li>" +
                                                    "<li>4 - APP_TREINO (App Treino)</li>" +
                                                    "<li>5 - APP_PROFESSOR (App Professor)</li>" +
                                                    "<li>6 - AUTO_ATENDIMENTO (Autoatendimento)</li>" +
                                                    "<li>7 - SITE (Site Vendas)</li>" +
                                                    "<li>8 - BUZZLEAD (Buzz Lead)</li>" +
                                                    "<li>9 - VENDAS_ONLINE_2 (Vendas 2.0)</li>" +
                                                    "<li>10 - APP_CONSULTOR (App do consultor)</li>" +
                                                    "<li>11 - BOOKING_GYMPASS (Booking Gympass)</li>" +
                                                    "<li>12 - FILA_ESPERA (Fila de espera)</li>" +
                                                    "<li>13 - IMPORTACAO_API (Importação API)</li>" +
                                                    "<li>14 - HUBSPOT (Hubspot Lead)</li>" +
                                                    "<li>15 - CRM_META_DIARIA (CRM Meta Diaria)</li>" +
                                                    "<li>16 - APP_FLOW (Pacto Flow)</li>" +
                                                    "<li>17 - NOVA_TELA_NEGOCIACAO (Nova Tela de Negociação)</li>" +
                                                    "</ul>",
                                            value = EnvelopeRespostaOrigem.resposta)
                            )
                    )}
    )
    @GetMapping("/origem/{recurso}")
    public ResponseEntity<EnvelopeRespostaDTO> origem(@PathVariable String recurso) {
        try {
            return ResponseEntityFactory.ok(infoMigracaoService.origemRecurso(TipoInfoMigracaoEnum.valueOf(recurso)));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Habilitar um recurso no sistema",
            description = "Habilitar um recurso no sistema.",
            parameters = {
                    @Parameter(
                            name = "recurso", description = "Recurso que será cadastrado",
                            schema = @Schema(implementation = TipoInfoMigracaoEnum.class),
                            required = true
                    ),
                    @Parameter(
                            name = "info", description = "Indica se o recurso está ativo ou não (valores aceitos true ou false)",
                            example = "true",
                            required = true
                    ),
                    @Parameter(
                            name = "origem", description = "Origem do sistema que será cadastrada." +
                            "<strong>Valores disponíveis</strong>" +
                            "<ul>" +
                            "<li>1 - ZW (ZillyonWeb)</li>" +
                            "<li>2 - AULA_CHEIA (Agenda Web)</li>" +
                            "<li>3 - TREINO (Pacto Treino)</li>" +
                            "<li>4 - APP_TREINO (App Treino)</li>" +
                            "<li>5 - APP_PROFESSOR (App Professor)</li>" +
                            "<li>6 - AUTO_ATENDIMENTO (Autoatendimento)</li>" +
                            "<li>7 - SITE (Site Vendas)</li>" +
                            "<li>8 - BUZZLEAD (Buzz Lead)</li>" +
                            "<li>9 - VENDAS_ONLINE_2 (Vendas 2.0)</li>" +
                            "<li>10 - APP_CONSULTOR (App do consultor)</li>" +
                            "<li>11 - BOOKING_GYMPASS (Booking Gympass)</li>" +
                            "<li>12 - FILA_ESPERA (Fila de espera)</li>" +
                            "<li>13 - IMPORTACAO_API (Importação API)</li>" +
                            "<li>14 - HUBSPOT (Hubspot Lead)</li>" +
                            "<li>15 - CRM_META_DIARIA (CRM Meta Diaria)</li>" +
                            "<li>16 - APP_FLOW (Pacto Flow)</li>" +
                            "<li>17 - NOVA_TELA_NEGOCIACAO (Nova Tela de Negociação)</li>" +
                            "</ul>",
                            schema = @Schema(implementation = OrigemSistemaEnum.class),
                            required = true
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Exemplo Resposta 200",
                                                    description = "Esse endpoint retorna uma resposta apenas com o status 200, sem mensagens.",
                                                    value = ""),
                                    }
                            )
                    )
            }
    )
    @PostMapping("/{recurso}/{info}")
    public ResponseEntity<EnvelopeRespostaDTO> alternar(@PathVariable String recurso,
                                                        @PathVariable String info,
                                                        @RequestParam(required = false) String origem) {
        try {
            infoMigracaoService.inserir(TipoInfoMigracaoEnum.valueOf(recurso), info, origem);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Verificar se a negociação está habilitada para uma empresa",
            description = "Verifica se a negociação está habilitada para uma empresa pelo código dela.",
            parameters = {
                    @Parameter(
                            name = "empresaId", description = "Código da empresa", example = "1", required = true
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaBoolan.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaBoolan.respostaTrue)
                            )
                    )
            }
    )
    @GetMapping("/negociacao-habilitada")
    public ResponseEntity<EnvelopeRespostaDTO> negociacaoHabilitada(@RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            boolean negociacaoPadraoEmpresa = infoMigracaoService.recursoPadraoEmpresa(TipoInfoMigracaoEnum.NEGOCIACAO, empresaId);
            Boolean negociacaoHabilitada = (negociacaoPadraoEmpresa || infoMigracaoService.recursoHabilitado(TipoInfoMigracaoEnum.NEGOCIACAO));
            return ResponseEntityFactory.ok(negociacaoHabilitada);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar se um recurso está habilitado para determinada empresa",
            description = "Consulta se um recurso está habilitado para determinada empresa à partir do código da empresa.",
            parameters = {
                    @Parameter(name = "recurso", description = "Recurso que será consultado", required = true,
                            schema = @Schema(implementation = TipoInfoMigracaoEnum.class)),
                    @Parameter(
                            name = "empresaId", description = "Código da empresa", example = "1", required = true
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaBoolan.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaBoolan.respostaTrue)
                            )
                    )
            }
    )
    @GetMapping("/recurso-padrao-empresa/{recurso}")
    public ResponseEntity<EnvelopeRespostaDTO> recursoPadraoEmpresa(@PathVariable String recurso,
                                                                    @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(infoMigracaoService.recursoPadraoEmpresa(TipoInfoMigracaoEnum.valueOf(recurso), empresaId));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
