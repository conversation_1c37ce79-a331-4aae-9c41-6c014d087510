package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.enveloperesposta.notafiscal.EnvelopeRespostaListNotaFiscalDTO;
import com.pacto.adm.core.dto.filtros.FiltroNotaFiscalJSON;
import com.pacto.adm.core.services.interfaces.NotaFiscalService;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/nota-fiscal")
@Tag(name = "Gestão de Notas")
public class NotaFiscalController {

    @Autowired
    private NotaFiscalService service;

    @Operation(
            summary = "Consultar notas fiscais de uma pessoa",
            description = "Consulta todas as notas de uma pessoa pelo código dela.",
            parameters = {
                    @Parameter(name = "codPessoa", description = "Código da pessoa que se deseja consultar as notas fiscais", example = "3"),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena os resultados com base nos campos permitidos.<br/>" +
                                    "<strong>Atributos de ordenação disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Ordena pelo código</li>" +
                                    "<li><strong>tipo:</strong> Ordena pelo tipo</li>" +
                                    "<li><strong>outros campos válidos:</strong> Ordena diretamente por algum campo existente da Nota Fiscal, como dataEmissao</li>" +
                                    "</ul>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordem ascendente</li>" +
                                    "<li><strong>desc:</strong> Ordem descendente</li>" +
                                    "</ul>" +
                                    "Utilize o formato: <strong>atributo,ordem</strong>.<br/>" +
                                    "Se não informado, o padrão será <strong>codigo desc</strong>.",
                            example = "tipo,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListNotaFiscalDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListNotaFiscalDTO.resposta),
                                    }
                            )
                    )
            }
    )
    @GetMapping("/by-pessoa/{codPessoa}")
    public ResponseEntity<EnvelopeRespostaDTO> findByPessoa(
            @PathVariable Integer codPessoa,
            @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroNotaFiscalJSON filtrosJson = new FiltroNotaFiscalJSON(filtros);
            return ResponseEntityFactory.ok(service.findByPessoa(codPessoa, filtrosJson, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno("Erro", e.getMessage());
        }
    }
}
