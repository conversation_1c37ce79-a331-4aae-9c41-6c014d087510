package com.pacto.adm.core.controller.questionario.questionariocliente;

import com.pacto.adm.core.dao.interfaces.agenda.AgendaRepository;
import com.pacto.adm.core.dto.QuestionarioClienteDTO;
import com.pacto.adm.core.dto.enveloperesposta.cliente.questionarios.EnvelopeRespostaQuestionarioClienteDTO;
import com.pacto.adm.core.services.interfaces.QuestionarioClienteService;
import com.pacto.adm.core.swagger.respostas.questionariocliente.ExemploRespostaBoolean;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.config.utils.Uteis;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/questionario-cliente")
public class QuestionarioClienteController {

    private final QuestionarioClienteService questionarioClienteService;
    private final AgendaRepository agendaRepository;

    public QuestionarioClienteController(QuestionarioClienteService questionarioClienteService, AgendaRepository agendaRepository) {
        this.questionarioClienteService = questionarioClienteService;
        this.agendaRepository = agendaRepository;
    }

    @PostMapping()
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarBv(
            @RequestBody QuestionarioClienteDTO questionarioClienteDTO,
            @RequestParam(required = false, defaultValue = "false") Boolean rematricula
    ) {
        try {
            questionarioClienteDTO = questionarioClienteService.cadastrarBv(questionarioClienteDTO, rematricula);
            questionarioClienteService.notificarWebhookBV(questionarioClienteDTO);
            return ResponseEntityFactory.ok(questionarioClienteDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping("/ultimo-bv-cliente/{codCliente}")
    public ResponseEntity<EnvelopeRespostaDTO> ultimoBvCliente(
            @PathVariable Integer codCliente,
            @RequestParam(required = false, defaultValue = "false") Boolean rematricula
    ) {
        try {
            return ResponseEntityFactory.ok(questionarioClienteService.ultimoBVCliente(
                    codCliente, rematricula, false
            ));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping("/possui-agendamentos-consultor/{codCliente}/{codConsultor}")
    public ResponseEntity<EnvelopeRespostaDTO> possuiAgendamentoConsultorAtual(
            @PathVariable Integer codCliente,
            @PathVariable Integer codConsultor
    ) {
        try {
            return ResponseEntityFactory.ok(agendaRepository.clientePossuiAgendamentosColaborador(
                    codConsultor, codCliente,
                    Uteis.getDataJDBC(Calendario.hoje())
            ));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("erro-consultar-agendamentos-consultor", e.getMessage());
        }
    }

    @GetMapping("/obter-bv-visitante")
    public ResponseEntity<EnvelopeRespostaDTO> obterBvVisitante(@RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(questionarioClienteService.obterBVVisitante(empresaId));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
