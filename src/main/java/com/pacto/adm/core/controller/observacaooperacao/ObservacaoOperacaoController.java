package com.pacto.adm.core.controller.observacaooperacao;

import com.pacto.adm.core.dto.filtros.FiltroBIControleOperacoesJSON;
import com.pacto.adm.core.dto.observacaooperacao.ObservacaoOperacaoTotaisDTO;
import com.pacto.adm.core.enumerador.TipoObservacaoOperacaoEnum;
import com.pacto.adm.core.services.interfaces.observacaooperacao.ObservacaoOperacaoService;
import com.pacto.adm.core.swagger.respostas.observacaooperacao.ExemploRespostaListObservacaoOperacaoPaginacao;
import com.pacto.adm.core.swagger.respostas.observacaooperacao.ExemploRespostaObservacaoOperacaoTotais;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/observacao-operacao")
@Tag(name = "BI Administrativo")
public class ObservacaoOperacaoController {

    private final ObservacaoOperacaoService observacaoOperacaoService;

    public ObservacaoOperacaoController(ObservacaoOperacaoService observacaoOperacaoService) {
        this.observacaoOperacaoService = observacaoOperacaoService;
    }

    @Operation(
            summary = "Consultar parcelas canceladas com totais",
            description = "Consulta as observações de parcelas canceladas com informações totalizadas dos valores das operações realizadas no período especificado.",
            tags = {"BI Administrativo"},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "dataOperacao,desc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **inicio**: Data de início do período para consulta (obrigatório)\n" +
                                    "- **fim**: Data de fim do período para consulta (obrigatório)\n" +
                                    "- **empresa**: Código da empresa para filtrar as operações\n" +
                                    "- **colaboradores**: Lista de códigos dos colaboradores para filtrar as operações\n" +
                                    "- **quickSearchValue**: Valor para busca rápida por nome de cliente ou informações relacionadas",
                            example = "{\"inicio\":\"2024-01-01T00:00:00.000Z\",\"fim\":\"2024-01-31T23:59:59.999Z\",\"empresa\":1,\"quickSearchValue\":\"João Silva\"}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaObservacaoOperacaoTotais.class)
                            )
                    )
            }
    )
    @GetMapping("/parcelas-canceladas")
    public ResponseEntity<EnvelopeRespostaDTO> parcelasCanceladas(
            @RequestParam(value = "filters", required = false) String filtros,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            ObservacaoOperacaoTotaisDTO observacaoOperacaoTotaisDTO = observacaoOperacaoService.parcelasCanceladas(
                    filtroBIControleOperacoesJSON, paginadorDTO, false
            );
            EnvelopeRespostaDTO envelopeRespostaDTO = EnvelopeRespostaDTO.of(
                    observacaoOperacaoTotaisDTO.getObservacoes(), paginadorDTO
            );
            envelopeRespostaDTO.setContent(observacaoOperacaoTotaisDTO);
            return ResponseEntityFactory.ok(envelopeRespostaDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar parcelas canceladas sem totais",
            description = "Consulta apenas a lista das observações de parcelas canceladas sem informações totalizadas, retornando somente os dados das operações realizadas no período especificado.",
            tags = {"BI Administrativo"},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "dataOperacao,desc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **inicio**: Data de início do período para consulta (obrigatório)\n" +
                                    "- **fim**: Data de fim do período para consulta (obrigatório)\n" +
                                    "- **empresa**: Código da empresa para filtrar as operações\n" +
                                    "- **colaboradores**: Lista de códigos dos colaboradores para filtrar as operações\n" +
                                    "- **quickSearchValue**: Valor para busca rápida por nome de cliente ou informações relacionadas",
                            example = "{\"inicio\":\"2024-01-01T00:00:00.000Z\",\"fim\":\"2024-01-31T23:59:59.999Z\",\"empresa\":1,\"quickSearchValue\":\"João Silva\"}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListObservacaoOperacaoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping("/parcelas-canceladas-sem-totais")
    public ResponseEntity<EnvelopeRespostaDTO> parcelasCanceladasSemTotais(
            @RequestParam(value = "filters", required = false) String filtros,
            @Parameter(hidden = true) PaginadorDTO paginadorDTO
    ) {
        try {
            FiltroBIControleOperacoesJSON filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON();
            if (filtros != null) {
                filtroBIControleOperacoesJSON = new FiltroBIControleOperacoesJSON(new JSONObject(filtros));
            }
            return ResponseEntityFactory.ok(observacaoOperacaoService.parcelasCanceladas(
                    filtroBIControleOperacoesJSON, paginadorDTO, false
            ).getObservacoes(), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
