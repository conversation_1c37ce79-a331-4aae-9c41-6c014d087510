package com.pacto.adm.core.controller;


import com.pacto.adm.core.dto.enveloperesposta.integracoes.*;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.alterdata.EnvelopeRespostaConfiguracaoContabilAlterData;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.botconversa.EnvelopeRespostaConfiguracaoIntegracaoBotConversaDTO;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.botconversa.EnvelopeRespostaIntegracaoBotConversaDTO;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.botconversa.EnvelopeRespostaIntegracaoGymbotProDTO;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.botconversa.EnvelopeRespostaListConfiguracaoIntegracaoBotConversaDTO;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.facilitepay.EnvelopeRespostaConfiguracaoIntegracaoFacilitePayDTO;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.spivi.EnvelopeRespostaConfiguracaoIntegracaoSpiviDTO;
import com.pacto.adm.core.dto.enveloperesposta.integracoes.spivi.EnvelopeRespostaListConfiguracaoIntegracaoSpiviDTO;
import com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis.EnvelopeRespostaBoolan;
import com.pacto.adm.core.dto.enveloperesposta.tiposvariaveis.EnvelopeRespostaString;
import com.pacto.adm.core.dto.filtros.FiltroIntegracoesJSON;
import com.pacto.adm.core.dto.filtros.FiltroProcessarConciliadoraJSON;
import com.pacto.adm.core.dto.integracoes.*;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.adm.core.services.interfaces.*;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.config.utils.UteisValidacao;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/integracoes")
@Tag(name = "Integrações")
public class IntegracoesController {
    @Autowired
    IntegracoesService integracoesService;
    @Autowired
    ParceiroFidelidadeService parceiroFidelidadeService;
    @Autowired
    ConfiguracaoEmpresaRDStationService configuracaoEmpresaRDStationService;
    @Autowired
    ConfiguracaoEmpresaHubSpotService configuracaoEmpresaHubSpotService;
    @Autowired
    ConfiguracaoEmpresaBitrix24Service configuracaoEmpresaBitrix24Service;
    @Autowired
    ConfiguracaoIntegracaoWordPressService configuracaoIntegracaoWordPressService;
    @Autowired
    ConfiguracaoIntegracaoBuzzLeadService configuracaoIntegracaoBuzzLeadService;
    @Autowired
    ConfiguracaoIntegracaoJoinService configuracaoIntegracaoJoinService;
    @Autowired
    ConfiguracaoIntegracaoGenericaLeadsService configuracaoIntegracaoGenericaLeadsService;
    @Autowired
    ConfiguracaoIntegracaoGenericaLeadsGymbotService configuracaoIntegracaoGenericaLeadsGymbotService;
    @Autowired
    ConfiguracaoIntegracaoBotConversa  configuracaoIntegracaoBotConversaService;
    @Autowired
    ConfiguracaoIntegracaoGymbotProService  salvarConfiguracaoIntegracaoGymbotProService;
    @Autowired
    ConfiguracaoIntegracaoFogueteService configuracaoIntegracaoFogueteService;
    @Autowired
    ConfiguracaoIntegracaoEnvioAcessoPratiqueService configuracaoIntegracaoEnvioAcessoPratiqueService;

    @Operation(
            summary = "Consultar todas as configurações das empresas",
            description = "Consulta todas as configurações, podendo aplicar filtros na busca.",
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtro para buscar as configurações de integração da empresa.<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigoEmpresa:</strong> Código da empresa cujas integrações devem ser consultadas. Obrigatório.</li>" +
                                    "<li><strong>moduloEnum:</strong> Módulo de integrações a ser consultado. Valores possíveis: " +
                                    "ADM, CRM, FINANCEIRO, TREINO, PACTO_PAY, FACILITE_PAY.</li>" +
                                    "</ul>",
                            example = "{\"codigoEmpresa\":123,\"moduloEnum\":\"CRM\"}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoIntegracoesEmpresasDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Resposta 200",
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaConfiguracaoIntegracoesEmpresasDTO.resposta
                                            ),
                                    }
                            )
                    )
            }
    )
    @GetMapping("")
    public ResponseEntity<EnvelopeRespostaDTO> findAllByFilters(@RequestParam(value = "filters", required = true) JSONObject filters) {
        try {
            FiltroIntegracoesJSON filtros = new FiltroIntegracoesJSON(filters);
            return ResponseEntityFactory.ok(integracoesService.findAllByFilters(filtros));
        } catch (ServiceException exception) {
            StringBuilder result = new StringBuilder(exception.toString() + "\n");
            StackTraceElement[] trace = exception.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração com o MyWellness",
            description = "Salva as configurações da integração com o MyWellness.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoMyWellnessDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoMyWellnessDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/mywellness")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoMyWellness(@RequestBody ConfiguracaoIntegracaoMyWellnessDTO configuracaoIntegracaoMyWellnessDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoMyWellness(configuracaoIntegracaoMyWellnessDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração com o MentorWeb",
            description = "Salva as configurações da integração com o MentorWeb.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoMentorWebDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoMentorWeb.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/mentor-web")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoMentorWeb(@RequestBody ConfiguracaoIntegracaoMentorWebDTO configuracaoIntegracaoMentorWebDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoMentorWeb(configuracaoIntegracaoMentorWebDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração com o Estacionamento",
            description = "Salva as configurações da integração com o Estacionamento.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoEstacionamentoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoEstacionamentoDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/estacionamento")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoEstacionamento(@RequestBody ConfiguracaoIntegracaoEstacionamentoDTO configuracaoIntegracaoEstacionamentoDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoEstacionamento(configuracaoIntegracaoEstacionamentoDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Testar a integração com o estacionamento",
            description = "Testa a integração com o sistema de estacionamento.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para  testar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoEstacionamentoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoEstacionamentoDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/estacionamento-teste")
    public ResponseEntity<EnvelopeRespostaDTO> testarConfiguracaoIntegracaoEstacionamento(@RequestBody ConfiguracaoIntegracaoEstacionamentoDTO configuracaoIntegracaoEstacionamentoDTO) {
        try {
            integracoesService.testarConfiguracaoIntegracaoEstacionamento(configuracaoIntegracaoEstacionamentoDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração com o CDL SPC",
            description = "Salva as configurações da integração com o CDL SPC.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoCDLSPCDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoCDLSPCDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/cdl-spc")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoCDLSPC(@RequestBody ConfiguracaoIntegracaoCDLSPCDTO configDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoCDLSPC(configDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração com o Delsoft",
            description = "Salva as configurações da integração com o Delsoft.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoDelsoftDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoDelsoftDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/delsoft")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoDelsoft(@RequestBody ConfiguracaoIntegracaoDelsoftDTO configDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoDelsoft(configDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração com o Vitio",
            description = "Salva as configurações da integração com o Vitio.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoVitioDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoVitioDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/vitio")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoVitio(@RequestBody ConfiguracaoIntegracaoVitioDTO configDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoVitio(configDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração com o Vitio",
            description = "Salva as configurações da integração com o Vitio.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoVitioDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoVitioDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/parceiro-fidelidade")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoParceiroFidelidade(@RequestBody ConfiguracaoIntegracaoParceiroFidelidadeDTO configuracaoIntegracaoParceiroFidelidadeDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoParceiroFidelidade(configuracaoIntegracaoParceiroFidelidadeDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração de notificação com Webhook",
            description = "Salva as configurações da integração de notificação com Webhook.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoNotificacaoWebhookDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoNotificacaoWebhookDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/notificacao-webhook")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoNotificacaoWebhook(@RequestBody ConfiguracaoIntegracaoNotificacaoWebhookDTO configuracaoIntegracaoNotificacaoWebhookDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoNotificacaoWebhook(configuracaoIntegracaoNotificacaoWebhookDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração com o Amigo Fit",
            description = "Salva as configurações da integração com o Amigo Fit.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoAmigoFitDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoAmigoFitDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/amigo-fit")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoAmigoFit(@RequestBody ConfiguracaoIntegracaoAmigoFitDTO configAmigoFitDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoAmigoFit(configAmigoFitDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração com o We Help",
            description = "Salva as configurações da integração com o We Help.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoWeHelpDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoWeHelpDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/we-help")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoWeHelp(@RequestBody ConfiguracaoIntegracaoWeHelpDTO configWeHelpDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoWeHelp(configWeHelpDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar configurações do GymPass",
            description = "Consulta as configurações do GymPass, podendo filtrar pela empresa.",
            parameters = {
                    @Parameter(name = "empresa", description = "Código da empresa que será usada para buscar a integração do GymPass.", example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoIntegracaoGymPassDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Resposta 200 (Com parâmetro empresa)",
                                                    summary = "Resposta Status 200 (Com parâmetro empresa)",
                                                    description = "Exemplo de resposta com o status 200, informando o parâmetro empresa (Irá mostrar a configuração apenas da empresa informada).",
                                                    value = EnvelopeRespostaConfiguracaoIntegracaoGymPassDTO.resposta
                                            ),
                                            @ExampleObject(
                                                    name = "Resposta 200 (Sem parâmetro empresa)",
                                                    summary = "Resposta Status 200 (Sem parâmetro empresa)",
                                                    description = "Exemplo de resposta com o status 200, sem informar o parâmetro empresa (Irá mostrar a configuração de todas as empresas).",
                                                    value = EnvelopeRespostaListConfiguracaoIntegracaoGymPassDTO.resposta
                                            )
                                    }
                            )
                    ),
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoIntegracaoGymPassDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200 (Com parâmetro empresa)",
                                                    description = "Exemplo de resposta com o status 200, informando o parâmetro empresa (Irá mostrar a configuração apenas da empresa informada).",
                                                    value = EnvelopeRespostaConfiguracaoIntegracaoGymPassDTO.resposta
                                            ),
                                            @ExampleObject(
                                                    summary = "Resposta Status 200 (Sem parâmetro empresa)",
                                                    description = "Exemplo de resposta com o status 200, sem informar o parâmetro empresa (Irá mostrar a configuração de todas as empresas).",
                                                    value = EnvelopeRespostaListConfiguracaoIntegracaoGymPassDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/gympass")
    public ResponseEntity<EnvelopeRespostaDTO> consultarConfiguracoesIntegracoesGymPass(@RequestParam(required = false) Integer empresa,
                                                                                        @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            if (!UteisValidacao.emptyNumber(empresa)) {
                return ResponseEntityFactory.ok(integracoesService.consultarConfiguracoesIntegracoesGymPass(empresa));
            } else {
                return ResponseEntityFactory.ok(integracoesService.consultarConfiguracoesIntegracoesGymPass());
            }
        } catch (ServiceException exception) {
            StringBuilder result = new StringBuilder(exception.toString() + "\n");
            StackTraceElement[] trace = exception.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Salvar configurações de integração com Gym Pass",
            description = "Salva as configurações da integração com Gym Pass.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoGymPassDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoGymPassDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/gympass")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoGympass(@RequestBody ConfiguracaoIntegracaoGymPassDTO integracaoGymPassDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoGympass(integracaoGymPassDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar configurações da integração com o GoGood",
            description = "Consulta as configurações de integração com o GoGood.",
            parameters = {
                    @Parameter(name = "empresa", description = "Código da empresa que será usada para buscar as informações da integração.", example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoIntegracaoGoGoodDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Status 200 (Com parâmetro empresa)",
                                                    summary = "Resposta Status 200 (Com parâmetro empresa)",
                                                    description = "Exemplo de resposta com o status 200, informando o parâmetro empresa (Irá mostrar a configuração apenas da empresa informada).",
                                                    value = EnvelopeRespostaConfiguracaoIntegracaoGoGoodDTO.resposta
                                            ),
                                            @ExampleObject(
                                                    name = "Status 200 (Sem parâmetro empresa)",
                                                    summary = "Resposta Status 200 (Sem parâmetro empresa)",
                                                    description = "Exemplo de resposta com o status 200, sem informar o parâmetro empresa (Irá mostrar a configuração de todas as empresas).",
                                                    value = EnvelopeRespostaListConfiguracaoIntegracaoGoGoodDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/gogood")
    public ResponseEntity<EnvelopeRespostaDTO> consultarConfiguracoesIntegracoesGogood(@RequestParam(required = false) Integer empresa,
                                                                                       @Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            if (!UteisValidacao.emptyNumber(empresa)) {
                return ResponseEntityFactory.ok(integracoesService.consultarConfiguracoesIntegracoesGoGood(empresa));
            } else {
                return ResponseEntityFactory.ok(integracoesService.consultarConfiguracoesIntegracoesGogood());
            }
        } catch (ServiceException exception) {
            StringBuilder result = new StringBuilder(exception.toString() + "\n");
            StackTraceElement[] trace = exception.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Salvar configurações de integração com GoGood",
            description = "Salva as configurações da integração com GoGood.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoGoGoodDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoGoGoodDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/gogood")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoGogood(@RequestBody ConfiguracaoIntegracaoGoGoodDTO integracaoGoGoodDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoGoGood(integracaoGoGoodDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar configurações da integração com o TotalPass",
            description = "Consulta as configurações de integração com o TotalPass, podendo consultar as configurações de todas as empresas, ou de uma empresa específica.",
            parameters = {
                    @Parameter(name = "empresa", description = "Código da empresa que será usada para buscar as informações da integração.", example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoIntegracaoTotalPassDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Resposta 200 (Com parâmetro empresa)",
                                                    summary = "Resposta Status 200 (Com parâmetro empresa)",
                                                    description = "Exemplo de resposta com o status 200, informando o parâmetro empresa (Irá mostrar a configuração apenas da empresa informada).",
                                                    value = EnvelopeRespostaConfiguracaoIntegracaoTotalPassDTO.resposta
                                            ),
                                            @ExampleObject(
                                                    name = "Resposta 200 (Sem parâmetro empresa)",
                                                    summary = "Resposta Status 200 (Sem parâmetro empresa)",
                                                    description = "Exemplo de resposta com o status 200, sem informar o parâmetro empresa (Irá mostrar a configuração de todas as empresas).",
                                                    value = EnvelopeRespostaListConfiguracaoIntegracaoTotalPassDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/totalpass")
    public ResponseEntity<EnvelopeRespostaDTO> consultarConfiguracoesIntegracoesTotalPass(@Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                                          @RequestParam(required = false) Integer empresa) {
        try {
            if (!UteisValidacao.emptyNumber(empresa)) {
                return ResponseEntityFactory.ok(integracoesService.consultarConfiguracoesIntegracoesTotalPass(empresa));
            } else {
                return ResponseEntityFactory.ok(integracoesService.consultarConfiguracoesIntegracoesTotalPass());
            }
        } catch (ServiceException exception) {
            StringBuilder result = new StringBuilder(exception.toString() + "\n");
            StackTraceElement[] trace = exception.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Salvar configurações de integração com TotalPass",
            description = "Salva as configurações da integração com TotalPass.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoTotalPassDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoTotalPassDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/totalpass")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoTotalpass(@RequestBody ConfiguracaoIntegracaoTotalPassDTO integracaoTotalPassDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoTotalpass(integracaoTotalPassDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar configurações da integração com o Spivi",
            description = "Consulta as configurações de integração com o Spivi, podendo consultar as configurações de todas as empresas, ou de uma empresa específica.",
            parameters = {
                    @Parameter(name = "empresa", description = "Código da empresa que será usada para buscar as informações da integração.", example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoIntegracaoSpiviDTO.class),
                                    examples = {
                                            @ExampleObject(

                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListConfiguracaoIntegracaoSpiviDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/spivi")
    public ResponseEntity<EnvelopeRespostaDTO> consultarConfiguracoesIntegracoesSpivi(@Parameter(hidden = true) @RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            return ResponseEntityFactory.ok(integracoesService.consultarConfiguracoesIntegracoesSpivi());
        } catch (ServiceException exception) {
            StringBuilder result = new StringBuilder(exception.toString() + "\n");
            StackTraceElement[] trace = exception.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), result.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Salvar configurações de integração com Spivi",
            description = "Salva as configurações da integração com Spivi.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoSpiviDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoSpiviDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/spivi")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoSpivi(@RequestBody ConfiguracaoIntegracaoSpiviDTO configuracaoIntegracaoSpiviDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoSpivi(configuracaoIntegracaoSpiviDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Salvar configurações de integração com F360",
            description = "Salva as configurações da integração com F360.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoF360RelatorioDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoF360DTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/f360")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoF360(@RequestBody ConfiguracaoIntegracaoF360RelatorioDTO configuracaoIntegracaoF360RelatorioDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoF360(configuracaoIntegracaoF360RelatorioDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Salvar configurações de integração com a Conciliadora",
            description = "Salva as configurações da integração com a Conciliadora.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoIntegracaoConciliadoraDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoIntegracaoConciliadoraDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoIntegracaoConciliadoraDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaConfiguracaoIntegracaoConciliadoraDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/conciliadora")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoConciliadora(@RequestBody ConfiguracaoIntegracaoConciliadoraDTO configuracaoIntegracaoConciliadoraDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoConciliadora(configuracaoIntegracaoConciliadoraDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Processar conciliadora",
            description = "Processa a integração com a conciliadora.",
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca para a integração com a conciliadora.<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>dataInicial:</strong> Data inicial do intervalo da consulta. Obrigatório.</li>" +
                                    "<li><strong>dataFinal:</strong> Data final do intervalo da consulta. Obrigatório.</li>" +
                                    "<li><strong>codigoEmpresa:</strong> Código da empresa a ser consultada. Obrigatório.</li>" +
                                    "<li><strong>codigoRecibo:</strong> Código do recibo relacionado à operação.</li>" +
                                    "</ul>",
                            example = "{\"dataInicial\":\"2024-01-01\",\"dataFinal\":\"2024-01-31\",\"codigoEmpresa\":123,\"codigoRecibo\":456}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200 (Sem corpo de resposta)",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = ""
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/processar-conciliadora")
    public ResponseEntity<EnvelopeRespostaDTO> processarConciliadora(@RequestParam(value = "filters") JSONObject filters) {
        try {
            FiltroProcessarConciliadoraJSON filtro = new FiltroProcessarConciliadoraJSON(filters);
            integracoesService.processarConciliadora(filtro);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Estornar recebido da conciliadora de pagamentos",
            description = "Estorna um recebido da conciliadora de pagamentos.",
            parameters = {
                    @Parameter(name = "codigoRecibo", description = "Código do recebido que será estornado.", example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200 (Sem corpo de resposta)",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = ""
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/estornar-recibo-conciliadora")
    public ResponseEntity<EnvelopeRespostaDTO> processarConciliadora(@RequestParam(value = "codigorecibo") Integer codigoRecibo) {
        try {
            integracoesService.estornarReciboNaConciliadora(codigoRecibo);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração com a a RDStation",
            description = "Salva as configurações da integração com a RDStation.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoEmpresaRDStationDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoEmpresaRDStationDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoEmpresaRDStationDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaConfiguracaoEmpresaRDStationDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/rdstation")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoEmpresaRDStation(@RequestBody ConfiguracaoEmpresaRDStationDTO configuracaoEmpresaRDStationDTO) {
        try {
            return ResponseEntityFactory.ok(configuracaoEmpresaRDStationService.salvarConfiguracaoEmpresaRDStation(configuracaoEmpresaRDStationDTO));
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @PostMapping("/sesc-df")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoSescDf(@RequestBody ConfiguracaoIntegracaoSescDfDTO configuracaoIntegracaoSescDfDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoSescDf(configuracaoIntegracaoSescDfDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            final StringBuilder result = new StringBuilder(e + "/n");
            final StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Aprovar configurações de integração com a RDStation",
            description = "Aprovar as configurações da integração com a RDStation.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para aprovar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoEmpresaRDStationDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoEmpresaRDStationDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoEmpresaRDStationDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaConfiguracaoEmpresaRDStationDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/rdstation/aprovar")
    public ResponseEntity<EnvelopeRespostaDTO> aprovar(@RequestBody ConfiguracaoEmpresaRDStationDTO configuracaoEmpresaRDStationDTO) {
        try {
            return ResponseEntityFactory.ok(configuracaoEmpresaRDStationService.aprovar(configuracaoEmpresaRDStationDTO));
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integrações com o BotConversa",
            description = "Salvar as configurações da integração com o BotConversa.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaListConfiguracaoIntegracaoBotConversaDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoIntegracaoBotConversaDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaConfiguracaoIntegracaoBotConversaDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/botconversa")
    public ResponseEntity<EnvelopeRespostaDTO> salvarIntegracaoBotConversa(@RequestBody List<ConfiguracaoIntegracaoBotConversaDTO> configDTO) {
        try {
            return ResponseEntityFactory.ok(configuracaoIntegracaoBotConversaService.salvarConfiguracaoIntegracaoBotConversao(configDTO));
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Remover configurações do BotConversa",
            description = "Remove as configurações do BotConversa.",
            parameters = {
                    @Parameter(name = "codConfgBotConversa", description = "Código da configuração que será excluída.", example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaString.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = "{\"content\": \"OK\"}"
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/botconversa/remover/{codConfgBotConversa}")
    public ResponseEntity<EnvelopeRespostaDTO> removeConfigBotConversa(@PathVariable Integer codConfgBotConversa) {
        try {
            return ResponseEntityFactory.ok(configuracaoIntegracaoBotConversaService.removerConfigBotConversa(codConfgBotConversa));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Inativar ou ativar configuração do BotConversa",
            description = "Inativa ou ativa configuração do BotConversa.",
            parameters = {
                    @Parameter(name = "codConfgBotConversa", description = "Código da configuração que será ativada ou inativada.", example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaString.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = "{\"content\": \"OK\"}"
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/botconversa/inativarativa/{codConfgBotConversa}")
    public ResponseEntity<EnvelopeRespostaDTO> inativaOuAtivaConfigBotConversa(@PathVariable Integer codConfgBotConversa) {
        try {
            return ResponseEntityFactory.ok(configuracaoIntegracaoBotConversaService.inativaOuAtivaConfigBotConversa(codConfgBotConversa));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integrações com o GymbotPro",
            description = "Salvar as configurações da integração com o GymbotPro.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaListConfiguracaoIntegracaoGymBotProDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoIntegracaoGymBotProDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaConfiguracaoIntegracaoGymBotProDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/gymbotpro")
    public ResponseEntity<EnvelopeRespostaDTO> salvarIntegracaoGymbotPro(@RequestBody List<ConfiguracaoIntegracaoGymbotProDTO> configDTO) {
        try {
            return ResponseEntityFactory.ok(salvarConfiguracaoIntegracaoGymbotProService.salvarConfiguracaoIntegracaoGymbotProo(configDTO));
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Remover configuração do GymBotPro",
            description = "Remove a configuração do GymBotPro.",
            parameters = {
                    @Parameter(name = "codConfgGymbotPro", description = "Código da configuração que será excluída.", example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaString.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = "{\"content\": \"OK\"}"
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/gymbotpro/remover/{codConfgGymbotPro}")
    public ResponseEntity<EnvelopeRespostaDTO> removeConfigGymbotPro(@PathVariable Integer codConfgGymbotPro) {
        try {
            return ResponseEntityFactory.ok(salvarConfiguracaoIntegracaoGymbotProService.removerConfigGymbotPro(codConfgGymbotPro));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Inativar ou ativar as configuração do GymBotPro",
            description = "Inativa ou ativa uma configuração do GymBotPro.",
            parameters = {
                    @Parameter(name = "codigo", description = "Código da configuração que será ativada ou inativada.", example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaString.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = "{\"content\": \"OK\"}"
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/gymbotpro/inativarativa/{codigo}")
    public ResponseEntity<EnvelopeRespostaDTO> inativaOuAtivaConfigGymbotPro(@PathVariable Integer codigo) {
        try {
            return ResponseEntityFactory.ok(salvarConfiguracaoIntegracaoGymbotProService.inativaOuAtivaConfigGymbotPro(codigo));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Salvar configurações de integração da empresa na HubSpot",
            description = "Salva as configurações de integração da empresa na HubSpot.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoEmpresaHubSpotDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoEmpresaHubSpotDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaConfiguracaoEmpresaHubSpotDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/hubspot")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoEmpresaHubSpot(@RequestBody ConfiguracaoEmpresaHubSpotDTO configDTO) {
        try {
            return ResponseEntityFactory.ok(configuracaoEmpresaHubSpotService.salvarConfiguracaoEmpresaHubSpot(configDTO));
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração da empresa na Bitrix24",
            description = "Salva as configurações de integração da empresa na Bitrix24.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoEmpresaBitrix24DTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoEmpresaBitrix24DTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaConfiguracaoEmpresaBitrix24DTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/bitrix24")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoEmpresaBitrix24(@RequestBody ConfiguracaoEmpresaBitrix24DTO configDTO) {
        try {
            return ResponseEntityFactory.ok(configuracaoEmpresaBitrix24Service.salvarConfiguracaoEmpresaBitrix24(configDTO));
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Aprovar configurações de integração da empresa na HubSpot",
            description = "Aprova as configurações de integração da empresa na HubSpot.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para aprovar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoEmpresaHubSpotDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoEmpresaHubSpotDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaConfiguracaoEmpresaHubSpotDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/hubspot/aprovar")
    public ResponseEntity<EnvelopeRespostaDTO> aprovarHubSpot(@RequestBody ConfiguracaoEmpresaHubSpotDTO configDTO) {
        try {
            return ResponseEntityFactory.ok(configuracaoEmpresaHubSpotService.aprovar(configDTO));
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração da empresa na BuzzLead",
            description = "Salva as configurações de integração da empresa na BuzzLead.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoIntegracaoBuzzLeadDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/buzzlead")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoBuzzLead(@RequestBody ConfiguracaoIntegracaoBuzzLeadDTO configuracaoIntegracaoBuzzLeadDTO) {
        try {
            configuracaoIntegracaoBuzzLeadService.salvar(configuracaoIntegracaoBuzzLeadDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações de integração do envio de SMS",
            description = "Salva as configurações de integração do envio de SMS.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoIntegracaoSmsDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/sms")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoSms(@RequestBody ConfiguracaoIntegracaoSmsDTO configSmsDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoSms(configSmsDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Salvar configurações da integração com o WordPress",
            description = "Salva as configurações da integração com o WordPress.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoIntegracaoBuzzLeadDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/wordpress")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoWordPress(@RequestBody ConfiguracaoIntegracaoWordPressDTO configDTO) {
        try {
            configuracaoIntegracaoWordPressService.salvar(configDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Salvar configurações da integração com a Join",
            description = "Salva as configurações da integração com a Join.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoIntegracaoBuzzLeadDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/join")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoJoin(@RequestBody ConfiguracaoIntegracaoJoinDTO configDTO) {
        try {
            configuracaoIntegracaoJoinService.salvar(configDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações da integração com a GenericaLeads",
            description = "Salva as configurações da integração com a GenericaLeads.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoIntegracaoGenericaLeadsDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/genericaleads")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoGenericaLeads(@RequestBody ConfiguracaoIntegracaoGenericaLeadsDTO configDTO) {
        try {
            configuracaoIntegracaoGenericaLeadsService.salvar(configDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações da integração com a GymbotLeads",
            description = "Salva as configurações da integração com a GymbotLeads.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoIntegracaoGenericaLeadsGymbotDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoIntegracaoGenericaLeadsGymbotDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaConfiguracaoIntegracaoGenericaLeadsGymbotDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/genericaleadsgymbot")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoGymbotLeads(
            @RequestBody ConfiguracaoIntegracaoGenericaLeadsGymbotDTO configDTO) {
        try {
            configuracaoIntegracaoGenericaLeadsGymbotService.salvar(configDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar a configuração da integração com o GymBotLeads",
            description = "Consulta a configuração da integração com o GymBotLeads",
            parameters = {
                    @Parameter(name = "empresa", description = "Código da empresa que será consultada a configuração.", example = "5", required = true)
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoIntegracaoGenericaLeadsGymbotDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaConfiguracaoIntegracaoGenericaLeadsGymbotDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/genericaleadsgymbot")
    public ResponseEntity<EnvelopeRespostaDTO> consultarConfiguracaoIntegracaoGymbotLeads(
            @RequestParam(required = true) Integer empresa) {
        try {
            return ResponseEntityFactory.ok(
                    configuracaoIntegracaoGenericaLeadsGymbotService.findByEmpresaId(empresa)
            );
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Disparar fluxo do GymBot",
            description = "Dispara um fluxo do GymBot.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para disparar um fluxo do GymBot.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaIntegracaoBotConversaDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200 (Sem corpo de resposta)",
                                                    description = "Exemplo de resposta com o status 200."
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/dispararFluxoGymBot")
    public ResponseEntity<EnvelopeRespostaDTO> dispararFluxoGymBot(@RequestBody IntegracaoBotConversaDTO botConversaDTO) {
        try {
            configuracaoIntegracaoBotConversaService.enviarFluxoBotConversa(botConversaDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Disparar fluxo do GymBotPro",
            description = "Dispara um fluxo do GymBotPro.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para disparar um fluxo do GymBot.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaIntegracaoGymbotProDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200 (Sem corpo de resposta)",
                                                    description = "Exemplo de resposta com o status 200."
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/dispararFluxoGymBotPro")
    public ResponseEntity<EnvelopeRespostaDTO> dispararFluxoGymBotPro(@RequestBody IntegracaoGymbotProDTO gymbotProDTO) {
        try {
            configuracaoIntegracaoBotConversaService.enviarFluxoGymbotPro(gymbotProDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar existência de fluxo do GymBot",
            description = "Consulta a existência de fluxo do GymBot filtrando por empresa. A resposta da requisição é true quando há o fluxo e false quando não existe o fluxo.",
            parameters = {
                    @Parameter(name = "idEmpresa", description = "Código da empresa que será consultada.", example = "5", required = true)
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaBoolan.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaBoolan.respostaTrue
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/existeFluxoTelaClienteByEmpresa/{idEmpresa}")
    public ResponseEntity<EnvelopeRespostaDTO> obterFluxoGymBot(@PathVariable Integer idEmpresa)  {
        try {
            return ResponseEntityFactory.ok(configuracaoIntegracaoBotConversaService.existeFluxoTelaClienteByEmpresa(String.valueOf(idEmpresa)));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar existência de fluxo do GymBotPro",
            description = "Consulta a existência de fluxo do GymBotPro filtrando por empresa. A resposta da requisição é true quando há o fluxo e false quando não existe o fluxo.",
            parameters = {
                    @Parameter(name = "idEmpresa", description = "Código da empresa que será consultada.", example = "5", required = true)
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaBoolan.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaBoolan.respostaTrue
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/existeFluxoGymbotProTelaClienteByEmpresa/{idEmpresa}")
    public ResponseEntity<EnvelopeRespostaDTO> obterFluxoGymBotPro(@PathVariable Integer idEmpresa)  {
        try {
            return ResponseEntityFactory.ok(configuracaoIntegracaoBotConversaService.existeFluxoGymbotProTelaClienteByEmpresa(String.valueOf(idEmpresa)));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Salvar configuração de integração com a Contabilidade AlterData",
            description = "Salvar configuração de integração com a Contabilidade AlterData.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações salvar a configuração",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoContabilAlterData.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200 (Sem corpo de resposta)",
                                                    description = "Exemplo de resposta com o status 200."
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/alterdata")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoAlterData(@RequestBody ConfiguracaoIntegracaoSistemaContabilAlterDataDTO configuracaoIntegracaoSistemaContabilAlterDataDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoSistemaContabilAlterData(configuracaoIntegracaoSistemaContabilAlterDataDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configuração de integração com a FacilitePay",
            description = "Salva as configuração de integração com a FacilitePay.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações salvar a configuração",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoIntegracaoFacilitePayDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200 (Sem corpo de resposta)",
                                                    description = "Exemplo de resposta com o status 200."
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping("/recursos-facilitepay")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoRecursosFacilitePay(@RequestBody ConfiguracaoIntegracaoRecursosFacilitePayDTO configDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoRecursosFacilitePay(configDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações da integração com a Nuvemshop",
            description = "Salva as configurações da integração com a Nuvemshop.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoIntegracaoNuvemshopDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/nuvemshop")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoNuvemshop(@RequestBody ConfiguracaoIntegracaoNuvemshopDTO configDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoNuvemshop(configDTO);
            return ResponseEntityFactory.ok();
        } catch (RuntimeException e) {
            return ResponseEntityFactory.mensagemFront("ERRO", e.getMessage());
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações da integração com a Foguete",
            description = "Salva as configurações da integração com a Foguete.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoIntegracaoFogueteDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/foguete")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoFoguete(@RequestBody ConfiguracaoIntegracaoFogueteDTO configDTO) {
        try {
            configuracaoIntegracaoFogueteService.salvar(configDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações da integração com a Envio Acesso Pratique",
            description = "Salva as configurações da integração com a Envio Acesso Pratique.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoIntegracaoFogueteDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/envioAcessoPratique")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoEnvioAcessoPratique(@RequestBody ConfiguracaoIntegracaoEnvioAcessoPratiqueDTO configDTO) {
        try {
            configuracaoIntegracaoEnvioAcessoPratiqueService.salvar(configDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Salvar configurações da integração com o ManyChat",
            description = "Salva as configurações da integração com o ManyChat.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para salvar as configurações da integração.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = List.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body",
                                    value = EnvelopeRespostaConfiguracaoIntegracaoManyChatDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. A resposta não retorna corpo, apenas o status 200.")
                                    }
                            )
                    )
            }
    )
    @PostMapping("/manychat")
    public ResponseEntity<EnvelopeRespostaDTO> salvarConfiguracaoIntegracaoManyChat(@RequestBody ConfiguracaoIntegracaoManyChatDTO configDTO) {
        try {
            integracoesService.salvarConfiguracaoIntegracaoManyChat(configDTO);
            return ResponseEntityFactory.ok();
        } catch (RuntimeException e) {
            return ResponseEntityFactory.mensagemFront("ERRO", e.getMessage());
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar Tags do ManyChat",
            description = "Consulta as Tags do ManyChat.",
            parameters = {
                    @Parameter(name = "empresa", required = true, description = "Código da empresa que será usada para buscar as Tags do ManyChat.", example = "5")
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListTagDTO.class),
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200.",
                                                    value = EnvelopeRespostaListTagDTO.resposta
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/manychat/tags")
    public ResponseEntity<EnvelopeRespostaDTO> consultarTagsManyChat(@RequestParam(value = "empresa") Integer empresa) {
        try {
            return ResponseEntityFactory.ok(integracoesService.consultarTagsManyChat(empresa));
        } catch (RuntimeException e) {
            return ResponseEntityFactory.mensagemFront("ERRO", e.getMessage());
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

}
