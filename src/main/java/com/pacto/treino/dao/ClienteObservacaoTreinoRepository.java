package com.pacto.treino.dao;

import com.pacto.treino.entities.ClienteObservacaoTreino;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ClienteObservacaoTreinoRepository extends PagingAndSortingRepository<ClienteObservacaoTreino, Integer> {

    public List<ClienteObservacaoTreino> findAllByCliente(Integer cliente);

}
