registro.com.relacionamento=Este registro n\u00E3o pode ser exclu\u00EDdo pois possui relacionamento com {0}.


contrato.operacao.atestado.estorno.venda.credito=N\u00E3o \u00E9 poss\u00EDvel estornar opera\u00E7\u00E3o de planos que s\u00E3o de cr\u00E9dito, pois houveram aulas desmarcadas automaticamente oriundas desta opera\u00E7\u00E3o e ela n\u00E3o pode ser desfeita.
contrato.operacao.atestado.dados.nao.encontrados=Dados n\u00E3o encontrados(ContratoOperacao)

atestado.aptidao.fisica.produto.nao.informado=O campo "Produto" deve ser informado
atestado.aptidao.fisica.data.inicio.nao.informado=O campo "Data de inicio" deve ser informado

contrato.operacao.lancar.atestado.antes.retorno.trancamento="N\u00E3o \u00E9 poss\u00EDvel lan\u00E7ar outra opera\u00E7\u00E3o de contrato antes do aluno retornar do trancamento."

contrato.operacao.atestado.periodo.atestado.nao.informado=O per\u00EDodo do atestado deve ser informado
contrato.operacao.atestado.justificativa.nao.informada=O campo Justificativa deve ser informado
contrato.operacao.atestado.data.inicio.maior.que.data.inicio.contrato=A data de in\u00CDcio do(a) {0} deve ser maior que a data de in\u00CDcio do contrato

comprovante-vacina-data-nao-informada=O campo "Data da aplica\u00E7\u00E3o" deve ser informado
comprovante-vacina-fabricante-nao-informada=O campo "Fabricante" deve ser informado"

sorteio.nenhum.cliente.encontrato=Nenhum cliente foi encontrato para realizar o sorteio! verifique as configura\u00E7\u00F5es!

integracao.empresa.nao.informada=Empresa n\u00E3o informada.

integracoes.codigoempresa.nao.informado=C\u00F3digo empresa n\u00E3o informado.
integracoes.modulo.nao.informado=M\u00F3dulo n\u00E3o informado.

integracao.mywellness.facility.url.nao.informada=O campo "Facility URL" deve ser informado.
integracao.mywellness.apikey.nao.informada=O campo "ApiKey" deve ser informado.
integracao.mywellness.numero.dias.vigencia.nao.informada=O campo "N\u00FAmero de dias de vig\u00EAncia associa\u00E7\u00E3o Gympass" deve ser informado"
integracao.falha.salvar.integracao=Falha ao tentar salvar integra\u00E7\u00E3o.

integracao.mentor.web.url.nao.informada=O campo "Url" deve ser informado.
integracao.mentor.web.nome.servico.nao.informado=O campo "Nome do servi\u00E7o" deve ser informado.
integracao.mentor.web.usuario.nao.informado=O campo "Usu\u00E1rio" dever ser informado.
integracao.mentor.web.senha.nao.informada=O campo "Senha" deve ser informado.

integracao.estacionamento.ftp.host.nao.informado=O campo "FTP Server" deve ser informado.
integracao.estacionamento.ftp.porta.nao.informada=O campo "FTP porta" deve ser informado.
integracao.estacionamento.ftp.user.nao.informado=O campo "Usu\u00E1rio" deve ser informado.
integracao.estacionamento.ftp.pass.nao.informado=O campo "Senha" deve ser informado.
integracao.estacionamento.nome.arquivo.nao.informado=O campo "Endere\u00E7o e nome do arquivo..." deve ser informado.

integracao.cdlspc.codigoassociado.nao.informado=O campo "C\u00F3digo Associado SPC" deve ser informado
integracao.cdlspc.operador.nao.informado=O campo "Operador SPC" deve ser informado
integracao.cdlspc.senha.nao.informada=O campo "Senha SPC" deve ser informado

integracao.vitio.linkcheckout.nao.informado=O campo "Link checkout Vitio" deve ser informado
integracao.vitio.linkebook.nao.informado=O campo "Link Ebook Vitio" deve ser informado
integracao.vitio.mensagemwhatsapp.nao.informado=O campo "Mensagem Vitio Whatsapp" deve ser informado
integracao.vitio.mensagemquercomprar.nao.informado=O campo "Mensagem quer comprar" deve ser informado

integracao.delsoft.usuario.nao.informado=O campo "Usu\u00E1rio" deve ser informado.
integracao.delsoft.senha.nao.informada=O campo "Senha" deve ser informado.
integracao.delsoft.plano.nao.informado=O campo "Plano" deve ser informado.
integracao.delsoft.host.nao.informado=O campo "Host" deve ser informado.
integracao.delsoft.porta.nao.informado=O campo "Porta" deve ser informado.
integracao.delsoft.nomeaplicacao.nao.informado=O campo "Nome Aplica\u00E7\u00E3o" deve ser informado.
integracao.delsoft.token.nao.informado=O campo "Token" deve ser informado.

integracao.notificacao.web.hook.url.nao.informada=O campo "URL Webhook" deve ser informado.

integracao.amigo.fit.usuario.nao.informado=O campo "Usu\u00E1rio" deve ser informado.
integracao.amigo.fit.senha.nao.informada=O campo "Senha" deve ser informado.

integracao.buzzlead.token.nao.informado=O campo "Token privado Buzzlead" deve ser informado.
integracao.buzzlead.responsavelpadrao.nao.informado=O campo "Respons\u00E1vel padr\u00E3o" deve ser informado.
integracao.buzzlead.horalimite.nao.informado=O campo "Hora limite" deve ser informado.

integracao.rdstation.responsavel.nao.informado=O campo "Respons\u00E1vel padr\u00E3o" deve ser informado.
integracao.rdstation.hora.limite.nao.informada=O campo "Hora limite" deve ser informado.
integracao.rdstation.acao.objecao.nao.informado=O campo "Tipo obje\u00E7\u00E3o para atualizar o Lead" deve ser informado.
integracao.rdstation.gatilho.nao.informado=O campo "Gatilho" deve ser informado.
integracao.rdstation.limite.requisicoes.atingido=Limite de requisi\u00E7\u00F5es atingido. Tente novamente em 5 minutos.
integracao.rdstation.empresa.nao.informada=Empresa n\u00E3o informada.

integracao.hubspot.empresa.nao.informada=Empresa n\u00E3o informada.
integracao.hubspot.responsavel.nao.informado=O campo "Respons\u00E1vel padr\u00E3o" deve ser informado.
integracao.hubspot.hora.limite.nao.informada=O campo "Hora limite" deve ser informado.
integracao.hubspot.acao.objecao.nao.informado=O campo "Tipo obje\u00E7\u00E3o para atualizar o Lead" deve ser informado.
integracao.hubspot.clienteid.nao.informado=O campo "ClientId" deve ser informado.
integracao.hubspot.clientesecret.nao.informado=O campo "ClientSecret" deve ser informado.
integracao.hubspot.urlinstalacao.nao.informada=O campo "Url instalacoa" deve ser informado.

integracao.sms.tokensms.nao.informado=O campo "Token SMS" deve ser informado.
integracao.sms.tokensmsshortcode.nao.informado=O campo "Token SMS(ShortCode)" deve ser informado.

integracao.wordpress.empresa.nao.informada=Empresa n\u00E3o informada.
integracao.wordpress.responsavel.nao.informado=O campo "Respons\u00E1vel padr\u00E3o" deve ser informado.
integracao.wordpress.hora.limite.nao.informada=O campo "Hora limite" deve ser informado.
integracao.wordpress.acao.objecao.nao.informado=O campo "Tipo obje\u00E7\u00E3o para atualizar o Lead" deve ser informado.

integracao.join.empresa.nao.informada=Empresa n\u00E3o informada.
integracao.join.responsavel.nao.informado=O campo "Respons\u00E1vel padr\u00E3o" deve ser informado.
integracao.join.hora.limite.nao.informada=O campo "Hora limite" deve ser informado.
integracao.join.acao.objecao.nao.informado=O campo "Tipo obje\u00E7\u00E3o para atualizar o Lead" deve ser informado.

integracao.genericaleads.empresa.nao.informada=Empresa n\u00E3o informada.
integracao.genericaleads.responsavel.nao.informado=O campo "Respons\u00E1vel padr\u00E3o" deve ser informado.
integracao.genericaleads.hora.limite.nao.informada=O campo "Hora limite" deve ser informado.
integracao.genericaleads.acao.objecao.nao.informado=O campo "Tipo obje\u00E7\u00E3o para atualizar o Lead" deve ser informado.

integracao.f360.ftpserver.nao.informado=O campo "FTP Server" deve ser informado.
integracao.f360.ftpporta.nao.informado=O campo "FTP Porta" deve ser informado.
integracao.f360.user.nao.informado=O campo "Usu\u00E1rio" deve ser informado.
integracao.f360.password.nao.informado=O campo "Senha" deve ser informado.
integracao.f360.dir.nao.informado=O campo "FTP Diret\u00F3rio" deve ser informado.
integracao.f360.dir.informado.sem.files=O campo "FTP Diret\u00F3rio" deve conter o '/files' antes de tudo.

integracao.conciliadora.idempresa.nao.informada=O campo "ID Empresa" deve ser informado.
integracao.concliadora.senha.nao.informada=O campo "Senha" deve ser informado.
integracao.concliadora.codigoempresa.nao.informado=C\u00F3digo empresa n\u00E3o informado.
integracao.concliadora.codigorecibo.nao.informado=C\u00F3digo recibo n\u00E3o informado.
integracao.concliadora.datainicial.nao.informada=Data inicial n\u00E3o informada.
integracao.concliadora.datafinal.nao.informada=Data final n\u00E3o informada.

integracao.conciliadora.periodode.nao.informado=A data inicial deve ser informada.
integracao.conciliadora.periodoate.nao.informado=A data final deve ser informada.
integracao.conciliadora.data.inicial.maior=A data inicial deve ser inferior a data final.
integracao.conciliadora.periodo.limite.ultrapassado=O per\u00EDodo n\u00E3o pode ser superior a 31 dias.

integracao.spivi.sourcename.nao.informado=O campo "Source name" deve ser informado.
integracao.spivi.password.nao.informado=O campo "Password" deve ser informado.
integracao.spivi.siteid.nao.informado=O campo "Site ID" deve ser informado.

integracao.parceiro.fidelidade.clientid.nao.informado= O campo "Cliend id" deve ser informado.
integracao.parceiro.fidelidade.clientsecret.nao.informado=O campo "Client Secrect(Rewards)" deve ser informado.
integracao.parceiro.fidelidade.clientidredemption.nao.informado=O campo "Client ID (Redemption)" deve ser informado.
integracao.parceiro.fidelidade.clientsecretredemption.nao.informado=O campo "Cliente Secrect (Redemption)" deve ser informado.
integracao.parceiro.fidelidade.codigoloja.nao.informado=O campo "C\u00F3digo Loja (StoreCode)" deve ser informado.
integracao.parceiro.fidelidade.codigomaquina.nao.informado=O campo "C\u00F3digo M\u00E1quina (DeviceCode)" deve ser informado.
integracao.parceiro.fidelidade.codigooferta.nao.informado=O campo "C\u00F3digo Oferta (OfferCode)" deve ser informado.
integracao.parceiro.fidelidade.codigoresgate.nao.informado=O campo "C\u00F3digo Resgate (LocationId)" deve ser informado.

integracao.sistema.sesc.df.token.nao.inforamdo=O campo "Token" deve ser informado.

empresa.obter-por-id.invalid=Erro ao obter a empresa com 'id': {0}.

cliente.observacao.observacao.nao.informado=O campo "Observa\u00E7\u00E3o" deve ser informado.
cliente.observacao.usuario.nao.informado=O campo "Usu\u00E1rio" deve ser informado.
cliente.observacao.cliente.nao.informado=O campo "Cliente" deve ser informado.
cliente.mensagem.nao.encontrado=Dados n\u00E3o encontrados(ClienteMensagem)

integracao.foguete.empresa.nao.informada=Empresa n\u00E3o informada.
integracao.foguete.tokenapi.nao.informado=O campo "Token API" deve ser informado.
integracao.foguete.urlapi.nao.informado=O campo "URL API" deve ser informado.
integracao.foguete.produto.nao.informado=O campo "Produto" deve ser informado.

solicitacaocompra.nenhum.encontrado=Nenhuma solicita\u00E7\u00E3o de compra foi encontrada.
