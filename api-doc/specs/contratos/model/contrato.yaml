Contrato:
  description: DTO de plano.
  type: object
  properties:
    codigo:
      type: number
    situacao:
      type: string
    bolsa:
      type: boolean
    regimeRecorrencia:
      type: boolean
    dataLancamento:
      type: number
    vigenciaDe:
      type: number
    vigenciaAte:
      type: number
    vigenciaAteAjustada:
      type: number
    valorBaseCalculo:
      type: number
    valorFinal:
      type: number
    somaProduto:
      type: number
    valorConvenioDesconto:
      type: number
    situacaoContrato:
      type: string
    dataMatricula:
      type: number
    contratoResponsavelRenovacaoMatricula:
      type: number
    contratoResponsavelRematriculaMatricula:
      type: number
    dataPrevistaRenovar:
      type: number
    dataRenovarRealizada:
      type: number
    situacaoRenovacao:
      type: string
    contratoBaseadoRenovacao:
      type: number
    dataRematriculaRealizada:
      type: number
    dataPrevistaRematricula:
      type: number
    situacaoRematricula:
      type: string
    contratoBaseadoRematricula:
      type: number
    observacao:
      type: string
    nomeModalidades:
      type: string
