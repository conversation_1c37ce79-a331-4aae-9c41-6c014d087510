paths:
  '/contrato/{id}':
    get:
      tags:
        - Contrato
      operationId: Buscar um plano
      parameters:
        - in: path
          name: codigo
          description: código do plano.
      responses:
        '200':
          description: Sucesso
          content:
            "application/json":
              schema:
                $ref: './model/plano.yaml#/Contrato'

  '/contrato/{id}/contrato-duracao':
    get:
      tags:
        - Contrato
      operationId: Obter o plano duração
      parameters:
        - in: path
          name: codigo
          description: código do plano.
      responses:
        '200':
          description: Sucesso
          content:
            "application/json":
              schema:
                $ref: './model/plano-duracao.yaml#/Contrato Duração'
